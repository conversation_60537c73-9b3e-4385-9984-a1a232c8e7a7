Video Player :- https://www.kibo-ui.com/components/video-player
server status indiactor :- https://www.kibo-ui.com/components/status
pill design :- https://www.kibo-ui.com/components/pill
shortcut key design :- https://www.kibo-ui.com/components/kbd
announcement banner :- https://www.kibo-ui.com/components/banner
Alerts :- https://originui.com/alert
select tag :- https://www.kibo-ui.com/components/tags
upload file zone :- https://originui.com/file-upload
cursor :- https://www.kibo-ui.com/components/cursor

theme colors :- https://blocks.mvp-subha.me/docs/foundation/colors
buttons :- https://blocks.mvp-subha.me/docs/basic/buttons (3)
loading :- https://blocks.mvp-subha.me/docs/basic/loaders (2)
Modals :- https://blocks.mvp-subha.me/docs/basic/modals
pagination :- https://blocks.mvp-subha.me/docs/basic/pagination
login page :- https://blocks.mvp-subha.me/docs/forms/loginforms (2)

server row :- https://blocks.mvp-subha.me/docs/cards/card-flip

view file :- https://blocks.mvp-subha.me/docs/cards/code-blocks

login page design :- https://blocks.mvp-subha.me/docs/creative/globe (1)

tooltip:- https://blocks.mvp-subha.me/docs/creative/interactive-tooltip

Bento card :- https://blocks.mvp-subha.me/docs/grids/bento (1)

Some headers :- https://animate-ui.com/docs/text/writing

loading skeleton :- https://blocks.mvp-subha.me/docs/skeletons

file manager tree :- https://animate-ui.com/docs/components/files
accordion :- https://originui.com/accordion

bread crumb :- https://originui.com/breadcrumb

image cropper with zoom :- https://originui.com/image-cropper
dialog :- https://originui.com/dialog
dropdown :- https://originui.com/dropdown

notifications :- https://originui.com/notification
popovers:- https://originui.com/popover
select box :- https://originui.com/select
radio button:- https://originui.com/radio
slider :- https://reactbits.dev/components/elastic-slider

tabs :- https://originui.com/tabs


animations :- https://originui.com/easings

file editer :- https://animate-ui.com/docs/components/code-editor

server pin :- https://animate-ui.com/docs/components/pin-list

tabs (animation):- https://animate-ui.com/docs/radix/tabs

server buttons :- https://animate-ui.com/docs/ui-elements/management-bar

motion effect :- https://animate-ui.com/docs/effects/motion-effect

check box :- https://animate-ui.com/docs/radix/checkbox

progress bar :- https://animate-ui.com/docs/radix/progress
switch :- https://animate-ui.com/docs/radix/switch
toggle group :- https://animate-ui.com/docs/base/toggle-group

animated dialog:- https://animate-ui.com/docs/radix/dialog
sidebar :- https://animate-ui.com/docs/radix/sidebar

copy button :- https://animate-ui.com/docs/buttons/copy
icon button:- https://animate-ui.com/docs/buttons/icon

loading number :- https://animate-ui.com/docs/text/sliding-number

Star Background :- https://animate-ui.com/docs/backgrounds/stars
background :- https://reactbits.dev/backgrounds/dark-veil

Loading text:- https://reactbits.dev/text-animations/split-text

Shiny text :- https://reactbits.dev/text-animations/shiny-text

Hover Effect :- https://reactbits.dev/animations/glare-hover

button load animation :- https://reactbits.dev/animations/animated-content

click spark :- https://reactbits.dev/animations/click-spark

button magnet:- https://reactbits.dev/animations/magnet

Border animation :- https://reactbits.dev/animations/star-border
server list animation :- https://reactbits.dev/components/animated-list

hover card animation :- https://reactbits.dev/components/tilted-card

button click animation :- https://reactbits.dev/components/gooey-nav
spotlight card :- https://reactbits.dev/components/spotlight-card

dock :- https://magicui.design/docs/components/dock

shine border:- https://magicui.design/docs/components/shine-border

button :- https://magicui.design/docs/components/shimmer-button

hover button:- https://magicui.design/docs/components/interactive-hover-button

flickering grid background :- https://magicui.design/docs/components/flickering-grid

dot pattern :- https://magicui.design/docs/components/dot-pattern