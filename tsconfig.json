{"compilerOptions": {"target": "es2015", "module": "es2020", "jsx": "react", "moduleResolution": "node", "lib": ["es2015", "dom"], "strict": true, "noEmit": true, "sourceMap": true, "noImplicitReturns": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "importsNotUsedAsValues": "preserve", "paths": {"@/*": ["./resources/scripts/*"], "@resources/*": ["./resources/scripts/*"], "@definitions/*": ["./resources/scripts/api/definitions/*"], "@feature/*": ["./resources/scripts/components/server/features/*"], "@rolexdev/*": ["./rolexdev/*"], "@rolexdev/themes/hyperv1/*": ["rolexdev/themes/hyperv1/*"]}, "plugins": [{"name": "typescript-plugin-tw-template"}], "typeRoots": ["node_modules/@types"]}, "include": ["./resources/scripts/**/*"], "exclude": ["/node_modules/"]}