/routes/api-client.php
Please paste this lines to /routes/api-client.php above the Route::group(['prefix' => '/settings'], function () { line

Route::group(['prefix' => '/playermanager', 'middleware' => \Pterodactyl\Http\Middleware\Api\Client\Server\IsMinecraft::class], function () {
	Route::get('/', [Client\Servers\MinecraftPlayerManagerController::class, 'index']);
	Route::post('/command', [Client\Servers\MinecraftPlayerManagerController::class, 'runCommand']);
});




/app/Transformers/Api/Client/ServerTransformer.php
Please paste this line to /app/Transformers/Api/Client/ServerTransformer.php under the 'is_transferring' => !is_null($server->transfer) line

'nest_id' => $server->nest_id,
'egg_id' => $server->egg_id,




/app/Models/Permission.php
Please insert this line to /app/Models/Permission.php above the 'websocket' => [ line

'playermanager' => [
	'description' => 'Manage player manager.',
	'keys' => [
		'manage' => 'List, op, deop, kick, ban, whitelist players.',
	],
],




/resources/scripts/routers/routes.ts
Please paste this line to /resources/scripts/routers/routes.ts under the import ServerActivityLogContainer from '@/components/server/ServerActivityLogContainer'; line

import PlayerManagerContainer from '@/components/server/playermanager/PlayerManagerContainer';


Please paste this lines to /resources/scripts/routers/routes.ts under the permission: string | string[] | null; line

nestId?: number;
eggId?: number;
nestIds?: number[];
eggIds?: number[];


Please paste this lines to /resources/scripts/routers/routes.ts to the bottom of server array (help01.png)

{
	path: '/playermanager',
	permission: 'playermanager.*',
	name: 'Player Manager',
	component: PlayerManagerContainer,
	nestIds: [ 1 ],
},




/resources/scripts/api/server/getServer.ts
Please paste this line to /resources/scripts/api/server/getServer.ts under the allocations: Allocation[]; line

nestId: number;
eggId: number;


Please paste this line to /resources/scripts/api/server/getServer.ts under the allocations: ((data.relationships?.allocations as FractalResponseList | undefined)?.data || []).map(rawDataToServerAllocation), line

nestId: data.nest_id,
eggId: data.egg_id,




/resources/scripts/routers/ServerRouter.tsx
Please paste this lines to /resources/scripts/routers/ServerRouter.tsx under the import routes from '@/routers/routes'; line

import { Navigation, ComponentLoader } from '@/routers/ServerElements';


Please replace the marked codes in (help02.png) to this line (it should look like help04.png)

<Navigation />


Please replace the marked codes in (help03.png) to this line (it should look like help04.png)

<ComponentLoader />




After all code inserted to code and app and resources folder pasted. Please run this commands (node is required, min version: v10.x [node -v]):
- npm i -g yarn
- cd /var/www/pterodactyl
- yarn install
- yarn run build:production
- php artisan migrate
- php artisan optimize

# 1296N0IMTIFJ0ZM0W2IP