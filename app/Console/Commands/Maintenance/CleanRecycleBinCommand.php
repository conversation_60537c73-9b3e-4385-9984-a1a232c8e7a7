<?php

namespace Pterodactyl\Console\Commands\Maintenance;

use Exception;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Pterodactyl\Repositories\Wings\DaemonFileRepository;
use Pterodactyl\Models\Server;

class CleanRecycleBinCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'p:maintenance:clean-recycle-bin {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     */
    protected $description = 'Permanently delete files from recycle bin that are older than 7 days';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $dryRun = $this->option('dry-run');
        $cutoffDate = Carbon::now()->subDays(7);
        
        $this->info('Starting recycle bin cleanup...');
        $this->info('Cutoff date: ' . $cutoffDate->toDateTimeString());
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No files will actually be deleted');
        }

        $totalCleaned = 0;
        $totalErrors = 0;

        try {
            // Get all servers to check their recycle bins
            $servers = Server::all();
            
            foreach ($servers as $server) {
                $this->info("Processing server: {$server->name} (UUID: {$server->uuid})");
                
                $cleaned = $this->cleanServerRecycleBin($server, $cutoffDate, $dryRun);
                $totalCleaned += $cleaned['cleaned'];
                $totalErrors += $cleaned['errors'];
            }
            
            $this->info('Recycle bin cleanup completed successfully!');
            $this->info("Total files cleaned: {$totalCleaned}");
            
            if ($totalErrors > 0) {
                $this->warn("Total errors encountered: {$totalErrors}");
            }
            
            return self::SUCCESS;
            
        } catch (Exception $e) {
            $this->error('An error occurred during cleanup: ' . $e->getMessage());
            Log::error('Recycle bin cleanup failed', ['exception' => $e]);
            
            return self::FAILURE;
        }
    }

    /**
     * Clean recycle bin for a specific server
     */
    private function cleanServerRecycleBin(Server $server, Carbon $cutoffDate, bool $dryRun): array
    {
        $cleaned = 0;
        $errors = 0;

        try {
            // Get expired files from recycle bin table
            $expiredFiles = DB::table('server_recycle_bin')
                ->where('server_id', $server->id)
                ->where('expires_at', '<', $cutoffDate)
                ->get();

            if ($expiredFiles->isEmpty()) {
                $this->line("  No expired files found for server {$server->name}");
                return ['cleaned' => 0, 'errors' => 0];
            }

            $this->line("  Found {$expiredFiles->count()} expired files");

            $fileRepository = app(DaemonFileRepository::class);
            $fileRepository->setServer($server);

            foreach ($expiredFiles as $file) {
                try {
                    if ($dryRun) {
                        $this->line("  [DRY RUN] Would delete: {$file->name} (expired: {$file->expires_at})");
                    } else {
                        // Delete the actual file from storage - use relative path
                        $fileRepository->deleteFiles('/', ['recycle-bin/' . $file->recycle_bin_path]);
                        
                        // Remove from database
                        DB::table('server_recycle_bin')
                            ->where('id', $file->id)
                            ->delete();
                        
                        $this->line("  Deleted: {$file->name}");
                    }
                    
                    $cleaned++;
                    
                } catch (Exception $e) {
                    $this->error("  Failed to delete {$file->name}: " . $e->getMessage());
                    Log::warning('Failed to delete expired recycle bin file', [
                        'server_id' => $server->id,
                        'file_id' => $file->id,
                        'file_name' => $file->name,
                        'error' => $e->getMessage(),
                    ]);
                    $errors++;
                }
            }

        } catch (Exception $e) {
            $this->error("  Error processing server {$server->name}: " . $e->getMessage());
            Log::warning('Error processing server recycle bin', [
                'server_id' => $server->id,
                'error' => $e->getMessage(),
            ]);
            $errors++;
        }

        return ['cleaned' => $cleaned, 'errors' => $errors];
    }
}