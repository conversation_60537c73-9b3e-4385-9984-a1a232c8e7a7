<?php

namespace Pterodactyl\Transformers\Api\Client;

use Pterodactyl\Models\ServerRecycleBin;

class RecycleBinFileTransformer extends BaseClientTransformer
{
    /**
     * Return the resource name for the transformer.
     */
    public function getResourceName(): string
    {
        return 'recycled_file';
    }

    /**
     * Transform a ServerRecycleBin model into a representation for the client API.
     */
    public function transform(ServerRecycleBin $model): array
    {
        return [
            'id' => (string) $model->id,
            'name' => $model->name ?? 'Unknown',
            'original_path' => $model->original_path ?? '',
            'original_directory' => $model->original_directory ?? '/',
            'is_file' => (bool) $model->is_file,
            'size' => $model->is_file ? ($model->size ?? 0) : null,
            'modified_at' => $model->modified_at?->toISOString(),
            'deleted_at' => $model->deleted_at?->toISOString() ?? now()->toISOString(),
            'expires_at' => $model->expires_at?->toISOString() ?? now()->addDays(7)->toISOString(),
            'mimetype' => $model->mimetype,
            'extension' => $model->extension,
            'recycle_bin_path' => $model->recycle_bin_path ?? '',
            'is_expired' => $model->isExpired(),
            'is_expiring_soon' => $model->isExpiringSoon(),
            'formatted_size' => $model->getFormattedSizeAttribute(),
        ];
    }
}