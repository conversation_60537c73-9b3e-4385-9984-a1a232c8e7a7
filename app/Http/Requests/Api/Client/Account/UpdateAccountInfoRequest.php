<?php

namespace Pterodactyl\Http\Requests\Api\Client\Account;

use Illuminate\Container\Container;
use Illuminate\Contracts\Hashing\Hasher;
use Pterodactyl\Http\Requests\Api\Client\ClientApiRequest;
use Pterodactyl\Models\User;
use Pterodactyl\Exceptions\Http\Base\InvalidPasswordProvidedException;

class UpdateAccountInfoRequest extends ClientApiRequest
{
    public function authorize(): bool
    {
        if (!parent::authorize()) {
            return false;
        }

        $hasher = Container::getInstance()->make(Hasher::class);

        // Require password confirmation for any change.
        if (!$hasher->check($this->input('confirm_password'), $this->user()->password)) {
            throw new InvalidPasswordProvidedException(trans('validation.internal.invalid_password'));
        }

        return true;
    }

    public function rules(): array
    {
        $rules = User::getRulesForUpdate($this->user());
        return [
            'first_name' => $rules['name_first'],
            'last_name' => $rules['name_last'],
            'username' => $rules['username'],
            'email' => $rules['email'],
            'confirm_password' => ['required', 'string'],
        ];
    }
}