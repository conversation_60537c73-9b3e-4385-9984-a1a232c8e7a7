<?php

namespace Pterodactyl\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Pterodactyl\Rules\Username;

class RegisterRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'first_name' => 'required|string|between:1,191',
            'last_name' => 'required|string|between:1,191',
            'username' => [
                'required', 
                'string', 
                'between:1,191', 
                Rule::unique('users')->where(function ($query) {
                    return $query->whereRaw('LOWER(username) = ?', [strtolower($this->input('username'))]);
                }),
                new Username()
            ],
            'email' => 'required|email|between:1,191|unique:users,email',
            'password' => 'required|string|confirmed|min:8',
        ];
    }

    public function messages(): array
    {
        return [
            'first_name.required' => 'A first name is required.',
            'first_name.string' => 'First name must be a string.',
            'first_name.between' => 'First name must be between 1 and 191 characters.',
            
            'last_name.required' => 'A last name is required.',
            'last_name.string' => 'Last name must be a string.',
            'last_name.between' => 'Last name must be between 1 and 191 characters.',
            
            'username.required' => 'A username is required.',
            'username.string' => 'Username must be a string.',
            'username.between' => 'Username must be between 1 and 191 characters.',
            'username.unique' => 'This username is already taken.',
            
            'email.required' => 'An email address is required.',
            'email.email' => 'Please provide a valid email address.',
            'email.between' => 'Email must be between 1 and 191 characters.',
            'email.unique' => 'A user with this email address already exists.',
            
            'password.required' => 'A password is required.',
            'password.string' => 'Password must be a string.',
            'password.confirmed' => 'The password confirmation does not match.',
            'password.min' => 'Password must be at least 8 characters long.',
        ];
    }
}
