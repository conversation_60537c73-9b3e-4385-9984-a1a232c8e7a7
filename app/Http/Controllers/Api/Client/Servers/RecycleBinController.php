<?php

namespace Pterodactyl\Http\Controllers\Api\Client\Servers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Pterodactyl\Models\Server;
use Illuminate\Http\JsonResponse;
use Pterodactyl\Facades\Activity;
use Pterodactyl\Models\ServerRecycleBin;
use Illuminate\Database\Eloquent\Builder;
use Pterodactyl\Services\Nodes\NodeJWTService;
use Pterodactyl\Repositories\Wings\DaemonFileRepository;
use Pterodactyl\Http\Controllers\Api\Client\ClientApiController;
use Pterodactyl\Http\Requests\Api\Client\Servers\Files\DeleteFileRequest;
use Pterodactyl\Transformers\Api\Client\RecycleBinFileTransformer;

class RecycleBinController extends ClientApiController
{
    /**
     * RecycleBinController constructor.
     */
    public function __construct(
        private NodeJWTService $jwtService,
        private DaemonFileRepository $fileRepository
    ) {
        parent::__construct();
    }

    /**
     * Get list of files in recycle bin with filtering and pagination
     */
    public function index(Request $request, Server $server): array
    {
        $perPage = min($request->get('per_page', 50), 100);
        
        $query = ServerRecycleBin::where('server_id', $server->id);

        // Apply filters
        if ($request->filled('type') && $request->get('type') !== 'all') {
            $query->fileType($request->get('type'));
        }

        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function (Builder $q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('original_path', 'like', "%{$search}%");
            });
        }

        if ($request->boolean('expiring_soon')) {
            $query->expiringSoon();
        }

        // Apply sorting
        $sortBy = $request->get('sort', 'deleted_at');
        $direction = $request->get('direction', 'desc');
        
        $allowedSorts = ['name', 'deleted_at', 'expires_at', 'size', 'original_path'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $direction);
        }

        $files = $query->paginate($perPage);

        return $this->fractal->collection($files->items())
            ->transformWith(RecycleBinFileTransformer::class)
            ->withResourceName('recycled_files')
            ->paginateWith(new \League\Fractal\Pagination\IlluminatePaginatorAdapter($files))
            ->toArray();
    }

    /**
     * Get recycle bin statistics
     */
    public function stats(Request $request, Server $server): JsonResponse
    {
        $stats = [
            'total_items' => ServerRecycleBin::where('server_id', $server->id)->count(),
            'total_size' => ServerRecycleBin::where('server_id', $server->id)->sum('size') ?: 0,
            'expiring_today' => ServerRecycleBin::where('server_id', $server->id)
                ->where('expires_at', '<', Carbon::now()->addDay())
                ->where('expires_at', '>', Carbon::now())
                ->count(),
            'expiring_this_week' => ServerRecycleBin::where('server_id', $server->id)
                ->where('expires_at', '<', Carbon::now()->addWeek())
                ->where('expires_at', '>', Carbon::now())
                ->count(),
        ];

        return new JsonResponse(['data' => $stats]);
    }

    /**
     * Move files to recycle bin
     */
    public function store(DeleteFileRequest $request, Server $server): JsonResponse
    {
        $root = $request->input('root');
        $files = $request->input('files');

        // Get file information before moving to recycle bin
        $this->fileRepository->setServer($server);
        
        try {
            $fileList = $this->fileRepository->getDirectory($root);
        } catch (\Exception $e) {
            throw new \Exception('Failed to get file information: ' . $e->getMessage());
        }

        $movedFiles = [];
        $failedFiles = [];
        
        foreach ($files as $fileName) {
            $fileInfo = collect($fileList)->firstWhere('name', $fileName);
            
            if (!$fileInfo) {
                $failedFiles[] = [
                    'name' => $fileName,
                    'error' => 'File not found in directory'
                ];
                continue;
            }

            try {
                // Build proper file path
                $filePath = $root === '/' ? '/' . $fileName : $root . '/' . $fileName;

                // Generate unique path in recycle bin
                $recycleBinPath = ServerRecycleBin::generateRecycleBinPath(
                    $filePath, 
                    $server->id
                );

                // Move file to recycle bin directory on the server
                // Use absolute paths to ensure we move to root /recycle-bin directory
                $this->fileRepository->renameFiles('/', [
                    [
                        'from' => ltrim($filePath, '/'),
                        'to' => 'recycle-bin/' . $recycleBinPath
                    ]
                ]);

                // Create database record
                $recycleBinEntry = ServerRecycleBin::create([
                    'server_id' => $server->id,
                    'name' => $fileName,
                    'original_path' => $filePath,
                    'original_directory' => $root,
                    'is_file' => $fileInfo['file'],
                    'size' => $fileInfo['file'] ? $fileInfo['size'] : null,
                    'modified_at' => isset($fileInfo['modified']) ? Carbon::parse($fileInfo['modified']) : null,
                    'deleted_at' => Carbon::now(),
                    'expires_at' => Carbon::now()->addDays(7),
                    'mimetype' => $fileInfo['mimetype'] ?? null,
                    'extension' => pathinfo($fileName, PATHINFO_EXTENSION) ?: null,
                    'recycle_bin_path' => $recycleBinPath,
                ]);

                $movedFiles[] = $recycleBinEntry;

            } catch (\Exception $e) {
                $failedFiles[] = [
                    'name' => $fileName,
                    'error' => $e->getMessage()
                ];
            }
        }

        Activity::event('server:file.recycle')
            ->property('directory', $root)
            ->property('files', $files)
            ->property('moved_count', count($movedFiles))
            ->property('failed_count', count($failedFiles))
            ->log();

        $response = ['message' => 'Files processed for recycle bin'];
        
        if (count($failedFiles) > 0) {
            $response['failed_files'] = $failedFiles;
            $response['moved_files'] = count($movedFiles);
        }

        return new JsonResponse($response, Response::HTTP_OK);
    }

    /**
     * Restore a single file from recycle bin
     */
    public function restore(Request $request, Server $server): JsonResponse
    {
        $request->validate([
            'file_id' => 'required|string',
            'custom_path' => 'sometimes|string',
            'overwrite' => 'sometimes|boolean',
        ]);

        $recycleBinFile = ServerRecycleBin::where('server_id', $server->id)
            ->where('id', $request->input('file_id'))
            ->firstOrFail();

        $targetPath = $request->input('custom_path', $recycleBinFile->original_directory);
        $overwrite = $request->boolean('overwrite');

        $this->fileRepository->setServer($server);

        try {
            // Check if target file exists
            if (!$overwrite) {
                try {
                    $existingFiles = $this->fileRepository->getDirectory($targetPath);
                    $fileExists = collect($existingFiles)->contains('name', $recycleBinFile->name);
                    
                    if ($fileExists) {
                        return new JsonResponse([
                            'success' => false,
                            'message' => 'File already exists at target location'
                        ], 409);
                    }
                } catch (\Exception $e) {
                    // Directory might not exist, try to create it
                    if ($targetPath !== '/') {
                        try {
                            // Split the path to create nested directories if needed
                            $pathParts = array_filter(explode('/', $targetPath));
                            $currentPath = '';
                            
                            foreach ($pathParts as $part) {
                                $currentPath .= '/' . $part;
                                try {
                                    $this->fileRepository->getDirectory($currentPath);
                                } catch (\Exception $dirException) {
                                    $parentPath = dirname($currentPath);
                                    if ($parentPath === '.') $parentPath = '/';
                                    $this->fileRepository->createDirectory($part, $parentPath);
                                }
                            }
                        } catch (\Exception $createException) {
                            \Log::warning('Failed to create target directory', [
                                'path' => $targetPath,
                                'error' => $createException->getMessage()
                            ]);
                        }
                    }
                }
            }

            // Move file back from recycle bin
            $targetFullPath = $targetPath === '/' ? $recycleBinFile->name : ltrim($targetPath, '/') . '/' . $recycleBinFile->name;
            $sourceFullPath = 'recycle-bin/' . $recycleBinFile->recycle_bin_path;
            
            // Log the paths for debugging
            \Log::info('Restoring file', [
                'file_id' => $recycleBinFile->id,
                'file_name' => $recycleBinFile->name,
                'source_path' => $sourceFullPath,
                'target_path' => $targetFullPath,
                'original_directory' => $recycleBinFile->original_directory,
                'recycle_bin_path' => $recycleBinFile->recycle_bin_path
            ]);
            
            $this->fileRepository->renameFiles('/', [
                [
                    'from' => $sourceFullPath,
                    'to' => $targetFullPath
                ]
            ]);

            // Remove from recycle bin database
            $recycleBinFile->delete();

            Activity::event('server:file.restore')
                ->property('file', $recycleBinFile->name)
                ->property('from_recycle_bin', true)
                ->property('target_path', $targetPath)
                ->log();

            return new JsonResponse([
                'success' => true,
                'restoredFiles' => [$recycleBinFile->name],
                'failedFiles' => []
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Failed to restore file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Restore multiple files from recycle bin
     */
    public function restoreMultiple(Request $request, Server $server): JsonResponse
    {
        $request->validate([
            'file_ids' => 'required|array',
            'file_ids.*' => 'string',
            'overwrite' => 'sometimes|boolean',
        ]);

        $fileIds = $request->input('file_ids');
        $overwrite = $request->boolean('overwrite');

        $recycleBinFiles = ServerRecycleBin::where('server_id', $server->id)
            ->whereIn('id', $fileIds)
            ->get();

        $this->fileRepository->setServer($server);

        $restored = [];
        $failed = [];

        foreach ($recycleBinFiles as $file) {
            try {
                // Check if target file exists
                if (!$overwrite) {
                    try {
                        $existingFiles = $this->fileRepository->getDirectory($file->original_directory);
                        $fileExists = collect($existingFiles)->contains('name', $file->name);
                        
                        if ($fileExists) {
                            $failed[] = [
                                'name' => $file->name,
                                'error' => 'File already exists at target location'
                            ];
                            continue;
                        }
                    } catch (\Exception $e) {
                        // Directory might not exist, try to create it
                        if ($file->original_directory !== '/') {
                            try {
                                // Split the path to create nested directories if needed
                                $pathParts = array_filter(explode('/', $file->original_directory));
                                $currentPath = '';
                                
                                foreach ($pathParts as $part) {
                                    $currentPath .= '/' . $part;
                                    try {
                                        $this->fileRepository->getDirectory($currentPath);
                                    } catch (\Exception $dirException) {
                                        $parentPath = dirname($currentPath);
                                        if ($parentPath === '.') $parentPath = '/';
                                        $this->fileRepository->createDirectory($part, $parentPath);
                                    }
                                }
                            } catch (\Exception $createException) {
                                \Log::warning('Failed to create target directory for multiple restore', [
                                    'path' => $file->original_directory,
                                    'file' => $file->name,
                                    'error' => $createException->getMessage()
                                ]);
                            }
                        }
                    }
                }

                // Move file back from recycle bin
                $targetFullPath = $file->original_directory === '/' ? $file->name : ltrim($file->original_directory, '/') . '/' . $file->name;
                $sourceFullPath = 'recycle-bin/' . $file->recycle_bin_path;
                
                $this->fileRepository->renameFiles('/', [
                    [
                        'from' => $sourceFullPath,
                        'to' => $targetFullPath
                    ]
                ]);

                // Remove from recycle bin database
                $file->delete();
                $restored[] = $file->name;

            } catch (\Exception $e) {
                $failed[] = [
                    'name' => $file->name,
                    'error' => $e->getMessage()
                ];
            }
        }

        Activity::event('server:file.restore.multiple')
            ->property('restored_files', $restored)
            ->property('failed_files', $failed)
            ->log();

        return new JsonResponse([
            'success' => count($failed) === 0,
            'restoredFiles' => $restored,
            'failedFiles' => $failed
        ]);
    }

    /**
     * Permanently delete files from recycle bin
     */
    public function permanentDelete(Request $request, Server $server): JsonResponse
    {
        $request->validate([
            'file_ids' => 'required|array',
            'file_ids.*' => 'string',
        ]);

        $fileIds = $request->input('file_ids');

        \Log::info('Permanent delete request', [
            'server_id' => $server->id,
            'file_ids' => $fileIds
        ]);

        $recycleBinFiles = ServerRecycleBin::where('server_id', $server->id)
            ->whereIn('id', $fileIds)
            ->get();

        \Log::info('Found recycled files for deletion', [
            'count' => $recycleBinFiles->count(),
            'files' => $recycleBinFiles->pluck('name')->toArray()
        ]);

        $this->fileRepository->setServer($server);

        $deleted = [];
        $failed = [];

        foreach ($recycleBinFiles as $file) {
            try {
                // Delete file from storage - use relative path for deleteFiles
                $sourceFullPath = 'recycle-bin/' . $file->recycle_bin_path;
                $this->fileRepository->deleteFiles('/', [$sourceFullPath]);

                // Remove from database
                $file->delete();
                $deleted[] = $file->name;

            } catch (\Exception $e) {
                $failed[] = [
                    'name' => $file->name,
                    'error' => $e->getMessage()
                ];
            }
        }

        Activity::event('server:file.permanent_delete')
            ->property('deleted_files', $deleted)
            ->property('failed_files', $failed)
            ->log();

        return new JsonResponse([
            'success' => count($failed) === 0,
            'deletedFiles' => $deleted,
            'failedFiles' => $failed
        ]);
    }

    /**
     * Empty entire recycle bin
     */
    public function empty(Request $request, Server $server): JsonResponse
    {
        $request->validate([
            'confirmed' => 'required|boolean',
        ]);

        if (!$request->boolean('confirmed')) {
            return new JsonResponse(['message' => 'Confirmation required'], 400);
        }

        $recycleBinFiles = ServerRecycleBin::where('server_id', $server->id)->get();

        $this->fileRepository->setServer($server);

        $deleted = [];
        $failed = [];

        foreach ($recycleBinFiles as $file) {
            try {
                // Delete file from storage - use relative path for deleteFiles
                $sourceFullPath = 'recycle-bin/' . $file->recycle_bin_path;
                $this->fileRepository->deleteFiles('/', [$sourceFullPath]);

                // Remove from database
                $file->delete();
                $deleted[] = $file->name;

            } catch (\Exception $e) {
                $failed[] = [
                    'name' => $file->name,
                    'error' => $e->getMessage()
                ];
            }
        }

        Activity::event('server:file.recycle_bin.empty')
            ->property('deleted_files', $deleted)
            ->property('failed_files', $failed)
            ->log();

        return new JsonResponse([
            'success' => count($failed) === 0,
            'deletedFiles' => $deleted,
            'failedFiles' => $failed
        ]);
    }

    /**
     * Get details of a specific recycled file
     */
    public function show(Request $request, Server $server, string $fileId): array
    {
        $recycleBinFile = ServerRecycleBin::where('server_id', $server->id)
            ->where('id', $fileId)
            ->firstOrFail();

        return $this->fractal->item($recycleBinFile)
            ->transformWith(RecycleBinFileTransformer::class)
            ->toArray();
    }

    /**
     * Preview a recycled file (if it's a text file)
     */
    public function preview(Request $request, Server $server, string $fileId): Response
    {
        $recycleBinFile = ServerRecycleBin::where('server_id', $server->id)
            ->where('id', $fileId)
            ->firstOrFail();

        if (!$recycleBinFile->is_file) {
            abort(400, 'Cannot preview directory');
        }

        $this->fileRepository->setServer($server);

        try {
            $content = $this->fileRepository->getContent('/recycle-bin/' . $recycleBinFile->recycle_bin_path);
            
            return new Response($content, 200, [
                'Content-Type' => 'text/plain',
            ]);
        } catch (\Exception $e) {
            abort(500, 'Failed to load file preview: ' . $e->getMessage());
        }
    }

    /**
     * Download a recycled file
     */
    public function download(Request $request, Server $server, string $fileId): JsonResponse
    {
        $recycleBinFile = ServerRecycleBin::where('server_id', $server->id)
            ->where('id', $fileId)
            ->firstOrFail();

        if (!$recycleBinFile->is_file) {
            abort(400, 'Cannot download directory');
        }

        $this->fileRepository->setServer($server);

        try {
            $downloadToken = $this->jwtService->handle($server->node, $server->uuid, [
                'file_read',
                'file_download'
            ]);

            $downloadUrl = sprintf(
                '%s/download/file?file=%s&token=%s',
                $server->node->getConnectionAddress(),
                rawurlencode('/recycle-bin/' . $recycleBinFile->recycle_bin_path),
                $downloadToken->toString()
            );

            return new JsonResponse(['download_url' => $downloadUrl]);
        } catch (\Exception $e) {
            abort(500, 'Failed to generate download URL: ' . $e->getMessage());
        }
    }
}