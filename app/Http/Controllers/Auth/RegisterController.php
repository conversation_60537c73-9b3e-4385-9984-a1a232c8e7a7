<?php

namespace <PERSON>terodactyl\Http\Controllers\Auth;

use <PERSON>\Uuid\Uuid;
use Pterodactyl\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Pterodactyl\Facades\Activity;
use Illuminate\Contracts\View\View;
use Illuminate\Contracts\Hashing\Hasher;
use Illuminate\Database\ConnectionInterface;
use Illuminate\Contracts\View\Factory as ViewFactory;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Pterodactyl\Http\Requests\Auth\RegisterRequest;
use Pterodactyl\Services\Users\UserCreationService;

class RegisterController extends AbstractLoginController
{
    /**
     * RegisterController constructor.
     */
    public function __construct(
        private ViewFactory $view,
        private UserCreationService $userCreationService
    ) {
        parent::__construct();
    }

    /**
     * Handle all incoming requests for the registration routes and render the
     * base authentication view component. React will take over at this point and
     * turn the register area into an SPA.
     */
    public function index(): View
    {
        return $this->view->make('templates/auth.core');
    }

    /**
     * Handle a registration request to the application.
     *
     * @throws \Pterodactyl\Exceptions\DisplayException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function register(RegisterRequest $request): JsonResponse
    {
        if ($this->hasTooManyLoginAttempts($request)) {
            $this->fireLockoutEvent($request);
            $this->sendLockoutResponse($request);
        }

        try {
            // Check if user already exists (case-insensitive for username)
            $existingUser = User::query()
                ->where('email', $request->input('email'))
                ->orWhereRaw('LOWER(username) = ?', [strtolower($request->input('username'))])
                ->first();

            if ($existingUser) {
                if ($existingUser->email === $request->input('email')) {
                    return new JsonResponse([
                        'errors' => [
                            'email' => ['A user with this email address already exists.']
                        ]
                    ], 422);
                }
                
                if (strtolower($existingUser->username) === strtolower($request->input('username'))) {
                    return new JsonResponse([
                        'errors' => [
                            'username' => ['This username is already taken.']
                        ]
                    ], 422);
                }
            }

            // Create the user
            $user = $this->userCreationService->handle([
                'uuid' => Uuid::uuid4()->toString(),
                'email' => $request->input('email'),
                'username' => $request->input('username'),
                'name_first' => $request->input('first_name'),
                'name_last' => $request->input('last_name'),
                'password' => $request->input('password'),
                'root_admin' => false,
                'language' => 'en',
            ]);

            Activity::event('auth:registered')->withRequestMetadata()->subject($user)->log();

            // Log the user in immediately after registration
            $this->auth->guard()->login($user, true);

            return new JsonResponse([
                'data' => [
                    'complete' => true,
                    'intended' => $this->redirectPath(),
                    'user' => $user->toVueObject(),
                ],
            ]);

        } catch (\Exception $exception) {
            Activity::event('auth:register.fail')
                ->withRequestMetadata()
                ->property('exception', $exception->getMessage())
                ->log();

            $this->incrementLoginAttempts($request);

            return new JsonResponse([
                'errors' => [
                    'general' => ['An error occurred while creating your account. Please try again.']
                ]
            ], 500);
        }
    }
}
