<?php

namespace Pterodactyl\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Pterodactyl\Http\Controllers\Controller;
use Pterodactyl\Models\HyperV1ThemeSettings;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class ThemeSettingsController extends Controller
{
    /**
     * Get the theme settings.
     */
    public function index(): JsonResponse
    {
        $settings = HyperV1ThemeSettings::getInstance();
        $settingsArray = $settings->toArray();
        
        // Merge with color defaults to ensure all colors are present
        $colorValues = $settings->getColorValues();
        $settingsArray = array_merge($settingsArray, $colorValues);
        
        return response()->json([
            'object' => 'theme_settings',
            'attributes' => $settingsArray,
        ]);
    }

    /**
     * Update the theme settings.
     */
    public function update(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'large_logo_url' => 'nullable|string|max:500',
            'large_logo_width' => 'integer|min:10|max:1000',
            'large_logo_height' => 'integer|min:10|max:300',
            'large_logo_enabled' => 'boolean',
            'small_logo_url' => 'nullable|string|max:500',
            'small_logo_width' => 'integer|min:10|max:200',
            'small_logo_height' => 'integer|min:10|max:200',
            'small_logo_enabled' => 'boolean',
            'discord_url' => 'nullable|string|max:500',
            'knowledge_base_url' => 'nullable|string|max:500',
            'support_url' => 'nullable|string|max:500',
            'billing_url' => 'nullable|string|max:500',
            'primary_color' => 'nullable|string|max:25',
            'primary_hover_color' => 'nullable|string|max:25',
            'secondary_color' => 'nullable|string|max:25',
            'accent_color' => 'nullable|string|max:25',
            'background_color' => 'nullable|string|max:25',
            'card_color' => 'nullable|string|max:25',
            'muted_color' => 'nullable|string|max:25',
            'destructive_color' => 'nullable|string|max:25',
            'sidebar_color' => 'nullable|string|max:25',
            'glass_color' => 'nullable|string|max:25',
            'tooltip_color' => 'nullable|string|max:25',
            'text_primary_color' => 'nullable|string|max:25',
            'text_secondary_color' => 'nullable|string|max:25',
            'text_foreground_color' => 'nullable|string|max:25',
            'text_muted_color' => 'nullable|string|max:25',
            'text_destructive_color' => 'nullable|string|max:25',
            'destructive_text_color' => 'nullable|string|max:25',
            'dark_mode' => 'boolean',
            'sidebar_collapsed' => 'boolean',
            'animations' => 'boolean',
            'custom_css' => 'nullable|string|max:50000',
        ]);

        // Additional validation for URL fields to accept both full URLs and relative paths
        $urlFields = ['large_logo_url', 'small_logo_url', 'discord_url', 'knowledge_base_url', 'support_url', 'billing_url'];
        foreach ($urlFields as $field) {
            if (isset($validated[$field]) && $validated[$field] !== null) {
                $value = $validated[$field];
                // Check if it's a relative path (starts with /) or a valid URL
                if (!str_starts_with($value, '/') && !filter_var($value, FILTER_VALIDATE_URL)) {
                    return response()->json([
                        'message' => 'The given data was invalid.',
                        'errors' => [
                            $field => ['The ' . str_replace('_', ' ', $field) . ' must be a valid URL or path starting with /.']
                        ]
                    ], 422);
                }
            }
        }

        $settings = HyperV1ThemeSettings::getInstance();
        $settings->update($validated);

        return response()->json([
            'object' => 'theme_settings',
            'attributes' => $settings->fresh()->toArray(),
        ]);
    }

    /**
     * Reset theme settings to defaults.
     */
    public function reset(): JsonResponse
    {
        $settings = HyperV1ThemeSettings::getInstance();
        
        $settings->update([
            'large_logo_url' => '/logo/large.png',
            'large_logo_width' => 120,
            'large_logo_height' => 24,
            'large_logo_enabled' => true,
            'small_logo_url' => '/logo/small.png',
            'small_logo_width' => 24,
            'small_logo_height' => 24,
            'small_logo_enabled' => true,
            'discord_url' => null,
            'knowledge_base_url' => null,
            'support_url' => null,
            'billing_url' => null,
            'primary_color' => '#df3050',
            'primary_hover_color' => '#e44b63',
            'secondary_color' => '#27272A',
            'accent_color' => '#292524',
            'background_color' => '#0C0A09',
            'card_color' => 'rgba(28, 25, 23, 0.48)',
            'muted_color' => '#262626',
            'destructive_color' => '#7F1D1D',
            'sidebar_color' => 'rgba(25, 25, 25, 0.86)',
            'glass_color' => 'rgba(135, 135, 135, 0.27)',
            'tooltip_color' => '#1f2937',
            'text_primary_color' => '#ffffff',
            'text_secondary_color' => '#FAFAFA',
            'text_foreground_color' => '#F2F2F2',
            'text_muted_color' => '#A1A1AA',
            'text_destructive_color' => '#FEF2F2',
            'destructive_text_color' => '#ff0000',
            'dark_mode' => true,
            'sidebar_collapsed' => false,
            'animations' => true,
            'custom_css' => null,
        ]);

        return response()->json([
            'object' => 'theme_settings',
            'attributes' => $settings->fresh()->toArray(),
        ]);
    }
}
