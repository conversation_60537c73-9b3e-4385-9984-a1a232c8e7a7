<?php

namespace Pterodactyl\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HyperV1ThemeSettings extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'hyperv1_theme_settings';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'large_logo_url',
        'large_logo_width',
        'large_logo_height',
        'large_logo_enabled',
        'small_logo_url',
        'small_logo_width',
        'small_logo_height',
        'small_logo_enabled',
        'discord_url',
        'knowledge_base_url',
        'support_url',
        'billing_url',
        'primary_color',
        'primary_hover_color',
        'secondary_color',
        'accent_color',
        'background_color',
        'card_color',
        'muted_color',
        'destructive_color',
        'sidebar_color',
        'glass_color',
        'tooltip_color',
        'text_primary_color',
        'text_secondary_color',
        'text_foreground_color',
        'text_muted_color',
        'text_destructive_color',
        'destructive_text_color',
        'dark_mode',
        'sidebar_collapsed',
        'animations',
        'custom_css',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'large_logo_enabled' => 'boolean',
        'small_logo_enabled' => 'boolean',
        'dark_mode' => 'boolean',
        'sidebar_collapsed' => 'boolean',
        'animations' => 'boolean',
        'large_logo_width' => 'integer',
        'large_logo_height' => 'integer',
        'small_logo_width' => 'integer',
        'small_logo_height' => 'integer',
    ];

    /**
     * Get the theme settings instance. Creates one if it doesn't exist.
     */
    public static function getInstance(): self
    {
        $settings = static::first();
        
        if (!$settings) {
            $settings = static::create([
                'large_logo_url' => '/logo/large.png',
                'large_logo_width' => 120,
                'large_logo_height' => 24,
                'large_logo_enabled' => true,
                'small_logo_url' => '/logo/small.png',
                'small_logo_width' => 24,
                'small_logo_height' => 24,
                'small_logo_enabled' => true,
                'discord_url' => null,
                'knowledge_base_url' => null,
                'support_url' => null,
                'billing_url' => null,
                'primary_color' => '#df3050',
                'primary_hover_color' => '#e44b63',
                'secondary_color' => '#27272A',
                'accent_color' => '#292524',
                'background_color' => '#0C0A09',
                'card_color' => 'rgba(28, 25, 23, 0.48)',
                'muted_color' => '#262626',
                'destructive_color' => '#7F1D1D',
                'sidebar_color' => 'rgba(25, 25, 25, 0.86)',
                'glass_color' => 'rgba(135, 135, 135, 0.27)',
                'tooltip_color' => '#1f2937',
                'text_primary_color' => '#ffffff',
                'text_secondary_color' => '#FAFAFA',
                'text_foreground_color' => '#F2F2F2',
                'text_muted_color' => '#A1A1AA',
                'text_destructive_color' => '#FEF2F2',
                'destructive_text_color' => '#ff0000',
                'dark_mode' => true,
                'sidebar_collapsed' => false,
                'animations' => true,
                'custom_css' => null,
            ]);
        }
        
        return $settings;
    }

    /**
     * Get all color values with fallbacks to defaults
     */
    public function getColorValues(): array
    {
        $defaults = [
            'primary_color' => '#df3050',
            'primary_hover_color' => '#e44b63',
            'secondary_color' => '#27272A',
            'accent_color' => '#292524',
            'background_color' => '#0C0A09',
            'card_color' => 'rgba(28, 25, 23, 0.48)',
            'muted_color' => '#262626',
            'destructive_color' => '#7F1D1D',
            'sidebar_color' => 'rgba(25, 25, 25, 0.86)',
            'glass_color' => 'rgba(135, 135, 135, 0.27)',
            'tooltip_color' => '#1f2937',
            'text_primary_color' => '#ffffff',
            'text_secondary_color' => '#FAFAFA',
            'text_foreground_color' => '#F2F2F2',
            'text_muted_color' => '#A1A1AA',
            'text_destructive_color' => '#FEF2F2',
            'destructive_text_color' => '#ff0000',
        ];

        $colors = [];
        foreach ($defaults as $key => $default) {
            $colors[$key] = $this->$key ?? $default;
        }

        return $colors;
    }
}
