<?php

namespace Pterodactyl\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $server_id
 * @property string $name
 * @property string $original_path
 * @property string $original_directory
 * @property bool $is_file
 * @property int|null $size
 * @property Carbon|null $modified_at
 * @property Carbon $deleted_at
 * @property Carbon $expires_at
 * @property string|null $mimetype
 * @property string|null $extension
 * @property string $recycle_bin_path
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * 
 * @property-read Server $server
 */
class ServerRecycleBin extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'server_recycle_bin';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'server_id',
        'name',
        'original_path',
        'original_directory',
        'is_file',
        'size',
        'modified_at',
        'deleted_at',
        'expires_at',
        'mimetype',
        'extension',
        'recycle_bin_path',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'is_file' => 'boolean',
        'size' => 'integer',
        'modified_at' => 'datetime',
        'deleted_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Scope to get only expired files
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', Carbon::now());
    }

    /**
     * Scope to get files expiring soon (within 24 hours)
     */
    public function scopeExpiringSoon($query)
    {
        return $query->where('expires_at', '<', Carbon::now()->addDay())
                    ->where('expires_at', '>', Carbon::now());
    }

    /**
     * Scope to get files by type
     */
    public function scopeFileType($query, string $type)
    {
        if ($type === 'file') {
            return $query->where('is_file', true);
        } elseif ($type === 'directory') {
            return $query->where('is_file', false);
        }
        
        return $query; // Return all if type is 'all' or invalid
    }

    /**
     * Get the server that owns this recycled file.
     */
    public function server(): BelongsTo
    {
        return $this->belongsTo(Server::class);
    }

    /**
     * Check if the file is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if the file is expiring soon (within 24 hours)
     */
    public function isExpiringSoon(): bool
    {
        return $this->expires_at && $this->expires_at->isBefore(Carbon::now()->addDay()) && !$this->isExpired();
    }

    /**
     * Get human readable file size
     */
    public function getFormattedSizeAttribute(): string
    {
        if (!$this->is_file || !$this->size) {
            return '0 B';
        }

        $sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        $i = floor(log($this->size) / log(1024));
        
        return round($this->size / pow(1024, $i), 2) . ' ' . $sizes[$i];
    }

    /**
     * Generate unique recycle bin path for the file
     */
    public static function generateRecycleBinPath(string $originalPath, int $serverId): string
    {
        $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
        $uuid = substr(md5($originalPath . $serverId . time()), 0, 8);
        
        return "deleted_{$timestamp}_{$uuid}_" . basename($originalPath);
    }
}