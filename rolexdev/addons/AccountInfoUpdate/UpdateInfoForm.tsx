import React from 'react';
import { Formik, Form, FormikHelpers } from 'formik';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import * as Yup from 'yup';
import SpinnerOverlay from '@/components/elements/SpinnerOverlay';
import updateAccountInfo from '@rolexdev/addons/AccountInfoUpdate/api/updateAccountInfo';
import { useStoreState, useStoreActions } from 'easy-peasy';
import { ApplicationStore } from '@rolexdev/themes/hyperv1/state';

interface Values {
    first_name: string;
    last_name: string;
    username: string;
    email: string;
    confirm_password: string;
}

const schema = Yup.object().shape({
    first_name: Yup.string().required('First name is required.'),
    last_name: Yup.string().required('Last name is required.'),
    username: Yup.string().required('Username is required.'),
    email: Yup.string().email('Invalid email address.').required('Email is required.'),
    confirm_password: Yup.string().required('Please confirm your password.'),
});

export default () => {
    const user = useStoreState((state: ApplicationStore) => state.user.data);
    const updateUserData = useStoreActions((actions) => actions.user.updateUserData);

    if (!user) return null;

    const submit = async (values: Values, { setSubmitting, setFieldError }: FormikHelpers<Values>) => {
        setSubmitting(true);
        try {
            await updateAccountInfo({
                first_name: values.first_name,
                last_name: values.last_name,
                username: values.username,
                email: values.email,
                confirm_password: values.confirm_password,
            });
            updateUserData({
                first_name: values.first_name,
                last_name: values.last_name,
                username: values.username,
                email: values.email,
            });
        } catch (error: any) {
            setFieldError('confirm_password', 'Incorrect password or failed to update info.');
        }
        setSubmitting(false);
    };

    return (
        <div className='mx-auto max-w-[380px] w-fit text-hyper-primary rounded-xl bg-hyper-card backdrop-blur-lg border border-hyper-primary p-5 max-h-fit'>
            <div className='w-full h-fit mb-4'>
                <h1 className='text-2xl font-bold text-hyper-primary'>Update Account Details</h1>
            </div>
            <Formik
                initialValues={{
                    first_name: user?.first_name || '',
                    last_name: user?.last_name || '',
                    username: user?.username || '',
                    email: user?.email || '',
                    confirm_password: '',
                }}
                validationSchema={schema}
                onSubmit={submit}
            >
                {({ isSubmitting }) => (
                    <Form>
                        <SpinnerOverlay size={'large'} visible={isSubmitting} />
                        <div className='grid grid-cols-2 gap-4 mb-6'>
                            <Field
                                light
                                type='text'
                                label='First Name'
                                name='first_name'
                                disabled={isSubmitting}
                            />
                            <Field
                                light
                                type='text'
                                label='Last Name'
                                name='last_name'
                                disabled={isSubmitting}
                            />
                        </div>
                        <div className='mb-6'>
                            <Field
                                light
                                type='text'
                                label='Username'
                                name='username'
                                disabled={isSubmitting}
                            />
                        </div>
                        <div className='mb-6'>
                            <Field
                                light
                                type='email'
                                label='Email Address'
                                name='email'
                                disabled={isSubmitting}
                            />
                        </div>
                        <div className='mb-6'>
                            <Field
                                light
                                type='password'
                                label='Confirm Password'
                                name='confirm_password'
                                disabled={isSubmitting}
                            />
                        </div>
                        <div className=''>
                            <Button type='submit' size='xlarge' disabled={isSubmitting}>
                                Update Account Details
                            </Button>
                        </div>
                    </Form>
                )}
            </Formik>
        </div>
    );
};