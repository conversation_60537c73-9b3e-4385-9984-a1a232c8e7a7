import http from '@/api/http';

export interface RegisterResponse {
    complete: boolean;
    intended?: string;
    user?: any;
}

export interface RegisterData {
    first_name: string;
    last_name: string;
    username: string;
    email: string;
    password: string;
    password_confirmation: string;
    recaptchaData?: string | null;
}

export default ({ first_name, last_name, username, email, password, password_confirmation, recaptchaData }: RegisterData): Promise<RegisterResponse> => {
    return new Promise((resolve, reject) => {
        http.get('/sanctum/csrf-cookie')
            .then(() =>
                http.post('/auth/register', {
                    first_name,
                    last_name,
                    username,
                    email,
                    password,
                    password_confirmation,
                    'g-recaptcha-response': recaptchaData,
                })
            )
            .then((response) => {
                if (!(response.data instanceof Object)) {
                    return reject(new Error('An error occurred while processing the registration request.'));
                }

                return resolve({
                    complete: response.data.data.complete,
                    intended: response.data.data.intended || undefined,
                    user: response.data.data.user || undefined,
                });
            })
            .catch(reject);
    });
};
