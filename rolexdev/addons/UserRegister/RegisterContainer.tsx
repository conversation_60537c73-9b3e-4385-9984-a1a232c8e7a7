import React, { useEffect, useRef, useState } from 'react';
import { RouteComponentProps } from 'react-router-dom';
import register from '@rolexdev/addons/UserRegister/api/auth/register';
import LoginFormContainer from '@rolexdev/themes/hyperv1/components/auth/LoginFormContainer';
import { useStoreState } from 'easy-peasy';
import { Formik, FormikHelpers } from 'formik';
import { object, string } from 'yup';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import tw from 'twin.macro';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import Reaptcha from 'reaptcha';
import useFlash from '@/plugins/useFlash';

interface Values {
    first_name: string;
    last_name: string;
    username: string;
    email: string;
    password: string;
    password_confirmation: string;
}

const RegisterContainer = ({ history }: RouteComponentProps) => {
    const ref = useRef<Reaptcha>(null);
    const [token, setToken] = useState('');

    const { clearFlashes, clearAndAddHttpError } = useFlash();
    const { enabled: recaptchaEnabled, siteKey } = useStoreState((state) => state.settings.data!.recaptcha);

    useEffect(() => {
        clearFlashes();
    }, []);

    const onSubmit = (values: Values, { setSubmitting }: FormikHelpers<Values>) => {
        clearFlashes();

        // If there is no token in the state yet, request the token and then abort this submit request
        // since it will be re-submitted when the recaptcha data is returned by the component.
        if (recaptchaEnabled && !token) {
            ref.current!.execute().catch((error) => {
                console.error(error);

                setSubmitting(false);
                clearAndAddHttpError({ error });
            });

            return;
        }

        register({ ...values, recaptchaData: token })
            .then((response) => {
                if (response.complete) {
                    // @ts-expect-error this is valid
                    window.location = response.intended || '/';
                    return;
                }
            })
            .catch((error) => {
                console.error(error);

                setToken('');
                if (ref.current) ref.current.reset();

                setSubmitting(false);
                clearAndAddHttpError({ error });
            });
    };

    return (
        <Formik
            onSubmit={onSubmit}
            initialValues={{
                first_name: '',
                last_name: '',
                username: '',
                email: '',
                password: '',
                password_confirmation: '',
            }}
            validationSchema={object().shape({
                first_name: string().required('A first name is required.'),
                last_name: string().required('A last name is required.'),
                username: string()
                    .required('A username is required.')
                    .min(1, 'Username must be at least 1 character.')
                    .max(191, 'Username cannot be longer than 191 characters.')
                    .matches(
                        /^[a-zA-Z0-9][\w\.-]*[a-zA-Z0-9]$|^[a-zA-Z0-9]$/,
                        'Username must start and end with alphanumeric characters and contain only letters, numbers, dashes, underscores, and periods.'
                    ),
                email: string()
                    .email('Please enter a valid email address.')
                    .required('An email address is required.'),
                password: string()
                    .required('Please enter a password.')
                    .min(8, 'Password must be at least 8 characters long.'),
                password_confirmation: string()
                    .required('Please confirm your password.')
                    .test('passwords-match', 'Passwords must match', function(value) {
                        return this.parent.password === value;
                    }),
            })}
        >
            {({ isSubmitting, setSubmitting, submitForm }) => (
                <LoginFormContainer 
                    title={'Create Account'} 
                    button={'Login'} 
                    button_link={'/auth/login'} 
                    text_button={'Forget Password?'} 
                    text_link={'/auth/password'} 
                    css={tw`w-full flex`}
                >
                    <div css={tw`grid grid-cols-2 gap-4`}>
                        <Field light type={'text'} label={'First Name'} name={'first_name'} disabled={isSubmitting} />
                        <Field light type={'text'} label={'Last Name'} name={'last_name'} disabled={isSubmitting} />
                    </div>
                    <div css={tw`mt-6`}>
                        <Field light type={'text'} label={'Username'} name={'username'} disabled={isSubmitting} />
                    </div>
                    <div css={tw`mt-6`}>
                        <Field light type={'email'} label={'Email Address'} name={'email'} disabled={isSubmitting} />
                    </div>
                    <div css={tw`mt-6`}>
                        <Field light type={'password'} label={'Password'} name={'password'} disabled={isSubmitting} />
                    </div>
                    <div css={tw`mt-6`}>
                        <Field light type={'password'} label={'Confirm Password'} name={'password_confirmation'} disabled={isSubmitting} />
                    </div>
                    <div css={tw`mt-6`}>
                        <Button type={'submit'} size={'xlarge'} isLoading={isSubmitting} disabled={isSubmitting}>
                            Create Account
                        </Button>
                    </div>
                    {recaptchaEnabled && (
                        <Reaptcha
                            ref={ref}
                            size={'invisible'}
                            sitekey={siteKey || '_invalid_key'}
                            onVerify={(response) => {
                                setToken(response);
                                submitForm();
                            }}
                            onExpire={() => {
                                setSubmitting(false);
                                setToken('');
                            }}
                        />
                    )}
                </LoginFormContainer>
            )}
        </Formik>
    );
};

export default RegisterContainer;
