import { action, Action } from 'easy-peasy';
import { RecycledFile, RecycleBinStats, RecycleBinFilterOptions } from '@rolexdev/themes/hyperv1/types/recycleBin';

export interface ServerRecycleBinStore {
    /**
     * List of recycled files
     */
    recycledFiles: RecycledFile[];
    
    /**
     * Selected recycled files for bulk operations
     */
    selectedRecycledFiles: string[];
    
    /**
     * Recycle bin statistics
     */
    stats: RecycleBinStats | null;
    
    /**
     * Current filter options
     */
    filters: RecycleBinFilterOptions;
    
    /**
     * Loading states
     */
    loading: {
        files: boolean;
        stats: boolean;
        restore: boolean;
        delete: boolean;
    };
    
    /**
     * Pagination info
     */
    pagination: {
        currentPage: number;
        totalPages: number;
        perPage: number;
        total: number;
    };
    
    /**
     * Open dropdown for recycled file actions
     */
    openDropdownFileId: string | null;

    // Actions
    setRecycledFiles: Action<ServerRecycleBinStore, RecycledFile[]>;
    addRecycledFile: Action<ServerRecycleBinStore, RecycledFile>;
    removeRecycledFile: Action<ServerRecycleBinStore, string>;
    updateRecycledFile: Action<ServerRecycleBinStore, RecycledFile>;
    
    setSelectedRecycledFiles: Action<ServerRecycleBinStore, string[]>;
    appendSelectedRecycledFile: Action<ServerRecycleBinStore, string>;
    removeSelectedRecycledFile: Action<ServerRecycleBinStore, string>;
    clearSelectedRecycledFiles: Action<ServerRecycleBinStore>;
    
    setStats: Action<ServerRecycleBinStore, RecycleBinStats>;
    
    setFilters: Action<ServerRecycleBinStore, Partial<RecycleBinFilterOptions>>;
    resetFilters: Action<ServerRecycleBinStore>;
    
    setLoading: Action<ServerRecycleBinStore, { type: keyof ServerRecycleBinStore['loading']; loading: boolean }>;
    
    setPagination: Action<ServerRecycleBinStore, Partial<ServerRecycleBinStore['pagination']>>;
    
    setOpenDropdownFileId: Action<ServerRecycleBinStore, string | null>;
}

const recycleBin: ServerRecycleBinStore = {
    recycledFiles: [],
    selectedRecycledFiles: [],
    stats: null,
    filters: {
        fileType: 'all',
        searchTerm: '',
        sortBy: 'deletedAt',
        sortDirection: 'desc',
        showExpiringSoon: false,
    },
    loading: {
        files: false,
        stats: false,
        restore: false,
        delete: false,
    },
    pagination: {
        currentPage: 1,
        totalPages: 1,
        perPage: 50,
        total: 0,
    },
    openDropdownFileId: null,

    // File management actions
    setRecycledFiles: action((state, payload) => {
        state.recycledFiles = payload;
    }),

    addRecycledFile: action((state, payload) => {
        const existingIndex = state.recycledFiles.findIndex((f: RecycledFile) => f.id === payload.id);
        if (existingIndex >= 0) {
            state.recycledFiles[existingIndex] = payload;
        } else {
            state.recycledFiles.unshift(payload);
        }
    }),

    removeRecycledFile: action((state, payload) => {
        state.recycledFiles = state.recycledFiles.filter((f: RecycledFile) => f.id !== payload);
        state.selectedRecycledFiles = state.selectedRecycledFiles.filter((id: string) => id !== payload);
    }),

    updateRecycledFile: action((state, payload) => {
        const index = state.recycledFiles.findIndex((f: RecycledFile) => f.id === payload.id);
        if (index >= 0) {
            state.recycledFiles[index] = payload;
        }
    }),

    // Selection management actions
    setSelectedRecycledFiles: action((state, payload) => {
        state.selectedRecycledFiles = payload;
    }),

    appendSelectedRecycledFile: action((state, payload) => {
        if (!state.selectedRecycledFiles.includes(payload)) {
            state.selectedRecycledFiles.push(payload);
        }
    }),

    removeSelectedRecycledFile: action((state, payload) => {
        state.selectedRecycledFiles = state.selectedRecycledFiles.filter((id: string) => id !== payload);
    }),

    clearSelectedRecycledFiles: action((state) => {
        state.selectedRecycledFiles = [];
    }),

    // Stats management
    setStats: action((state, payload) => {
        state.stats = payload;
    }),

    // Filter management
    setFilters: action((state, payload) => {
        state.filters = { ...state.filters, ...payload };
    }),

    resetFilters: action((state) => {
        state.filters = {
            fileType: 'all',
            searchTerm: '',
            sortBy: 'deletedAt',
            sortDirection: 'desc',
            showExpiringSoon: false,
        };
    }),

    // Loading state management
    setLoading: action((state, { type, loading }) => {
        state.loading[type] = loading;
    }),

    // Pagination management
    setPagination: action((state, payload) => {
        state.pagination = { ...state.pagination, ...payload };
    }),

    // Dropdown management
    setOpenDropdownFileId: action((state, payload) => {
        state.openDropdownFileId = payload;
    }),
};

export default recycleBin;