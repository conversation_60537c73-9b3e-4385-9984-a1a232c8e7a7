export interface RecycledFile {
    /**
     * Unique identifier for the recycled file (generated when moved to recycle bin)
     */
    id: string;
    
    /**
     * Original file name
     */
    name: string;
    
    /**
     * Original file path where it was deleted from
     */
    originalPath: string;
    
    /**
     * Full original directory path (for restoration)
     */
    originalDirectory: string;
    
    /**
     * Whether the item is a file or directory
     */
    isFile: boolean;
    
    /**
     * File size in bytes (for files only)
     */
    size?: number;
    
    /**
     * Original modification date
     */
    modifiedAt?: string;
    
    /**
     * Date when the file was moved to recycle bin
     */
    deletedAt: string;
    
    /**
     * Date when the file will be permanently deleted (7 days from deletion)
     */
    expiresAt: string;
    
    /**
     * MIME type of the file (for display purposes)
     */
    mimetype?: string;
    
    /**
     * File extension
     */
    extension?: string;
    
    /**
     * Path in the recycle bin directory
     */
    recycleBinPath: string;
    
    /**
     * Whether the file has expired
     */
    isExpired: boolean;
    
    /**
     * Whether the file is expiring soon (within 24 hours)
     */
    isExpiringSoon: boolean;
    
    /**
     * Human-readable formatted file size
     */
    formattedSize?: string;
}

export interface RecycleBinStats {
    /**
     * Total number of items in recycle bin
     */
    totalItems: number;
    
    /**
     * Total size of all items in recycle bin (bytes)
     */
    totalSize: number;
    
    /**
     * Number of items expiring today
     */
    expiringToday: number;
    
    /**
     * Number of items expiring this week
     */
    expiringThisWeek: number;
}

export interface RecycleBinFilterOptions {
    /**
     * Filter by file type
     */
    fileType?: 'file' | 'directory' | 'all';
    
    /**
     * Search term for file names
     */
    searchTerm?: string;
    
    /**
     * Sort by field
     */
    sortBy?: 'name' | 'deletedAt' | 'expiresAt' | 'size' | 'originalPath';
    
    /**
     * Sort direction
     */
    sortDirection?: 'asc' | 'desc';
    
    /**
     * Show only files expiring soon (within 24 hours)
     */
    showExpiringSoon?: boolean;
}

export interface RestoreFileRequest {
    /**
     * Recycled file ID to restore
     */
    fileId: string;
    
    /**
     * Optional: Custom path to restore to (if different from original)
     */
    customPath?: string;
    
    /**
     * Whether to overwrite existing files with same name
     */
    overwrite?: boolean;
}

export interface RestoreMultipleFilesRequest {
    /**
     * Array of file IDs to restore
     */
    fileIds: string[];
    
    /**
     * Whether to overwrite existing files with same name
     */
    overwrite?: boolean;
}

export interface PermanentDeleteRequest {
    /**
     * Array of file IDs to permanently delete
     */
    fileIds: string[];
}

export interface EmptyRecycleBinRequest {
    /**
     * Confirmation flag
     */
    confirmed: boolean;
}

// API Response types
export interface RecycleBinListResponse {
    data: RecycledFile[];
    meta: {
        total: number;
        page: number;
        perPage: number;
        hasNextPage: boolean;
        hasPreviousPage: boolean;
    };
}

export interface RecycleBinStatsResponse {
    data: RecycleBinStats;
}

// Error types
export interface RecycleBinError {
    message: string;
    code: string;
    details?: Record<string, any>;
}

// Action results
export interface RestoreResult {
    success: boolean;
    restoredFiles: string[];
    failedFiles: Array<{
        name: string;
        error: string;
    }>;
}

export interface DeleteResult {
    success: boolean;
    deletedFiles: string[];
    failedFiles: Array<{
        name: string;
        error: string;
    }>;
}