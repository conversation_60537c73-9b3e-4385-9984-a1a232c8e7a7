'use client';

import React, { useEffect, useRef, useState } from 'react';
import createGlobe from 'cobe';
import { cn } from '@rolexdev/themes/hyperv1/lib/utils';
import { getCSSVariableAsRgbArray, getCSSVariableValue } from '../../lib/colorUtils';

interface EarthProps {
  className?: string;
  theta?: number;
  dark?: number;
  scale?: number;
  diffuse?: number;
  mapSamples?: number;
  mapBrightness?: number;
  baseColor?: [number, number, number];
  markerColor?: [number, number, number];
  glowColor?: [number, number, number];
  width?: string | number;
  height?: string | number;
  maxWidth?: string | number;
  maxHeight?: string | number;
  stop?: boolean;
  usePrimaryColor?: boolean; // New prop to enable dynamic color
}
const Earth: React.FC<EarthProps> = ({
  className,
  theta = 0.25,
  dark = 1,
  scale = 1.1,
  diffuse = 1.2,
  mapSamples = 40000,
  mapBrightness = 6,
  baseColor,
  markerColor,
  glowColor,
  width = '100%',
  height = '100%',
  maxWidth = '100%',
  maxHeight,
  stop = false,
  usePrimaryColor = true,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const globeRef = useRef<any>(null);
  const phiRef = useRef(0);
  const [dynamicColors, setDynamicColors] = useState<{
    baseColor: [number, number, number];
    markerColor: [number, number, number];
    glowColor: [number, number, number];
  }>({
    baseColor: [0.4, 0.6509, 1],
    markerColor: [1, 0, 0],
    glowColor: [0.2745, 0.5765, 0.898],
  });

  // Calculate colors from CSS primary color
  useEffect(() => {
    if (!usePrimaryColor) return;
    
    let lastColorString = '';
    
    const updateColorsFromPrimary = () => {
      try {
        const primaryRgb = getCSSVariableAsRgbArray('--hyper-primary', '#df3050');
        const currentColorString = JSON.stringify(primaryRgb);
        
        // Only update if the color actually changed
        if (currentColorString !== lastColorString) {
          lastColorString = currentColorString;
          
          // Create color variations based on primary color
          const [r, g, b] = primaryRgb;
          
          const newColors = {
            baseColor: [r * 0.8, g * 0.8, b * 1.2] as [number, number, number],
            markerColor: primaryRgb,
            glowColor: [r * 0.6, g * 0.6, b * 1.1] as [number, number, number],
          };
          
          setDynamicColors(newColors);
        }
      } catch (error) {
        console.warn('Failed to get primary color for Earth, using defaults:', error);
        setDynamicColors({
          baseColor: [0.4, 0.6509, 1],
          markerColor: [1, 0, 0],
          glowColor: [0.2745, 0.5765, 0.898],
        });
      }
    };
    
    // Update initially
    updateColorsFromPrimary();
    
    // Debounce MutationObserver updates to prevent excessive color updates
    let updateTimeout: NodeJS.Timeout;
    const debouncedUpdate = () => {
      clearTimeout(updateTimeout);
      updateTimeout = setTimeout(updateColorsFromPrimary, 100);
    };
    
    const observer = new MutationObserver(debouncedUpdate);
    
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style']
    });
    
    return () => {
      clearTimeout(updateTimeout);
      observer.disconnect();
    };
  }, [usePrimaryColor]); // Remove dynamicColors from dependencies
  
  // Use either provided colors or calculated from primary color
  const effectiveBaseColor = baseColor || (usePrimaryColor ? dynamicColors.baseColor : [0.4, 0.6509, 1]);
  const effectiveMarkerColor = markerColor || (usePrimaryColor ? dynamicColors.markerColor : [1, 0, 0]);
  const effectiveGlowColor = glowColor || (usePrimaryColor ? dynamicColors.glowColor : [0.2745, 0.5765, 0.898]);

  // Create globe only once on mount and recreate only when essential props change (not colors)
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const resize = () => {
      const size = canvas.offsetWidth;
      canvas.width = size * 2;
      canvas.height = size * 2;
    };

    resize();

    // Destroy existing globe if it exists
    if (globeRef.current) {
      globeRef.current.destroy?.();
    }

    // Create new globe instance with initial colors
    globeRef.current = createGlobe(canvas, {
      devicePixelRatio: 2,
      width: canvas.width,
      height: canvas.height,
      phi: 0,
      theta,
      dark,
      scale,
      diffuse,
      mapSamples,
      mapBrightness,
      baseColor: effectiveBaseColor,
      markerColor: effectiveMarkerColor,
      glowColor: effectiveGlowColor,
      opacity: 1,
      offset: [0, 0],
      markers: [],
      onRender: (state: Record<string, any>) => {
        state.phi = phiRef.current;
        if (!stop) {
          phiRef.current += 0.003;
        }
      },
    });

    const handleResize = () => {
      resize();
      // Recreate globe on resize
      if (globeRef.current) {
        globeRef.current.destroy?.();
        globeRef.current = createGlobe(canvas, {
          devicePixelRatio: 2,
          width: canvas.width,
          height: canvas.height,
          phi: 0,
          theta,
          dark,
          scale,
          diffuse,
          mapSamples,
          mapBrightness,
          baseColor: effectiveBaseColor,
          markerColor: effectiveMarkerColor,
          glowColor: effectiveGlowColor,
          opacity: 1,
          offset: [0, 0],
          markers: [],
          onRender: (state: Record<string, any>) => {
            state.phi = phiRef.current;
            if (!stop) {
              phiRef.current += 0.003;
            }
          },
        });
      }
    };

    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      if (globeRef.current) {
        globeRef.current.destroy?.();
        globeRef.current = null;
      }
    };
  }, [theta, dark, scale, diffuse, mapSamples, mapBrightness, stop]); // Removed color dependencies
  
  // Separate effect for updating colors without recreating the globe
  useEffect(() => {
    if (!globeRef.current) return;
    
    // Since the cobe library doesn't have a built-in updateColors method,
    // we need to recreate the globe with new colors
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const currentGlobe = globeRef.current;
    
    // Recreate globe with new colors
    currentGlobe.destroy?.();
    globeRef.current = createGlobe(canvas, {
      devicePixelRatio: 2,
      width: canvas.width,
      height: canvas.height,
      phi: 0,
      theta,
      dark,
      scale,
      diffuse,
      mapSamples,
      mapBrightness,
      baseColor: effectiveBaseColor,
      markerColor: effectiveMarkerColor,
      glowColor: effectiveGlowColor,
      opacity: 1,
      offset: [0, 0],
      markers: [],
      onRender: (state: Record<string, any>) => {
        state.phi = phiRef.current;
        if (!stop) {
          phiRef.current += 0.003;
        }
      },
    });
  }, [JSON.stringify(effectiveBaseColor), JSON.stringify(effectiveMarkerColor), JSON.stringify(effectiveGlowColor)]); // Update only when colors change
  return (
    <div
      className={cn(
        'flex items-center justify-center z-[10] w-full mx-auto transition-transform duration-300 hover:scale-105 cursor-pointer overflow-visible',
        className
      )}
      style={{
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
    maxWidth: typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth,
    maxHeight: typeof maxHeight === 'number' ? `${maxHeight}px` : maxHeight,
  }}
    >
      <canvas
        ref={canvasRef}
        style={{
          width: width,
          height: height,
          maxWidth: maxWidth,
          maxHeight: maxHeight,
          aspectRatio: '1',
        }}
      />
    </div>
  );
};

export default Earth;