/* CodeMirror HyperV1 Theme Overrides */

/* Container styling for hyperv1 theme integration */
.hyperv1-editor-container .CodeMirror {
    background: var(--hyper-card) !important;
    color: var(--hyper-text-primary) !important;
    font-family: 'JetBrains Mono', 'Source Code Pro', monospace !important;
    font-size: 13px !important;
    line-height: 1.5 !important;
    border: none !important;
    border-radius: 12px !important;
    overflow: hidden !important;
}

/* Gutters (line numbers area) */
.hyperv1-editor-container .CodeMirror-gutters {
    background: var(--hyper-sidebar) !important;
    border-right: 1px solid var(--hyper-accent) !important;
    border-radius: 12px 0 0 12px !important;
}

/* Line numbers */
.hyperv1-editor-container .CodeMirror-linenumber {
    color: var(--hyper-text-muted) !important;
    font-size: 12px !important;
    padding: 0 8px !important;
}

/* Active line */
.hyperv1-editor-container .CodeMirror-activeline-background {
    background: var(--hyper-primary-10) !important;
}

/* Cursor */
.hyperv1-editor-container .CodeMirror-cursor {
    border-left: 2px solid var(--hyper-primary) !important;
}

/* Selection */
.hyperv1-editor-container .CodeMirror-selected {
    background: rgba(var(--hyper-primary-rgb), var(--hyper-opacity-30)) !important;
}

/* Focused selection */
.hyperv1-editor-container .CodeMirror-focused .CodeMirror-selected {
    background: rgba(var(--hyper-primary-rgb), var(--hyper-opacity-50)) !important;
}

/* Search highlight */
.hyperv1-editor-container .cm-searching {
    background: rgba(255, 255, 0, 0.4) !important;
}

/* Fold gutters */
.hyperv1-editor-container .CodeMirror-foldgutter {
    width: 16px !important;
}

.hyperv1-editor-container .CodeMirror-foldgutter-open,
.hyperv1-editor-container .CodeMirror-foldgutter-folded {
    color: var(--hyper-text-primary) !important;
    cursor: pointer !important;
}

.hyperv1-editor-container .CodeMirror-foldgutter-open:hover,
.hyperv1-editor-container .CodeMirror-foldgutter-folded:hover {
    color: var(--hyper-primary) !important;
}

/* Fold marker */
.hyperv1-editor-container .CodeMirror-foldmarker {
    background: var(--hyper-primary) !important;
    color: var(--hyper-text-primary) !important;
    border: 1px solid var(--hyper-primary) !important;
    border-radius: 4px !important;
    padding: 0 4px !important;
    margin: 0 2px !important;
    font-size: 10px !important;
}

/* Scrollbars */
.hyperv1-editor-container .CodeMirror-simplescroll-horizontal,
.hyperv1-editor-container .CodeMirror-simplescroll-vertical,
.hyperv1-editor-container .CodeMirror-overlayscroll-horizontal,
.hyperv1-editor-container .CodeMirror-overlayscroll-vertical {
    background: var(--hyper-glass) !important;
    border-radius: 8px !important;
}

.hyperv1-editor-container .CodeMirror-simplescroll-horizontal div,
.hyperv1-editor-container .CodeMirror-simplescroll-vertical div,
.hyperv1-editor-container .CodeMirror-overlayscroll-horizontal div,
.hyperv1-editor-container .CodeMirror-overlayscroll-vertical div {
    background: var(--hyper-primary) !important;
    border-radius: 8px !important;
    border: none !important;
}

.hyperv1-editor-container .CodeMirror-simplescroll-horizontal div:hover,
.hyperv1-editor-container .CodeMirror-simplescroll-vertical div:hover,
.hyperv1-editor-container .CodeMirror-overlayscroll-horizontal div:hover,
.hyperv1-editor-container .CodeMirror-overlayscroll-vertical div:hover {
    background: var(--hyper-primary-hover) !important;
}

/* Standard webkit scrollbars for CodeMirror - match main theme */
.hyperv1-editor-container .CodeMirror::-webkit-scrollbar {
    width: 2px !important;
    height: 2px !important;
}

.hyperv1-editor-container .CodeMirror::-webkit-scrollbar-thumb {
    background-color: var(--hyper-primary) !important;
    border-radius: 9999px !important;
    border: 1px solid transparent;
}

.hyperv1-editor-container .CodeMirror::-webkit-scrollbar-thumb:hover {
    background-color: var(--hyper-primary-hover) !important;
}

.hyperv1-editor-container .CodeMirror::-webkit-scrollbar-track {
    background: transparent !important;
    border-radius: 9999px !important;
}

/* Firefox scrollbar styling for CodeMirror */
.hyperv1-editor-container .CodeMirror {
    scrollbar-width: thin !important;
    scrollbar-color: var(--hyper-primary) transparent !important;
}

/* Syntax highlighting for hyperv1 theme */
.hyperv1-editor-container .cm-keyword {
    color: #e44b63 !important; /* hyper-primary-hover */
}

.hyperv1-editor-container .cm-atom {
    color: #fbbf24 !important; /* yellow */
}

.hyperv1-editor-container .cm-number {
    color: #60a5fa !important; /* blue */
}

.hyperv1-editor-container .cm-def {
    color: #34d399 !important; /* green */
}

.hyperv1-editor-container .cm-variable,
.hyperv1-editor-container .cm-punctuation,
.hyperv1-editor-container .cm-property,
.hyperv1-editor-container .cm-operator {
    color: var(--hyper-text-primary) !important;
}

.hyperv1-editor-container .cm-variable-2 {
    color: #a78bfa !important; /* purple */
}

.hyperv1-editor-container .cm-variable-3,
.hyperv1-editor-container .cm-type {
    color: #22d3ee !important; /* cyan */
}

.hyperv1-editor-container .cm-comment {
    color: var(--hyper-text-muted) !important;
    font-style: italic !important;
}

.hyperv1-editor-container .cm-string {
    color: #fbbf24 !important; /* yellow */
}

.hyperv1-editor-container .cm-string-2 {
    color: #fb7185 !important; /* pink */
}

.hyperv1-editor-container .cm-meta {
    color: var(--hyper-accent) !important;
}

.hyperv1-editor-container .cm-qualifier {
    color: #a78bfa !important; /* purple */
}

.hyperv1-editor-container .cm-builtin {
    color: #60a5fa !important; /* blue */
}

.hyperv1-editor-container .cm-bracket {
    color: var(--hyper-primary) !important;
}

.hyperv1-editor-container .cm-tag {
    color: var(--hyper-primary) !important;
}

.hyperv1-editor-container .cm-attribute {
    color: #34d399 !important; /* green */
}

.hyperv1-editor-container .cm-hr {
    color: var(--hyper-text-muted) !important;
}

.hyperv1-editor-container .cm-link {
    color: #60a5fa !important; /* blue */
    text-decoration: underline !important;
}

.hyperv1-editor-container .cm-error {
    background: rgba(239, 68, 68, 0.2) !important;
    color: #ef4444 !important;
}

/* Matching brackets */
.hyperv1-editor-container .CodeMirror-matchingbracket {
    color: var(--hyper-primary) !important;
    background: rgba(var(--hyper-primary-rgb), var(--hyper-opacity-30)) !important;
    border-radius: 2px !important;
}

.hyperv1-editor-container .CodeMirror-nonmatchingbracket {
    color: #ef4444 !important;
    background: rgba(239, 68, 68, 0.2) !important;
    border-radius: 2px !important;
}

/* Trailing spaces */
.hyperv1-editor-container .cm-trailingspace {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3QUXCToH00Y1UgAAACFJREFUGNNjYMAC/ykEX8n00Pj///8ZXw+9+v//P4lOSwWYHwAAGwwKCFOe0E4AAAAASUVORK5CYII=");
    background-position: bottom left;
    background-repeat: repeat-x;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hyperv1-editor-container .CodeMirror {
        font-size: 12px !important;
    }
    
    .hyperv1-editor-container .CodeMirror-linenumber {
        font-size: 11px !important;
        padding: 0 6px !important;
    }
}

/* Dark mode compatibility */
@media (prefers-color-scheme: dark) {
    .hyperv1-editor-container .CodeMirror {
        background: var(--hyper-card) !important;
        color: var(--hyper-text-primary) !important;
    }
}

/* Search dialog styling */
.hyperv1-editor-container .CodeMirror-dialog {
    background: var(--hyper-sidebar) !important;
    color: var(--hyper-text-primary) !important;
    border: 1px solid var(--hyper-primary) !important;
    border-radius: 8px !important;
    padding: 8px !important;
}

.hyperv1-editor-container .CodeMirror-dialog input {
    background: var(--hyper-background) !important;
    color: var(--hyper-text-primary) !important;
    border: 1px solid var(--hyper-primary) !important;
    border-radius: 4px !important;
    padding: 4px 8px !important;
}

.hyperv1-editor-container .CodeMirror-dialog input:focus {
    border-color: var(--hyper-primary) !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(var(--hyper-primary-rgb), var(--hyper-opacity-30)) !important;
}

/* Hint dropdown styling */
.hyperv1-editor-container .CodeMirror-hints {
    background: var(--hyper-sidebar) !important;
    border: 1px solid var(--hyper-primary) !important;
    border-radius: 8px !important;
    color: var(--hyper-text-primary) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
}

.hyperv1-editor-container .CodeMirror-hint {
    color: var(--hyper-text-primary) !important;
    padding: 4px 8px !important;
}

.hyperv1-editor-container .CodeMirror-hint-active {
    background: var(--hyper-primary) !important;
    color: var(--hyper-text-primary) !important;
}