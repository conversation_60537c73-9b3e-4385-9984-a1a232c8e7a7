@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

:root {
    /* Primary color palette */
    --hyper-primary: #df3050;
    --hyper-primary-hover: #e44b63;
    --hyper-secondary: #27272A;
    --hyper-accent: #292524;
    --hyper-background: #0C0A09;
    --hyper-card: #1c19177a;
    --hyper-muted: #262626;
    --hyper-destructive: #7F1D1D;
    --hyper-destructive-text: #ff0000;
    --hyper-sidebar: #191919db;
    --hyper-glass: #87878746;
    --hyper-tooltip: #1f2937;
    
    /* Text colors */
    --hyper-text-primary: #ffffff;
    --hyper-text-secondary: #FAFAFA;
    --hyper-text-foreground: #F2F2F2;
    --hyper-text-muted: #A1A1AA;
    --hyper-text-destructive: #FEF2F2;
    
    /* Opacity values */
    --hyper-opacity-10: 0.1;
    --hyper-opacity-30: 0.3;
    --hyper-opacity-50: 0.5;
    --hyper-opacity-60: 0.6;
    --hyper-opacity-70: 0.7;
    
    /* Color with opacity utilities */
    --hyper-primary-rgb: 223, 48, 80;
    --hyper-background-rgb: 12, 10, 9;
    --hyper-white-rgb: 255, 255, 255;
}

body, html, * {
    font-family: 'Poppins', sans-serif !important;
}

.bg-hyper-background {
    background-color: var(--hyper-background) !important;
}
.bg-hyper-card {
    background-color: var(--hyper-card) !important;
}
.bg-hyper-primary {
    background-color: var(--hyper-primary) !important;
}
.bg-hyper-secondary {
    background-color: var(--hyper-secondary) !important;
}
.bg-hyper-accent {
    background-color: var(--hyper-accent) !important;
}
.bg-hyper-muted {
    background-color: var(--hyper-muted) !important;
}
.bg-hyper-destructive {
    background-color: var(--hyper-destructive) !important;
}
.bg-hyper-sidebar {
    background-color: var(--hyper-sidebar) !important;
}
.bg-hyper-sidebar-accent {
    background-color: var(--hyper-secondary) !important;
}
.bg-hyper-text-primary {
    background-color: var(--hyper-text-primary) !important;
}

.bg-hyper-glass {
    background-color: var(--hyper-glass) !important;
    backdrop-filter: blur(1px);
}
.border-hyper-primary {
    border-color: var(--hyper-secondary) !important;
}
.border-hyper-accent {
    border-color: var(--hyper-primary) !important;
}

.text-hyper-primary {
    color: var(--hyper-text-primary) !important;
}
.text-hyper-secondary {
    color: var(--hyper-text-secondary) !important;
}
.text-hyper-muted-foreground {
    color: var(--hyper-text-muted) !important;
}
.text-hyper-destructive {
    color: var(--hyper-text-destructive) !important;
}

/* Sidebar specific styles */
.sidebar-collapsed {
    width: 80px !important;
}

.sidebar-expanded {
    width: 280px !important;
}

.sidebar-item-icon {
    min-width: 20px;
    min-height: 20px;
}

.sidebar-dropdown-arrow::before {
    content: '>';
    transition: transform 0.2s ease;
}

.sidebar-dropdown-arrow.open::before {
    transform: rotate(90deg);
}

/* Sidebar menu item active and hover states */
.sidebar-menu-item.active {
    background-color: var(--hyper-accent) !important;
    color: var(--hyper-text-primary) !important;
}

.sidebar-dropdown-item.active {
    background-color: var(--hyper-accent) !important;
    color: var(--hyper-text-primary) !important;
}

/* Tooltip styles for collapsed sidebar */
.sidebar-menu-item:hover::after {
    content: attr(title);
    position: absolute;
    left: 100%;
    margin-left: 8px;
    padding: 8px 12px;
    background-color: var(--hyper-tooltip);
    color: white;
    font-size: 0.875rem;
    border-radius: 6px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
    z-index: 1000;
}

.sidebar-collapsed .sidebar-menu-item:hover::after {
    opacity: 1;
}

/* Custom width classes for sidebar */
.w-20 {
    width: 80px !important;
}

.w-70 {
    width: 280px !important;
}

/* Custom margin-left classes for content area */
.ml-20 {
    margin-left: 80px !important;
}

.ml-70 {
    margin-left: 280px !important;
}
.text-hyper-foreground {
    color: var(--hyper-text-foreground) !important;
}
.text-hyper-accent {
    color: var(--hyper-primary) !important;
}
.text-hyper-destructive {
    color: var(--hyper-destructive-text) !important;
}
.hyper-input:focus {
    border-color: var(--hyper-primary) !important;
}
.hyper-textarea:focus {
    border-color: var(--hyper-primary) !important;
}

.bg-hyper-primary-10 {
    background-color: rgba(var(--hyper-primary-rgb), var(--hyper-opacity-10)) !important;
}
.bg-hyper-primary-30 {
    background-color: rgba(var(--hyper-primary-rgb), var(--hyper-opacity-30)) !important;
}
.bg-hyper-primary-50 {
    background-color: rgba(var(--hyper-primary-rgb), var(--hyper-opacity-50)) !important;
}
.bg-hyper-background-50 {
    background-color: rgba(var(--hyper-background-rgb), var(--hyper-opacity-60)) !important;
}

/* for Chrome, Safari, Edge */
progress.progress-rounded::-webkit-progress-bar {
    background-color: var(--hyper-glass) !important;
    backdrop-filter: blur(1px);
    border-radius: 9999px;
}

progress.progress-rounded::-webkit-progress-value {
  border-radius: 9999px;
}

/* for Firefox */
progress.progress-rounded {
    background-color: var(--hyper-glass) !important;
    backdrop-filter: blur(1px);
}
progress.progress-rounded::-moz-progress-bar {
  border-radius: 9999px;
}

.button-primary {
  box-shadow: inset 0px 2px 0px rgba(var(--hyper-white-rgb), var(--hyper-opacity-30));
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  cursor: pointer !important;
}
.button-primary:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2),
              inset 0px 2px 0px rgba(var(--hyper-white-rgb), var(--hyper-opacity-30));
}
.server-row {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}
.server-row:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.02);
}

.server-card .server-pin-icon {
    opacity: 0;
    pointer-events: none;
}
.server-card.show-pin .server-pin-icon {
    opacity: 0.3;
    pointer-events: auto;
}
.server-card.show-pin .server-pin-icon:hover {
    opacity: 1;
}

/* Scrollbar Track */
::-webkit-scrollbar {
    width: 2px !important;
    height: 2px !important;
}

/* Scrollbar Thumb */
::-webkit-scrollbar-thumb {
    background-color: var(--hyper-primary) !important; /* accent color */
    border-radius: 9999px !important;
    border: 1px solid transparent;
}

/* Scrollbar Track background */
::-webkit-scrollbar-track {
    background: transparent !important;
    border-radius: 9999px !important;
}
/* Firefox scrollbar styling */
* {
    scrollbar-width: thin !important;
    scrollbar-color: var(--hyper-primary) transparent !important;
}
::-webkit-scrollbar-thumb:hover {
    background-color: var(--hyper-primary-hover) !important;
}

.hover\:bg-hyper-text-primary:hover {
    background-color: var(--hyper-text-primary) !important;
}