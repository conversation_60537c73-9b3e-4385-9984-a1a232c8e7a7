import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { useEffect, useRef } from 'react';
import { SocketEvent } from '@/components/server/events';

const useWebsocketEvent = (event: SocketEvent, callback: (data: string) => void) => {
    let connected = false;
    let instance: any = null;
    
    try {
        const socketState = ServerContext.useStoreState((state: any) => state.socket);
        connected = socketState?.connected || false;
        instance = socketState?.instance || null;
    } catch (error) {
        console.warn('ServerContext not available in useWebsocketEvent:', error);
        connected = false;
        instance = null;
    }
    
    const savedCallback = useRef<any>(null);

    useEffect(() => {
        savedCallback.current = callback;
    }, [callback]);

    return useEffect(() => {
        const eventListener = (event: SocketEvent) => savedCallback.current(event);
        if (connected && instance) {
            instance.addListener(event, eventListener);
        }

        return () => {
            instance && instance.removeListener(event, eventListener);
        };
    }, [event, connected, instance]);
};

export default useWebsocketEvent;