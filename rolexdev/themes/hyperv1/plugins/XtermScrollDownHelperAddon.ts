import { Terminal, ITerminalAddon } from 'xterm';

export class ScrollDownHelperAddon implements ITerminalAddon {
    private terminal: Terminal = new Terminal();
    private element?: HTMLDivElement;

    activate(terminal: Terminal): void {
        this.terminal = terminal;

        this.terminal.onScroll(() => {
            if (this.isScrolledDown()) {
                this.hide();
            }
        });

        this.terminal.onLineFeed(() => {
            if (!this.isScrolledDown()) {
                this.show();
            }
        });

        this.show();
    }

    dispose(): void {
        // ignore
    }

    show(): void {
        if (!this.terminal || !this.terminal.element) {
            return;
        }
        if (this.element) {
            this.element.style.visibility = 'visible';
            return;
        }

        this.terminal.element.style.position = 'relative';

        this.element = document.createElement('div');
        this.element.innerHTML =
            '<svg aria-hidden="true" focusable="false" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" /></svg>';
        this.element.style.position = 'absolute';
        this.element.style.right = '1rem';
        this.element.style.bottom = '1rem';
        this.element.style.width = '2.5rem';
        this.element.style.height = '2.5rem';
        this.element.style.padding = '0.5rem';
        this.element.style.display = 'flex';
        this.element.style.alignItems = 'center';
        this.element.style.justifyContent = 'center';
        this.element.style.backgroundColor = '#191919db'; // --hyper-sidebar
        this.element.style.border = '1px solid #27272A'; // --hyper-secondary (used as border-hyper-primary)
        this.element.style.borderRadius = '0.5rem';
        this.element.style.color = '#ffffff'; // --hyper-text-primary
        this.element.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
        this.element.style.zIndex = '999';
        this.element.style.cursor = 'pointer';
        this.element.style.transition = 'all 0.2s ease-in-out';
        this.element.style.opacity = '0.8';

        this.element.addEventListener('click', () => {
            this.terminal.scrollToBottom();
        });

        // Add hover effects
        this.element.addEventListener('mouseenter', () => {
            this.element!.style.opacity = '1';
            this.element!.style.borderColor = '#df3050'; // --hyper-primary
            this.element!.style.transform = 'translateY(-2px) scale(1.05)';
            this.element!.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
        });

        this.element.addEventListener('mouseleave', () => {
            this.element!.style.opacity = '0.8';
            this.element!.style.borderColor = '#27272A'; // --hyper-secondary
            this.element!.style.transform = 'translateY(0) scale(1)';
            this.element!.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
        });

        this.terminal.element.appendChild(this.element);
    }

    hide(): void {
        if (this.element) {
            this.element.style.visibility = 'hidden';
        }
    }

    isScrolledDown(): boolean {
        return this.terminal.buffer.active.viewportY === this.terminal.buffer.active.baseY;
    }
}
