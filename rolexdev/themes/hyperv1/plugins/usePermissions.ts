import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { useDeepCompareMemo } from '@/plugins/useDeepCompareMemo';
import { useLocation } from 'react-router-dom';

export const usePermissions = (action: string | string[]): boolean[] => {
    const location = useLocation();
    
    // Check if we're in a server context route
    const isServerRoute = location.pathname.startsWith('/server/');
    
    // Use a safe state selector with comprehensive error handling
    let userPermissions: string[] = [];
    
    if (isServerRoute) {
        try {
            userPermissions = ServerContext.useStoreState((state: any) => {
                try {
                    return state?.server?.permissions || [];
                } catch (error) {
                    console.warn('Error accessing server permissions:', error);
                    return [];
                }
            });
        } catch (error) {
            // This error occurs when ServerContext.useStoreState is called outside of a Provider
            // or when the store is not properly initialized
            console.warn('ServerContext not available:', error);
            userPermissions = [];
        }
    } else {
        // For non-server routes (dashboard, account, etc.), we don't have server permissions
        // Return empty array to default to safe behavior
        userPermissions = [];
    }

    return useDeepCompareMemo(() => {
        // For non-server routes, return false to hide server-specific components
        if (!isServerRoute) {
            return Array(Array.isArray(action) ? action.length : 1).fill(false);
        }

        // If permissions is undefined/empty, return true to avoid breaking UI on server routes
        if (!userPermissions || userPermissions.length === 0) {
            return Array(Array.isArray(action) ? action.length : 1).fill(true);
        }

        if (userPermissions[0] === '*') {
            return Array(Array.isArray(action) ? action.length : 1).fill(true);
        }

        return (Array.isArray(action) ? action : [action]).map(
            (permission) =>
                // Allows checking for any permission matching a name, for example files.*
                // will return if the user has any permission under the file.XYZ namespace.
                (permission.endsWith('.*') &&
                    userPermissions.filter((p: string) => p.startsWith(permission.split('.')[0])).length > 0) ||
                // Otherwise just check if the entire permission exists in the array or not.
                userPermissions.indexOf(permission) >= 0
        );
    }, [action, userPermissions, isServerRoute]);
};