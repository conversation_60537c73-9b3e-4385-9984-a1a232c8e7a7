import useSWR from 'swr';
import loadDirectory, { FileObject } from '@/api/server/files/loadDirectory';
import { cleanDirectoryPath } from '@/helpers';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';

export const getDirectorySwrKey = (uuid: string, directory: string): string => `${uuid}:files:${directory}`;

export default () => {
    let uuid: string | undefined;
    let directory: string = '/';
    
    try {
        uuid = ServerContext.useStoreState((state: any) => state.server.data?.uuid);
        directory = ServerContext.useStoreState((state: any) => state.files.directory) || '/';
    } catch (error) {
        console.warn('ServerContext not available in useFileManagerSwr:', error);
        uuid = undefined;
        directory = '/';
    }

    return useSWR<FileObject[]>(
        uuid ? getDirectorySwrKey(uuid, directory) : null,
        () => {
            if (!uuid) {
                throw new Error('Server UUID not available');
            }
            return loadDirectory(uuid, cleanDirectoryPath(directory));
        },
        {
            focusThrottleInterval: 30000,
            revalidateOnMount: false,
            refreshInterval: 0,
            errorRetryCount: 2,
        }
    );
};