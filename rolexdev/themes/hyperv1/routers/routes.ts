import React, { lazy } from 'react';
import ServerConsole from '@rolexdev/themes/hyperv1/components/server/console/ServerConsoleContainer';
import DatabasesContainer from '@rolexdev/themes/hyperv1/components/server/databases/DatabasesContainer';
import ScheduleContainer from '@rolexdev/themes/hyperv1/components/server/schedules/ScheduleContainer';
import UsersContainer from '@rolexdev/themes/hyperv1/components/server/users/UsersContainer';
import BackupContainer from '@rolexdev/themes/hyperv1/components/server/backups/BackupContainer';
import NetworkContainer from '@rolexdev/themes/hyperv1/components/server/network/NetworkContainer';
import StartupContainer from '@rolexdev/themes/hyperv1/components/server/startup/StartupContainer';
import FileManagerContainer from '@rolexdev/themes/hyperv1/components/server/files/FileManagerContainer';
import RecycleBinContainer from '@rolexdev/themes/hyperv1/components/server/files/RecycleBinContainer';
import SettingsContainer from '@rolexdev/themes/hyperv1/components/server/settings/SettingsContainer';
import AccountOverviewContainer from '@rolexdev/themes/hyperv1/components/dashboard/AccountOverviewContainer';
import AccountApiContainer from '@rolexdev/themes/hyperv1/components/dashboard/AccountApiContainer';
import AccountSSHContainer from '@rolexdev/themes/hyperv1/components/dashboard/ssh/AccountSSHContainer';
import ActivityLogContainer from '@rolexdev/themes/hyperv1/components/dashboard/activity/ActivityLogContainer';
import ServerActivityLogContainer from '@rolexdev/themes/hyperv1/components/server/ServerActivityLogContainer';
import ThemeSettingsContainer from '@rolexdev/themes/hyperv1/components/dashboard/ThemeSettingsContainer';
import GeneralThemeSettings from '@rolexdev/themes/hyperv1/components/dashboard/GeneralThemeSettings';
import ColorThemeSettings from '@rolexdev/themes/hyperv1/components/dashboard/ColorThemeSettings';

// Lucide-react icons
import { Terminal, Folder, Database, CalendarClock, Users, Cloud, Network, Settings, Rocket, ListChecks, Trash2 } from 'lucide-react';

const FileEditContainer = lazy(() => import('@rolexdev/themes/hyperv1/components/server/files/FileEditContainer'));
const ScheduleEditContainer = lazy(() => import('@rolexdev/themes/hyperv1/components/server/schedules/ScheduleEditContainer'));

interface RouteDefinition {
    path: string;
    name: string | undefined;
    component: React.ComponentType;
    exact?: boolean;
}

interface ServerRouteDefinition extends RouteDefinition {
    permission: string | string[] | null;
    icon?: React.ReactNode;
    category?: string;
}

interface Routes {
    account: RouteDefinition[];
    dashboard: RouteDefinition[];
    server: ServerRouteDefinition[];
}

export default {
    account: [
        {
            path: '/',
            name: 'Account',
            component: AccountOverviewContainer,
            exact: true,
        },
        {
            path: '/api',
            name: 'API Credentials',
            component: AccountApiContainer,
        },
        {
            path: '/ssh',
            name: 'SSH Keys',
            component: AccountSSHContainer,
        },
        {
            path: '/activity',
            name: 'Activity',
            component: ActivityLogContainer,
        },
    ],
    dashboard: [
        {
            path: '/theme',
            name: 'Theme Settings',
            component: ThemeSettingsContainer,
        },
        {
            path: '/theme/general',
            name: 'General Settings',
            component: GeneralThemeSettings,
        },
        {
            path: '/theme/colors',
            name: 'Color Settings',
            component: ColorThemeSettings,
        },
    ],
    server: [
        {
            path: '/',
            permission: null,
            name: 'Console',
            component: ServerConsole,
            exact: true,
            icon: React.createElement(Terminal, { size: 18 }),
            category: 'General',
        },
        {
            path: '/files',
            permission: 'file.*',
            name: 'Files',
            component: FileManagerContainer,
            icon: React.createElement(Folder, { size: 18 }),
            category: 'Management',
        },
        {
            path: '/files/recycle-bin',
            permission: 'file.*',
            name: undefined,
            component: RecycleBinContainer,
        },
        {
            path: '/files/:action(edit|new)',
            permission: 'file.*',
            name: undefined,
            component: FileEditContainer,
        },
        {
            path: '/databases',
            permission: 'database.*',
            name: 'Databases',
            component: DatabasesContainer,
            icon: React.createElement(Database, { size: 18 }),
            category: 'Management',
        },
        {
            path: '/schedules',
            permission: 'schedule.*',
            name: 'Schedules',
            component: ScheduleContainer,
            icon: React.createElement(CalendarClock, { size: 18 }),
            category: 'Configuration',
        },
        {
            path: '/schedules/:id',
            permission: 'schedule.*',
            name: undefined,
            component: ScheduleEditContainer,
        },
        {
            path: '/users',
            permission: 'user.*',
            name: 'Users',
            component: UsersContainer,
            icon: React.createElement(Users, { size: 18 }),
            category: 'Configuration',
        },
        {
            path: '/backups',
            permission: 'backup.*',
            name: 'Backups',
            component: BackupContainer,
            icon: React.createElement(Cloud, { size: 18 }),
            category: 'Management',
        },
        {
            path: '/network',
            permission: 'allocation.*',
            name: 'Network',
            component: NetworkContainer,
            icon: React.createElement(Network, { size: 18 }),
            category: 'Management',
        },
        {
            path: '/startup',
            permission: 'startup.*',
            name: 'Startup',
            component: StartupContainer,
            icon: React.createElement(Rocket, { size: 18 }),
            category: 'Configuration',
        },
        {
            path: '/settings',
            permission: ['settings.*', 'file.sftp'],
            name: 'Settings',
            component: SettingsContainer,
            icon: React.createElement(Settings, { size: 18 }),
            category: 'General',
        },
        {
            path: '/activity',
            permission: 'activity.*',
            name: 'Activity',
            component: ServerActivityLogContainer,
            icon: React.createElement(ListChecks, { size: 18 }),
            category: 'General',
        },
    ],
} as Routes;
