import TransferListener from '@rolexdev/themes/hyperv1/components/server/TransferListener';
import React, { useEffect, useState } from 'react';
import { Route, Switch, useRouteMatch } from 'react-router-dom';
import TransitionRouter from '@/TransitionRouter';
import WebsocketHandler from '@rolexdev/themes/hyperv1/components/server/WebsocketHandler';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import Spinner from '@/components/elements/Spinner';
import { NotFound, ServerError } from '@/components/elements/ScreenBlock';
import { httpErrorToHuman } from '@/api/http';
import { useStoreState } from 'easy-peasy';
import InstallListener from '@rolexdev/themes/hyperv1/components/server/InstallListener';
import ErrorBoundary from '@/components/elements/ErrorBoundary';
import { useLocation } from 'react-router';
import ConflictStateRenderer from '@rolexdev/themes/hyperv1/components/server/ConflictStateRenderer';
import PermissionRoute from '@rolexdev/themes/hyperv1/components/elements/PermissionRoute';
import routes from '@rolexdev/themes/hyperv1/routers/routes';
import Sidebar from '@rolexdev/themes/hyperv1/components/Sidebar';
import NavigationBar from '@rolexdev/themes/hyperv1/components/NavigationBar';
import useMobileView from '@rolexdev/themes/hyperv1/hooks/useMobileView';

export default () => {
    const match = useRouteMatch<{ id: string }>();
    const location = useLocation();

    const rootAdmin = useStoreState((state) => state.user.data!.rootAdmin);
    const [error, setError] = useState('');

    const id = ServerContext.useStoreState((state) => state.server.data?.id);
    const uuid = ServerContext.useStoreState((state) => state.server.data?.uuid);
    const inConflictState = ServerContext.useStoreState((state) => state.server.inConflictState);
    const serverId = ServerContext.useStoreState((state) => state.server.data?.internalId);
    const getServer = ServerContext.useStoreActions((actions) => actions.server.getServer);
    const clearServerState = ServerContext.useStoreActions((actions) => actions.clearServerState);

    useEffect(
        () => () => {
            clearServerState();
        },
        []
    );

    useEffect(() => {
        setError('');
        getServer(match.params.id).catch((error) => {
            console.error(error);
            setError(httpErrorToHuman(error));
        });
        return () => {
            clearServerState();
        };
    }, [match.params.id]);

    const isMobile = useMobileView(972);
    const [sidebarCollapsed, setSidebarCollapsed] = useState(() => {
        const saved = localStorage.getItem('sidebar-collapsed');
        return saved === 'true';
    });
    const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);

    useEffect(() => {
        const handleToggle = (e: Event) => {
            const detail = (e as CustomEvent).detail;
            setSidebarCollapsed(detail);
        };
        window.addEventListener('sidebar-toggle', handleToggle);
        return () => window.removeEventListener('sidebar-toggle', handleToggle);
    }, []);

    const toggleSidebar = () => {
        setSidebarCollapsed(prev => {
            const newState = !prev;
            localStorage.setItem('sidebar-collapsed', String(newState));
            return newState;
        });
    };

    return (
        <div className="flex min-h-screen">
            {isMobile ? (
                <Sidebar
                    collapsed={sidebarCollapsed}
                    onToggle={toggleSidebar}
                    isMobile={true}
                    show={mobileSidebarOpen}
                    onClose={() => setMobileSidebarOpen(false)}
                    route="server"
                />
            ) : (
                <Sidebar
                    collapsed={sidebarCollapsed}
                    onToggle={toggleSidebar}
                    isMobile={false}
                    route="server"
                />
            )}
            <div
                className={`flex-1 transition-all duration-300 ${
                    isMobile ? '' : (sidebarCollapsed ? 'ml-20' : 'ml-70')
                }`}
            >
                <div className="flex flex-col h-full">
                    <NavigationBar
                        isMobile={isMobile}
                        sidebarCollapsed={sidebarCollapsed}
                        onSidebarToggle={() => {
                            if (isMobile) {
                                setMobileSidebarOpen(open => !open);
                            } else {
                                toggleSidebar();
                            }
                        }}
                        route="server"
                    />
                    {!uuid || !id ? (
                        error ? (
                            <ServerError message={error} />
                        ) : (
                            <Spinner size={'large'} centered />
                        )
                    ) : (
                        <>
                            <InstallListener />
                            <TransferListener />
                            <WebsocketHandler />
                            {inConflictState && (!rootAdmin || (rootAdmin && !location.pathname.endsWith(`/server/${id}`))) ? (
                                <ConflictStateRenderer />
                            ) : (
                                <ErrorBoundary>
                                    <TransitionRouter>
                                        <Switch location={location}>
                                            {routes.server.map(({ path, permission, component: Component }) => (
                                                <PermissionRoute key={path} permission={permission} path={`${match.url.replace(/\/*$/, '')}/${path.replace(/^\/+/, '')}`} exact>
                                                    <Spinner.Suspense>
                                                        <Component />
                                                    </Spinner.Suspense>
                                                </PermissionRoute>
                                            ))}
                                            <Route path={'*'} component={NotFound} />
                                        </Switch>
                                    </TransitionRouter>
                                </ErrorBoundary>
                            )}
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};