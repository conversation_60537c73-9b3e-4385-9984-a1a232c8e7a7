import React from 'react';
import { Route, Switch, useRouteMatch } from 'react-router-dom';
import LoginContainer from '@rolexdev/themes/hyperv1/components/auth/LoginContainer';
import RegisterContainer from '@rolexdev/addons/UserRegister/RegisterContainer';
import ForgotPasswordContainer from '@rolexdev/themes/hyperv1/components/auth/ForgotPasswordContainer';
import ResetPasswordContainer from '@rolexdev/themes/hyperv1/components/auth/ResetPasswordContainer';
import LoginCheckpointContainer from '@rolexdev/themes/hyperv1/components/auth/LoginCheckpointContainer';
import { NotFound } from '@/components/elements/ScreenBlock';
import { useHistory, useLocation } from 'react-router';

export default () => {
    const history = useHistory();
    const location = useLocation();
    const { path } = useRouteMatch();

    return (
        <div>
            <Switch location={location}>
                <Route path={`${path}/login`} component={LoginContainer} exact />
                <Route path={`${path}/register`} component={RegisterContainer} exact />
                <Route path={`${path}/login/checkpoint`} component={LoginCheckpointContainer} />
                <Route path={`${path}/password`} component={ForgotPasswordContainer} exact />
                <Route path={`${path}/password/reset/:token`} component={ResetPasswordContainer} />
                <Route path={`${path}/checkpoint`} />
                <Route path={'*'}>
                    <NotFound onBack={() => history.push('/auth/login')} />
                </Route>
            </Switch>
        </div>
    );
};
