import http from '@/api/http';

export interface ThemeSettings {
    id: number;
    large_logo_url: string | null;
    large_logo_width: number;
    large_logo_height: number;
    large_logo_enabled: boolean;
    small_logo_url: string | null;
    small_logo_width: number;
    small_logo_height: number;
    small_logo_enabled: boolean;
    discord_url: string | null;
    knowledge_base_url: string | null;
    support_url: string | null;
    billing_url: string | null;
    primary_color: string;
    primary_hover_color: string;
    secondary_color: string;
    accent_color: string;
    background_color: string;
    card_color: string;
    muted_color: string;
    destructive_color: string;
    sidebar_color: string;
    glass_color: string;
    tooltip_color: string;
    text_primary_color: string;
    text_secondary_color: string;
    text_foreground_color: string;
    text_muted_color: string;
    text_destructive_color: string;
    destructive_text_color: string;
    dark_mode: boolean;
    sidebar_collapsed: boolean;
    animations: boolean;
    custom_css: string | null;
    created_at: string;
    updated_at: string;
}

export interface ThemeSettingsResponse {
    object: 'theme_settings';
    attributes: ThemeSettings;
}

export const getThemeSettings = async (): Promise<ThemeSettings> => {
    const { data } = await http.get<ThemeSettingsResponse>('/admin/theme-settings');
    return data.attributes;
};

export const updateThemeSettings = async (settings: Partial<Omit<ThemeSettings, 'id' | 'created_at' | 'updated_at'>>): Promise<ThemeSettings> => {
    const { data } = await http.patch<ThemeSettingsResponse>('/admin/theme-settings', settings);
    return data.attributes;
};

export const resetThemeSettings = async (): Promise<ThemeSettings> => {
    const { data } = await http.post<ThemeSettingsResponse>('/admin/theme-settings/reset');
    return data.attributes;
};