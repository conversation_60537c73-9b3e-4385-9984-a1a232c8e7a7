import http from '@/api/http';
import {
    RecycledFile,
    RecycleBinListResponse,
    RecycleBinStatsResponse,
    RestoreFileRequest,
    RestoreMultipleFilesRequest,
    PermanentDeleteRequest,
    EmptyRecycleBinRequest,
    RestoreResult,
    DeleteResult,
    RecycleBinFilterOptions
} from '@rolexdev/themes/hyperv1/types/recycleBin';

/**
 * Move files to recycle bin instead of permanent deletion
 */
export const moveToRecycleBin = async (
    uuid: string, 
    directory: string, 
    files: string[]
): Promise<void> => {
    return new Promise((resolve, reject) => {
        http.post(`/api/client/servers/${uuid}/files/delete`, { 
            root: directory, 
            files 
        })
            .then(() => resolve())
            .catch(reject);
    });
};

/**
 * Get list of files in recycle bin with optional filtering and pagination
 */
export const getRecycleBinFiles = async (
    uuid: string,
    options?: RecycleBinFilterOptions & { page?: number; perPage?: number }
): Promise<RecycleBinListResponse> => {
    const params = new URLSearchParams();
    
    if (options?.page) params.append('page', options.page.toString());
    if (options?.perPage) params.append('per_page', options.perPage.toString());
    if (options?.fileType && options.fileType !== 'all') params.append('type', options.fileType);
    if (options?.searchTerm) params.append('search', options.searchTerm);
    if (options?.sortBy) params.append('sort', options.sortBy);
    if (options?.sortDirection) params.append('direction', options.sortDirection);
    if (options?.showExpiringSoon) params.append('expiring_soon', 'true');

    const { data } = await http.get(`/api/client/servers/${uuid}/files/recycle?${params.toString()}`);
    
        // Transform the data to flatten the attributes structure and convert to camelCase
    const transformedData = {
        ...data,
        data: data.data.map((item: any) => ({
            id: item.attributes.id,
            name: item.attributes.name,
            originalPath: item.attributes.original_path,
            originalDirectory: item.attributes.original_directory,
            isFile: item.attributes.is_file,
            size: item.attributes.size,
            modifiedAt: item.attributes.modified_at,
            deletedAt: item.attributes.deleted_at,
            expiresAt: item.attributes.expires_at,
            mimetype: item.attributes.mimetype,
            extension: item.attributes.extension,
            recycleBinPath: item.attributes.recycle_bin_path,
            isExpired: item.attributes.is_expired,
            isExpiringSoon: item.attributes.is_expiring_soon,
            formattedSize: item.attributes.formatted_size,
        }))
    };
    
    return transformedData;
};

/**
 * Get recycle bin statistics
 */
export const getRecycleBinStats = async (uuid: string): Promise<RecycleBinStatsResponse> => {
    const { data } = await http.get(`/api/client/servers/${uuid}/files/recycle/stats`);
    
    // Transform snake_case to camelCase
    const transformedData = {
        data: {
            totalItems: data.data.total_items || 0,
            totalSize: data.data.total_size || 0,
            expiringToday: data.data.expiring_today || 0,
            expiringThisWeek: data.data.expiring_this_week || 0
        }
    };
    
    return transformedData;
};

/**
 * Restore a single file from recycle bin
 */
export const restoreFile = async (
    uuid: string, 
    request: RestoreFileRequest
): Promise<RestoreResult> => {
    const payload = {
        file_id: request.fileId,
        custom_path: request.customPath,
        overwrite: request.overwrite
    };
    
    const { data } = await http.post(`/api/client/servers/${uuid}/files/recycle/restore`, payload);
    return data;
};

/**
 * Restore multiple files from recycle bin
 */
export const restoreMultipleFiles = async (
    uuid: string, 
    request: RestoreMultipleFilesRequest
): Promise<RestoreResult> => {
    const payload = {
        file_ids: request.fileIds,
        overwrite: request.overwrite
    };
    const { data } = await http.post(`/api/client/servers/${uuid}/files/recycle/restore/multiple`, payload);
    return data;
};

/**
 * Permanently delete files from recycle bin
 */
export const permanentlyDeleteFiles = async (
    uuid: string, 
    request: PermanentDeleteRequest
): Promise<DeleteResult> => {
    const payload = {
        file_ids: request.fileIds
    };
    
    return new Promise((resolve, reject) => {
        http.delete(`/api/client/servers/${uuid}/files/recycle/permanent`, {
            data: payload
        })
            .then(({ data }: any) => resolve(data))
            .catch(reject);
    });
};

/**
 * Empty entire recycle bin (permanently delete all files)
 */
export const emptyRecycleBin = async (
    uuid: string, 
    request: EmptyRecycleBinRequest
): Promise<DeleteResult> => {
    return new Promise((resolve, reject) => {
        http.delete(`/api/client/servers/${uuid}/files/recycle/empty`, {
            data: request
        })
            .then(({ data }: any) => resolve(data))
            .catch(reject);
    });
};

/**
 * Get details of a specific recycled file
 */
export const getRecycledFileDetails = async (
    uuid: string, 
    fileId: string
): Promise<RecycledFile> => {
    const { data } = await http.get(`/api/client/servers/${uuid}/files/recycle/${fileId}`);
    // Transform the data to flatten the attributes structure
    return {
        ...data.data.attributes,
        id: data.data.attributes.id || data.data.id
    };
};

/**
 * Extend expiration date for files (admin only feature)
 */
export const extendFileExpiration = async (
    uuid: string, 
    fileIds: string[], 
    days: number
): Promise<void> => {
    return new Promise((resolve, reject) => {
        http.post(`/api/client/servers/${uuid}/files/recycle/extend`, { 
            file_ids: fileIds, 
            days 
        })
            .then(() => resolve())
            .catch(reject);
    });
};

/**
 * Preview a recycled file (if it's a text file)
 */
export const previewRecycledFile = async (
    uuid: string, 
    fileId: string
): Promise<string> => {
    return new Promise((resolve, reject) => {
        http.get(`/api/client/servers/${uuid}/files/recycle/${fileId}/preview`, {
            transformResponse: (res: any) => res,
            responseType: 'text',
        })
            .then(({ data }: any) => resolve(data))
            .catch(reject);
    });
};

/**
 * Download a recycled file
 */
export const downloadRecycledFile = async (
    uuid: string, 
    fileId: string
): Promise<string> => {
    const { data } = await http.get(`/api/client/servers/${uuid}/files/recycle/${fileId}/download`);
    return data.download_url;
};

// Legacy function name for backward compatibility
export default moveToRecycleBin;