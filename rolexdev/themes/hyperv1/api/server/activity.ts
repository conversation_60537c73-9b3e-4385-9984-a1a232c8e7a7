import useSWR, { ConfigInterface, responseInterface } from 'swr';
import { ActivityLog, Transformers } from '@definitions/user';
import { AxiosError } from 'axios';
import http, { PaginatedResult, QueryBuilderParams, withQueryBuilderParams } from '@/api/http';
import { toPaginatedSet } from '@definitions/helpers';
import useFilteredObject from '@/plugins/useFilteredObject';
import { useServerSWRKey } from '@rolexdev/themes/hyperv1/plugins/useSWRKey';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';

export type ActivityLogFilters = QueryBuilderParams<'ip' | 'event', 'timestamp'> & {
    perPage?: number;
};

const useActivityLogs = (
    filters?: ActivityLogFilters,
    config?: ConfigInterface<PaginatedResult<ActivityLog>, AxiosError>
): responseInterface<PaginatedResult<ActivityLog>, AxiosError> => {
    const uuid = ServerContext.useStoreState((state) => state.server.data?.uuid);
    const key = useServerSWRKey(['activity', useFilteredObject(filters || {})]);

    return useSWR<PaginatedResult<ActivityLog>>(
        key,
        async () => {
            // Build params safely, mapping perPage to per_page
            const qbParams = withQueryBuilderParams(filters);
            const params: Record<string, any> = {
                ...qbParams,
                include: ['actor'],
            };
            if (filters?.perPage) {
                params['per_page'] = filters.perPage;
            }
            // Remove perPage if present (do not mutate qbParams)
            delete params['perPage'];

            const { data } = await http.get(`/api/client/servers/${uuid}/activity`, { params });

            return toPaginatedSet(data, Transformers.toActivityLog);
        },
        { revalidateOnMount: false, ...(config || {}) }
    );
};

export { useActivityLogs };
