import { ServerContext } from '../../state/server';
import useSWR from 'swr';
import http from '@/api/http';
import { rawDataToServerAllocation } from '@/api/transformers';
import { Allocation } from '@/api/server/getServer';

export default () => {
    let uuid: string | undefined;
    
    try {
        uuid = ServerContext.useStoreState((state: any) => state.server.data?.uuid);
    } catch (error) {
        console.warn('ServerContext not available in getServerAllocations:', error);
        uuid = undefined;
    }

    return useSWR<Allocation[]>(
        uuid ? ['server:allocations', uuid] : null,
        async () => {
            if (!uuid) {
                throw new Error('Server UUID not available');
            }

            const { data } = await http.get(`/api/client/servers/${uuid}/network/allocations`);

            return (data.data || []).map(rawDataToServerAllocation);
        },
        { revalidateOnFocus: false, revalidateOnMount: false }
    );
};