import useS<PERSON> from 'swr';
import http, { getPaginationSet, PaginatedResult } from '@/api/http';
import { ServerBackup } from '@/api/server/types';
import { rawDataToServerBackup } from '@/api/transformers';
import { ServerContext } from '../../state/server';
import { createContext, useContext } from 'react';

interface ctx {
    page: number;
    setPage: (value: number | ((s: number) => number)) => void;
}

export const Context = createContext<ctx>({ page: 1, setPage: () => 1 });

type BackupResponse = PaginatedResult<ServerBackup> & { backupCount: number };

export default () => {
    const { page } = useContext(Context);
    let uuid: string | undefined;
    
    try {
        uuid = ServerContext.useStoreState((state: any) => state.server.data?.uuid);
    } catch (error) {
        console.warn('ServerContext not available in getServerBackups:', error);
        uuid = undefined;
    }

    return useSWR<BackupResponse>(
        uuid ? ['server:backups', uuid, page] : null,
        async () => {
            if (!uuid) {
                throw new Error('Server UUID not available');
            }

            const { data } = await http.get(`/api/client/servers/${uuid}/backups`, { params: { page } });

            return {
                items: (data.data || []).map(rawDataToServerBackup),
                pagination: getPaginationSet(data.meta.pagination),
                backupCount: data.meta.backup_count,
            };
        }
    );
};