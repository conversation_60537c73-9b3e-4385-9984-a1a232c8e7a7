import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { NavLink, useLocation, useHistory, useRouteMatch } from 'react-router-dom';
import { useStoreState } from 'easy-peasy';
import { ApplicationStore } from '@/state';
import { LayoutDashboard } from '@rolexdev/themes/hyperv1/components/icons/layout-dashboard';
import { AnimateIcon } from './icons-move/icon';
import { User } from '@rolexdev/themes/hyperv1/components/icons/user';
import { ChevronRight } from '@rolexdev/themes/hyperv1/components/icons/chevron-right';
import { ChevronUpDown } from '@rolexdev/themes/hyperv1/components/icons/chevron-up-down';
import { MessageSquareMore } from '@rolexdev/themes/hyperv1/components/icons/message-square-more';
import { Layers } from '@rolexdev/themes/hyperv1/components/icons/layers';
import { motion, AnimatePresence } from 'framer-motion';
import { LogOut } from '@rolexdev/themes/hyperv1/components/icons/logout';
import { BellRing } from '@rolexdev/themes/hyperv1/components/icons/bellring';
import http from '@/api/http';
import Tooltip from '@rolexdev/themes/hyperv1/components/elements/tooltip/Tooltip';
import { createAvatar } from '@dicebear/core';
import { identicon } from '@dicebear/collection';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import routes from '@rolexdev/themes/hyperv1/routers/routes';
import { Settings, Users, CalendarClock, Folder, KeyRound, Activity, SquareTerminal, Palette } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

interface SidebarProps {
    collapsed: boolean;
    onToggle: () => void;
    isMobile?: boolean;
    show?: boolean;
    onClose?: () => void;
    route?: 'dashboard' | 'server';
}

const categoryIcons: Record<string, React.ReactNode> = {
    General: <Layers size={18} />,
    Management: <Folder size={18} />,
    Automation: <CalendarClock size={18} />,
    Access: <Users size={18} />,
    Configuration: <Settings size={18} />,
};

const getAvatarSvg = (seed: string) => {
    return createAvatar(identicon, { seed, size: 48 }).toString();
};

const SidebarButton: React.FC<{
    item: { path: string; name: string; icon: React.ReactNode; external?: boolean };
    uniqueKey: string;
    collapsed: boolean;
    buttonRefs: React.MutableRefObject<Record<string, HTMLDivElement | null>>;
    sidebarRootRef: React.RefObject<HTMLDivElement>;
    setHoveredRect: React.Dispatch<React.SetStateAction<any>>;
    setHoveredId: React.Dispatch<React.SetStateAction<string | null>>;
    isActive: (pathOrId: string) => boolean;
    small?: boolean;
}> = ({
    item,
    uniqueKey,
    collapsed,
    buttonRefs,
    sidebarRootRef,
    setHoveredRect,
    setHoveredId,
    isActive,
    small = false,
}) => {
    const key = uniqueKey;
    const handleMouseEnter = useCallback(() => {
        const button = buttonRefs.current[key];
        const container = sidebarRootRef.current;
        if (button && container) {
            const buttonRect = button.getBoundingClientRect();
            const containerRect = container.getBoundingClientRect();
            setHoveredRect({
                top: buttonRect.top - containerRect.top,
                height: buttonRect.height,
                width: buttonRect.width,
                left: buttonRect.left - containerRect.left,
            });
            setHoveredId(key);
        }
    }, [key, buttonRefs, sidebarRootRef, setHoveredRect, setHoveredId]);

    const handleMouseLeave = useCallback(() => setHoveredId(null), [setHoveredId]);

    const buttonContent = (
        <>
            <div className="w-5 h-5 mr-3 flex-shrink-0 sidebar-item-icon my-auto">
                {item.icon}
            </div>
            <span
                className={`transition-opacity duration-300 ${
                    collapsed ? 'opacity-0 w-0 overflow-hidden' : ''
                }`}
            >
                {item.name}
            </span>
        </>
    );

    return (
        <div
            key={key}
            ref={el => (buttonRefs.current[key] = el)}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            className="relative z-10"
        >
            <Tooltip content={item.name} placement="right" visible={collapsed}>
                <div>
                    <AnimateIcon animateOnHover>
                        {item.external ? (
                            <a
                                href={item.path}
                                target="_blank"
                                rel="noopener noreferrer"
                                className={`min-w-auto max-h-12 h-12 flex items-center p-3 rounded-lg text-hyper-secondary transition-colors mb-2 no-underline sidebar-menu-item hover:bg-hyper-primary-10 hover:text-hyper-accent`}
                            >
                                {buttonContent}
                            </a>
                        ) : (
                            <NavLink
                                to={item.path}
                                exact
                                activeClassName="bg-hyper-primary-10 text-hyper-accent"
                                className={`min-w-auto max-h-12 h-12 flex items-center p-3 rounded-lg text-hyper-secondary transition-colors mb-2 no-underline sidebar-menu-item`}
                            >
                                {buttonContent}
                            </NavLink>
                        )}
                    </AnimateIcon>
                </div>
            </Tooltip>
        </div>
    );
};

const Sidebar: React.FC<SidebarProps> = ({
    onToggle,
    isMobile = false,
    show = true,
    onClose,
    route = 'dashboard',
}) => {
    const [collapsed, setCollapsed] = useState(() => {
        const saved = localStorage.getItem('sidebar-collapsed');
        return saved === 'true';
    });
    const [accountDropdownOpen, setAccountDropdownOpen] = useState(false);
    const [themeDropdownOpen, setThemeDropdownOpen] = useState(false);
    const [profileDropdownOpen, setProfileDropdownOpen] = useState(false);
    const location = useLocation();
    const history = useHistory();
    const profileDropdownRef = useRef<HTMLDivElement>(null);
    const sidebarRootRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const accountDropdownButtonRef = useRef<HTMLDivElement | null>(null);
    const themeDropdownButtonRef = useRef<HTMLDivElement | null>(null);
    const buttonRefs = useRef<Record<string, HTMLDivElement | null>>({});
    const [hoveredId, setHoveredId] = useState<string | null>(null);
    const [hoveredRect, setHoveredRect] = useState<{
        top: number;
        left: number;
        width: number;
        height: number;
    } | null>(null);

    // Get theme settings for logos and external links
    const { getCurrentSettings } = useTheme();
    const themeSettings = getCurrentSettings();

    const user = useStoreState((state: ApplicationStore) => state.user.data);
    const rootAdmin = useStoreState((state: ApplicationStore) => state.user.data!.rootAdmin);

    // Filter external links based on theme settings
    const externalLinks = useMemo(() => {
        const links = [];
        
        if (themeSettings?.discord_url) {
            links.push({
                path: themeSettings.discord_url,
                name: 'Discord',
                icon: (
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.211.375-.445.865-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028c.462-.63.874-1.295 1.226-1.994a.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z" />
                    </svg>
                ),
                external: true,
            });
        }
        
        if (themeSettings?.support_url) {
            links.push({
                path: themeSettings.support_url,
                name: 'Support Ticket',
                icon: <MessageSquareMore size={20} />,
                external: true,
            });
        }
        
        if (themeSettings?.knowledge_base_url) {
            links.push({
                path: themeSettings.knowledge_base_url,
                name: 'Knowledge Base',
                icon: <Layers size={20} />,
                external: true,
            });
        }
        
        if (themeSettings?.billing_url) {
            links.push({
                path: themeSettings.billing_url,
                name: 'Billing',
                icon: (
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z" />
                    </svg>
                ),
                external: true,
            });
        }
        
        return links;
    }, [themeSettings]);

    // Combine dashboard menu items with external links
    const dashboardMenuItems = useMemo(
        () => [
            {
                path: '/',
                name: 'Dashboard',
                icon: <div className="h-5 w-5"><LayoutDashboard size={20} /></div>,
            },
            ...externalLinks,
        ],
        [externalLinks]
    );    const match = useRouteMatch<{ id: string }>('/server/:id');
    const serverId = match?.params.id;
    const to = (value: string) => {
        if (!match) return value;
        if (value === '/') return match.url;
        return `${match.url.replace(/\/*$/, '')}/${value.replace(/^\/+/, '')}`;
    };

    const serverMenuItems = useMemo(
        () =>
            routes.server
                .filter(
                    (route): route is typeof route & { name: string; icon: React.ReactNode } =>
                        typeof route.name === 'string' && !!route.icon
                )
                .map(route => ({
                    ...route,
                    path: to(route.path),
                    permission: route.permission,
                })),
        [match]
    );

    const groupedServerMenuItems = useMemo(() => {
        const groups: Record<string, typeof serverMenuItems> = {};
        serverMenuItems.forEach(item => {
            const category = item.category || 'Other';
            if (!groups[category]) groups[category] = [];
            groups[category].push(item);
        });
        return groups;
    }, [serverMenuItems]);

    const [openCategory, setOpenCategory] = useState<string | null>(() => {
        // Only persist openCategory for server route
        if (route === 'server') {
            return localStorage.getItem('sidebar-open-category');
        }
        return null;
    });

    const menuItems = route === 'server' ? serverMenuItems : dashboardMenuItems;

    const accountDropdownItems = useMemo(
        () => [
            { path: '/account', name: 'Account', icon: <User size={18} /> },
            { path: '/account/api', name: 'Api Credentials', icon: <KeyRound size={18} /> },
            { path: '/account/ssh', name: 'SSH Keys', icon: <SquareTerminal size={18} /> },
            { path: '/account/activity', name: 'Activity', icon: <Activity size={18} /> },
        ],
        []
    );

    const themeDropdownItems = useMemo(
        () => [
            { path: '/theme/general', name: 'General', icon: <Settings size={18} /> },
            { path: '/theme/colors', name: 'Colors', icon: <Palette size={18} /> },
        ],
        []
    );

    const profileMenuItems = useMemo(
        () => [
            { id: 'profile-account', label: 'Account', icon: <User size={16} />, action: 'account' },
            { id: 'profile-billing', label: 'Billing', icon: <LayoutDashboard size={16} />, action: 'billing' },
            { id: 'profile-notifications', label: 'Notifications', icon: <BellRing size={16} />, action: 'notifications' },
            { id: 'profile-logout', label: 'Logout', icon: <LogOut size={16} />, action: 'logout' },
        ],
        []
    );

    const isActive = useCallback(
        (pathOrId: string) => {
            // For server routes, check if current path starts with the menu item path
            // This allows sub-routes like /file/recyclebin to show /file as active
            if (route === 'server' && pathOrId !== match?.url) {
                return location.pathname.startsWith(pathOrId);
            }
            // For dashboard routes and exact server root, use exact matching
            return location.pathname === pathOrId;
        },
        [location.pathname, route, match?.url]
    );
    const getKey = useCallback((item: typeof menuItems[0]) => item.path, []);

    useEffect(() => {
        // Only persist openCategory for server route
        if (route === 'server') {
            if (openCategory) {
                localStorage.setItem('sidebar-open-category', openCategory);
            } else {
                localStorage.removeItem('sidebar-open-category');
            }
        }
    }, [openCategory, route]);

    useEffect(() => {
        if (location.pathname.startsWith('/account')) setAccountDropdownOpen(true);
        if (location.pathname.startsWith('/theme')) setThemeDropdownOpen(true);
    }, [location.pathname]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (profileDropdownRef.current && !profileDropdownRef.current.contains(event.target as Node)) {
                setProfileDropdownOpen(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    useEffect(() => {
        const handleToggle = (event: CustomEvent<boolean>) => {
            setCollapsed(event.detail);
            localStorage.setItem('sidebar-collapsed', String(event.detail));
        };
        window.addEventListener('sidebar-toggle', handleToggle as EventListener);
        return () => window.removeEventListener('sidebar-toggle', handleToggle as EventListener);
    }, []);

    const handleProfileMenuClick = useCallback(
        (action: string) => {
            setProfileDropdownOpen(false);
            switch (action) {
                case 'account':
                    history.push('/account');
                    break;
                case 'billing':
                    history.push('/billing');
                    break;
                case 'notifications':
                    console.log('Open notifications');
                    break;
                case 'logout':
                    http.post('/auth/logout').finally(() => {
                        // @ts-expect-error this is valid
                        window.location = '/';
                    });
                    break;
            }
        },
        [history]
    );

    const keyForAccountDropdown = 'account-settings';
    const keyForThemeDropdown = 'theme-settings';

    // Sidebar rendering
    const renderSidebarButtons = () => {
        if (route === 'server') {
            return renderServerSidebarButtons();
        }
        // Dashboard logic as before
        return dashboardMenuItems.map((item, index) => {
            const uniqueKey = `${item.name}-${index}`;
            return (
                <SidebarButton
                    key={uniqueKey}
                    uniqueKey={uniqueKey}
                    item={item}
                    collapsed={collapsed}
                    buttonRefs={buttonRefs}
                    sidebarRootRef={sidebarRootRef}
                    setHoveredRect={setHoveredRect}
                    setHoveredId={setHoveredId}
                    isActive={isActive}
                />
            );
        });
    };

    // Account dropdown rendering
    const renderAccountDropdown = () => (
        <div
            ref={accountDropdownButtonRef}
            onMouseEnter={() => {
                const button = accountDropdownButtonRef.current;
                const container = sidebarRootRef.current;
                if (button && container) {
                    const buttonRect = button.getBoundingClientRect();
                    const containerRect = container.getBoundingClientRect();
                    setHoveredRect({
                        top: buttonRect.top - containerRect.top,
                        height: buttonRect.height,
                        width: buttonRect.width,
                        left: buttonRect.left - containerRect.left,
                    });
                    setHoveredId(keyForAccountDropdown);
                }
            }}
            onMouseLeave={() => setHoveredId(null)}
            className="relative z-10"
        >
            <Tooltip content="Account Settings" placement="right" visible={collapsed}>
                <div>
                    <AnimateIcon animateOnHover>
                        <button
                            className={`max-h-12 h-12 flex items-center justify-between w-full p-3 rounded-lg text-hyper-secondary hover:bg-hyper-sidebar-accent transition-colors ${
                                location.pathname.startsWith('/account') ? 'bg-hyper-primary-10 text-hyper-accent' : ''
                            }`}
                            onClick={() => {
                                setAccountDropdownOpen(!accountDropdownOpen);
                                history.push('/account');
                            }}
                        >
                            <div className="flex items-center min-w-auto">
                                <div className="w-5 h-5 mr-3 flex-shrink-0 my-auto">
                                    <User size={20} />
                                </div>
                                <span
                                    className={`transition-opacity duration-300 ${
                                        collapsed ? 'opacity-0 w-0 overflow-hidden' : ''
                                    }`}
                                >
                                    Account Settings
                                </span>
                            </div>
                            <div
                                className={`w-4 h-4 transition-transform duration-200 flex items-center justify-center ${
                                    accountDropdownOpen && !collapsed ? 'rotate-90' : ''
                                } ${collapsed ? 'opacity-0' : ''}`}
                            >
                                <ChevronRight size={16} />
                            </div>
                        </button>
                    </AnimateIcon>
                </div>
            </Tooltip>
            <div
                className={`ml-4 border-l border-hyper-primary pl-4 transition-all duration-300 overflow-hidden ${
                    collapsed ? 'opacity-0' : ''
                }`}
                style={{
                    maxHeight: accountDropdownOpen && !collapsed ? '232px' : '0',
                }}
            >
                <div className="h-2" />
                {accountDropdownItems.map(item => (
                    <SidebarButton
                        key={item.path}
                        uniqueKey={item.path}
                        item={item}
                        collapsed={collapsed}
                        buttonRefs={buttonRefs}
                        sidebarRootRef={sidebarRootRef}
                        setHoveredRect={setHoveredRect}
                        setHoveredId={setHoveredId}
                        // Custom isActive logic for /account
                        isActive={pathOrId =>
                            item.path === '/account'
                                ? location.pathname === '/account'
                                : location.pathname === item.path
                        }
                        small
                    />
                ))}
            </div>
        </div>
    );

    // Theme dropdown rendering
    const renderThemeDropdown = () => (
        <div
            ref={themeDropdownButtonRef}
            onMouseEnter={() => {
                const button = themeDropdownButtonRef.current;
                const container = sidebarRootRef.current;
                if (button && container) {
                    const buttonRect = button.getBoundingClientRect();
                    const containerRect = container.getBoundingClientRect();
                    setHoveredRect({
                        top: buttonRect.top - containerRect.top,
                        height: buttonRect.height,
                        width: buttonRect.width,
                        left: buttonRect.left - containerRect.left,
                    });
                    setHoveredId(keyForThemeDropdown);
                }
            }}
            onMouseLeave={() => setHoveredId(null)}
            className="relative z-10"
        >
            <Tooltip content="Theme Settings" placement="right" visible={collapsed}>
                <div>
                    <AnimateIcon animateOnHover>
                        <button
                            className={`max-h-12 h-12 flex items-center justify-between w-full p-3 rounded-lg text-hyper-secondary hover:bg-hyper-sidebar-accent transition-colors ${
                                location.pathname.startsWith('/theme') ? 'bg-hyper-primary-10 text-hyper-accent' : ''
                            }`}
                            onClick={() => {
                                setThemeDropdownOpen(!themeDropdownOpen);
                                history.push('/theme/general');
                            }}
                        >
                            <div className="flex items-center min-w-auto">
                                <div className="w-5 h-5 mr-3 flex-shrink-0 my-auto">
                                    <Settings size={20} />
                                </div>
                                <span
                                    className={`transition-opacity duration-300 ${
                                        collapsed ? 'opacity-0 w-0 overflow-hidden' : ''
                                    }`}
                                >
                                    Theme Settings
                                </span>
                            </div>
                            <div
                                className={`w-4 h-4 transition-transform duration-200 flex items-center justify-center ${
                                    themeDropdownOpen && !collapsed ? 'rotate-90' : ''
                                } ${collapsed ? 'opacity-0' : ''}`}
                            >
                                <ChevronRight size={16} />
                            </div>
                        </button>
                    </AnimateIcon>
                </div>
            </Tooltip>
            <div
                className={`ml-4 border-l border-hyper-primary pl-4 transition-all duration-300 overflow-hidden ${
                    collapsed ? 'opacity-0' : ''
                }`}
                style={{
                    maxHeight: themeDropdownOpen && !collapsed ? '112px' : '0', // 56px per item * 2 items
                }}
            >
                <div className="h-2" />
                {themeDropdownItems.map(item => (
                    <SidebarButton
                        key={item.path}
                        uniqueKey={item.path}
                        item={item}
                        collapsed={collapsed}
                        buttonRefs={buttonRefs}
                        sidebarRootRef={sidebarRootRef}
                        setHoveredRect={setHoveredRect}
                        setHoveredId={setHoveredId}
                        // Custom isActive logic for /theme
                        isActive={pathOrId =>
                            item.path === '/theme/general'
                                ? location.pathname === '/theme/general' || location.pathname === '/theme'
                                : location.pathname === item.path
                        }
                        small
                    />
                ))}
            </div>
        </div>
    );

    // Profile dropdown rendering
    const renderProfileDropdown = () => (
        <div className="relative" ref={profileDropdownRef}>
            <button
                className={`p-2 rounded-lg text-hyper-muted-foreground hover:text-hyper-secondary hover:bg-hyper-sidebar-accent transition-colors ${
                    collapsed ? 'opacity-0 w-0 overflow-hidden' : ''
                }`}
                onClick={() => setProfileDropdownOpen(!profileDropdownOpen)}
            >
                <ChevronUpDown size={20} />
            </button>
            <div
                className={`absolute bottom-full left-0 mb-2 bg-hyper-sidebar border border-hyper-primary rounded-lg shadow-lg transition-all duration-200 overflow-hidden ${
                    profileDropdownOpen ? 'opacity-100 visible' : 'opacity-0 invisible'
                }`}
            >
                <div className="relative p-2">
                    <div className="flex items-center flex-1 mb-2">
                        <div
                            className="w-10 h-10 rounded-lg bg-hyper-accent flex items-center justify-center text-hyper-primary font-semibold"
                            dangerouslySetInnerHTML={{
                                __html: getAvatarSvg(user?.username || 'user'),
                            }}
                        />
                        <div className="ml-3 flex-1 transition-opacity duration-300">
                            <div className="text-sm font-medium text-hyper-secondary">
                                {user?.username}
                            </div>
                            <div className="text-xs text-hyper-muted-foreground">
                                {user?.email}
                            </div>
                        </div>
                    </div>
                    <div className="border-t border-hyper-primary pt-2">
                        {profileMenuItems.map(({ id, label, icon, action }) => (
                            <div
                                key={id}
                                ref={el => (buttonRefs.current[id] = el)}
                                onMouseEnter={() => {
                                    const button = buttonRefs.current[id];
                                    const container = sidebarRootRef.current;
                                    if (button && container) {
                                        const buttonRect = button.getBoundingClientRect();
                                        const containerRect = container.getBoundingClientRect();
                                        setHoveredRect({
                                            top: buttonRect.top - containerRect.top,
                                            height: buttonRect.height,
                                            width: buttonRect.width,
                                            left: buttonRect.left - containerRect.left,
                                        });
                                        setHoveredId(id);
                                    }
                                }}
                                onMouseLeave={() => setHoveredId(null)}
                                className="relative z-10"
                            >
                                <AnimateIcon animateOnHover>
                                    <button
                                        className="w-full text-left flex items-center gap-2 p-3 text-sm text-hyper-secondary hover:bg-hyper-primary-10 transition-colors rounded-lg"
                                        onClick={() => handleProfileMenuClick(action)}
                                    >
                                        <span className="w-4 h-4 flex-shrink-0">{icon}</span>
                                        <span>{label}</span>
                                    </button>
                                </AnimateIcon>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );

    const renderServerSidebarButtons = (customCollapsed?: boolean) => {
        const isCollapsed = typeof customCollapsed === 'boolean' ? customCollapsed : collapsed;
        return Object.entries(groupedServerMenuItems).map(([category, items]) => {
            // Check if any sub-item is active
            const isCategoryActive = items.some(item => isActive(item.path));
            // If a sub-item is active and no category is open, open this category by default
            const isOpen = openCategory === category || (!openCategory && isCategoryActive);
            const categoryIcon = categoryIcons[category] || <Layers size={18} />;
            return (
                <div key={category}>
                    <div
                        className={
                            `max-h-12 h-12 flex items-center justify-between w-full p-3 rounded-lg text-hyper-secondary hover:bg-hyper-sidebar-accent transition-colors cursor-pointer ` +
                            (isCategoryActive ? 'bg-hyper-primary-10 text-hyper-accent' : '')
                        }
                        onClick={() => setOpenCategory(isOpen ? null : category)}
                    >
                        <div className="flex items-center min-w-auto">
                            <div className="w-5 h-5 mr-3 flex-shrink-0 my-auto">{categoryIcon}</div>
                            <span className={`transition-opacity duration-300 ${isCollapsed ? 'opacity-0 w-0 overflow-hidden' : ''}`}>
                                {category}
                            </span>
                        </div>
                        <div className={`w-4 h-4 transition-transform duration-200 flex items-center justify-center ${isOpen && !isCollapsed ? 'rotate-90' : ''} ${isCollapsed ? 'opacity-0' : ''}`}>
                            <ChevronRight size={16} />
                        </div>
                    </div>
                    <div
                        className={`ml-4 border-l border-hyper-primary pl-4 transition-all duration-300 overflow-hidden ${isCollapsed ? 'opacity-0' : ''}`}
                        style={{
                            maxHeight: isOpen && !isCollapsed ? `${items.length * 56}px` : '0', // 56px per item
                        }}
                    >
                        <div className="h-2" />
                        {items.map(item =>
                            item.permission ? (
                                <Can key={item.path} action={item.permission} matchAny>
                                    <SidebarButton
                                        uniqueKey={item.path}
                                        item={item}
                                        collapsed={isCollapsed}
                                        buttonRefs={buttonRefs}
                                        sidebarRootRef={sidebarRootRef}
                                        setHoveredRect={setHoveredRect}
                                        setHoveredId={setHoveredId}
                                        isActive={isActive}
                                        small
                                    />
                                </Can>
                            ) : (
                                <SidebarButton
                                    key={item.path}
                                    uniqueKey={item.path}
                                    item={item}
                                    collapsed={isCollapsed}
                                    buttonRefs={buttonRefs}
                                    sidebarRootRef={sidebarRootRef}
                                    setHoveredRect={setHoveredRect}
                                    setHoveredId={setHoveredId}
                                    isActive={isActive}
                                    small
                                />
                            )
                        )}
                    </div>
                </div>
            );
        });
    };

    // Mobile sidebar rendering
    if (isMobile) {
        // Always expanded in mobile view
        const mobileCollapsed = false;
        if (!show) return null;
        return (
            <div className="fixed inset-0 z-[200] bg-black bg-opacity-40 flex">
                <div className="relative mt-14 h-[calc(100%-3.5rem)] bg-hyper-sidebar backdrop-blur-lg border-r border-hyper-primary transition-all duration-300 flex flex-col z-[201] w-70">
                    <div className="flex-1 p-4 overflow-y-auto">
                        <div className="mb-6 overflow-x-hidden">
                            <div className="relative" ref={containerRef}>
                                {route === 'server'
                                    ? renderServerSidebarButtons(mobileCollapsed)
                                    : (
                                        <>
                                            {menuItems.map((item, index) => {
                                                const uniqueKey = `${item.name}-${index}`;
                                                return (
                                                    <SidebarButton
                                                        key={uniqueKey}
                                                        uniqueKey={uniqueKey}
                                                        item={item}
                                                        collapsed={mobileCollapsed}
                                                        buttonRefs={buttonRefs}
                                                        sidebarRootRef={sidebarRootRef}
                                                        setHoveredRect={setHoveredRect}
                                                        setHoveredId={setHoveredId}
                                                        isActive={isActive}
                                                    />
                                                );
                                            })}
                                            {/* Account dropdown */}
                                            <div
                                                ref={accountDropdownButtonRef}
                                                onMouseEnter={() => {
                                                    const button = accountDropdownButtonRef.current;
                                                    const container = sidebarRootRef.current;
                                                    if (button && container) {
                                                        const buttonRect = button.getBoundingClientRect();
                                                        const containerRect = container.getBoundingClientRect();
                                                        setHoveredRect({
                                                            top: buttonRect.top - containerRect.top,
                                                            height: buttonRect.height,
                                                            width: buttonRect.width,
                                                            left: buttonRect.left - containerRect.left,
                                                        });
                                                        setHoveredId(keyForAccountDropdown);
                                                    }
                                                }}
                                                onMouseLeave={() => setHoveredId(null)}
                                                className="relative z-10"
                                            >
                                                <Tooltip content="Account Settings" placement="right" visible={mobileCollapsed}>
                                                    <div>
                                                        <AnimateIcon animateOnHover>
                                                            <button
                                                                className={`max-h-12 h-12 flex items-center justify-between w-full p-3 rounded-lg text-hyper-secondary hover:bg-hyper-sidebar-accent transition-colors ${
                                                                    location.pathname.startsWith('/account') ? 'bg-hyper-primary-10 text-hyper-accent' : ''
                                                                }`}
                                                                onClick={() => {
                                                                    setAccountDropdownOpen(!accountDropdownOpen);
                                                                    history.push('/account');
                                                                }}
                                                            >
                                                                <div className="flex items-center min-w-auto">
                                                                    <div className="w-5 h-5 mr-3 flex-shrink-0 my-auto">
                                                                        <User size={20} />
                                                                    </div>
                                                                    <span
                                                                        className={`transition-opacity duration-300 ${
                                                                            mobileCollapsed ? 'opacity-0 w-0 overflow-hidden' : ''
                                                                        }`}
                                                                    >
                                                                        Account Settings
                                                                    </span>
                                                                </div>
                                                                <div
                                                                    className={`w-4 h-4 transition-transform duration-200 flex items-center justify-center ${
                                                                        accountDropdownOpen && !mobileCollapsed ? 'rotate-90' : ''
                                                                    } ${mobileCollapsed ? 'opacity-0' : ''}`}
                                                                >
                                                                    <ChevronRight size={16} />
                                                                </div>
                                                            </button>
                                                        </AnimateIcon>
                                                    </div>
                                                </Tooltip>
                                                <div
                                                    className={`ml-4 border-l border-hyper-primary pl-4 transition-all duration-300 overflow-hidden ${
                                                        mobileCollapsed ? 'opacity-0' : ''
                                                    }`}
                                                    style={{
                                                        maxHeight: accountDropdownOpen && !mobileCollapsed ? '232px' : '0',
                                                    }}
                                                >
                                                    <div className="h-2" />
                                                    {accountDropdownItems.map(item => (
                                                        <SidebarButton
                                                            key={item.path}
                                                            uniqueKey={item.path}
                                                            item={item}
                                                            collapsed={mobileCollapsed}
                                                            buttonRefs={buttonRefs}
                                                            sidebarRootRef={sidebarRootRef}
                                                            setHoveredRect={setHoveredRect}
                                                            setHoveredId={setHoveredId}
                                                            isActive={pathOrId =>
                                                                item.path === '/account'
                                                                    ? location.pathname === '/account'
                                                                    : location.pathname === item.path
                                                            }
                                                            small
                                                        />
                                                    ))}
                                                </div>
                                            </div>
                                            {/* Theme dropdown */}
                                            {rootAdmin && (
                                                <div
                                                    ref={themeDropdownButtonRef}
                                                    onMouseEnter={() => {
                                                        const button = themeDropdownButtonRef.current;
                                                        const container = sidebarRootRef.current;
                                                        if (button && container) {
                                                            const buttonRect = button.getBoundingClientRect();
                                                            const containerRect = container.getBoundingClientRect();
                                                            setHoveredRect({
                                                                top: buttonRect.top - containerRect.top,
                                                                height: buttonRect.height,
                                                                width: buttonRect.width,
                                                                left: buttonRect.left - containerRect.left,
                                                            });
                                                            setHoveredId(keyForThemeDropdown);
                                                        }
                                                    }}
                                                    onMouseLeave={() => setHoveredId(null)}
                                                    className="relative z-10"
                                                >
                                                    <Tooltip content="Theme Settings" placement="right" visible={mobileCollapsed}>
                                                        <div>
                                                            <AnimateIcon animateOnHover>
                                                                <button
                                                                    className={`max-h-12 h-12 flex items-center justify-between w-full p-3 rounded-lg text-hyper-secondary hover:bg-hyper-sidebar-accent transition-colors ${
                                                                        location.pathname.startsWith('/theme') ? 'bg-hyper-primary-10 text-hyper-accent' : ''
                                                                    }`}
                                                                    onClick={() => {
                                                                        setThemeDropdownOpen(!themeDropdownOpen);
                                                                        history.push('/theme/general');
                                                                    }}
                                                                >
                                                                    <div className="flex items-center min-w-auto">
                                                                        <div className="w-5 h-5 mr-3 flex-shrink-0 my-auto">
                                                                            <Settings size={20} />
                                                                        </div>
                                                                        <span
                                                                            className={`transition-opacity duration-300 ${
                                                                                mobileCollapsed ? 'opacity-0 w-0 overflow-hidden' : ''
                                                                            }`}
                                                                        >
                                                                            Theme Settings
                                                                        </span>
                                                                    </div>
                                                                    <div
                                                                        className={`w-4 h-4 transition-transform duration-200 flex items-center justify-center ${
                                                                            themeDropdownOpen && !mobileCollapsed ? 'rotate-90' : ''
                                                                        } ${mobileCollapsed ? 'opacity-0' : ''}`}
                                                                    >
                                                                        <ChevronRight size={16} />
                                                                    </div>
                                                                </button>
                                                            </AnimateIcon>
                                                        </div>
                                                    </Tooltip>
                                                    <div
                                                        className={`ml-4 border-l border-hyper-primary pl-4 transition-all duration-300 overflow-hidden ${
                                                            mobileCollapsed ? 'opacity-0' : ''
                                                        }`}
                                                        style={{
                                                            maxHeight: themeDropdownOpen && !mobileCollapsed ? '112px' : '0', // 56px per item * 2 items
                                                        }}
                                                    >
                                                        <div className="h-2" />
                                                        {themeDropdownItems.map(item => (
                                                            <SidebarButton
                                                                key={item.path}
                                                                uniqueKey={item.path}
                                                                item={item}
                                                                collapsed={mobileCollapsed}
                                                                buttonRefs={buttonRefs}
                                                                sidebarRootRef={sidebarRootRef}
                                                                setHoveredRect={setHoveredRect}
                                                                setHoveredId={setHoveredId}
                                                                isActive={pathOrId =>
                                                                    item.path === '/theme/general'
                                                                        ? location.pathname === '/theme/general' || location.pathname === '/theme'
                                                                        : location.pathname === item.path
                                                                }
                                                                small
                                                            />
                                                        ))}
                                                    </div>
                                                </div>
                                            )}
                                        </>
                                    )
                                }
                            </div>
                        </div>
                    </div>
                    <div className="p-4 border-t border-hyper-primary">
                        <AnimateIcon animateOnHover>
                            <div className={`flex items-center justify-between ${mobileCollapsed ? 'justify-center' : ''}`}>
                                <div className={`flex items-center flex-1 ${mobileCollapsed ? 'justify-center' : ''}`}>
                                    <div
                                        className="w-10 h-10 rounded-lg bg-hyper-accent flex items-center justify-center text-hyper-primary font-semibold"
                                        dangerouslySetInnerHTML={{
                                            __html: getAvatarSvg(user?.username || 'user'),
                                        }}
                                    />
                                    <div className={`ml-3 flex-1 transition-opacity duration-300 ${
                                        mobileCollapsed ? 'opacity-0 w-0 overflow-hidden' : ''
                                    }`}>
                                        <div className="text-sm font-medium text-hyper-secondary">
                                            {user?.username}
                                        </div>
                                        <div className="text-xs text-hyper-muted-foreground">
                                            {user?.email}
                                        </div>
                                    </div>
                                </div>
                                {renderProfileDropdown()}
                            </div>
                        </AnimateIcon>
                    </div>
                </div>
                <div className="flex-1" onClick={onClose} />
            </div>
        );
    }

    // Desktop sidebar rendering
    return (
        <div
            ref={sidebarRootRef}
            className={`fixed left-0 top-0 h-full bg-hyper-sidebar backdrop-blur-lg border-r border-hyper-primary transition-all duration-300 flex flex-col z-[100] overflow-visible ${
                collapsed ? 'w-20' : 'w-70'
            }`}
        >
            <AnimatePresence>
                {hoveredId && hoveredRect && !isActive(hoveredId) && (
                    <motion.div
                        layoutId="hover-sheet"
                        className="absolute bg-hyper-sidebar-accent rounded-lg z-0"
                        initial={false}
                        animate={{
                            top: hoveredRect.top,
                            left: hoveredRect.left,
                            height: hoveredRect.height,
                            width: hoveredRect.width,
                        }}
                        exit={{ opacity: 0 }}
                        transition={{
                            type: 'spring',
                            stiffness: 500,
                            damping: 40,
                            mass: 0.3,
                        }}
                    />
                )}
            </AnimatePresence>
            <div className="flex h-14 border-b border-hyper-primary">
                <div className="p-2 flex items-center w-full max-h-16 justify-start">
                    <div className="relative flex items-center h-6 w-[150px] overflow-hidden">
                        <a href="/">
                            <div
                                className={`absolute left-3 top-0 flex items-center justify-center h-6 w-6 transition-all duration-300 ease-in-out z-10 ${
                                    collapsed ? 'opacity-100 scale-100' : 'opacity-0 scale-90 pointer-events-none'
                                }`}
                            >
                                {themeSettings?.small_logo_enabled && themeSettings?.small_logo_url ? (
                                    <img 
                                        className="h-6" 
                                        src={themeSettings.small_logo_url} 
                                        alt="Collapsed Logo"
                                        style={{
                                            height: themeSettings.small_logo_height ? `${themeSettings.small_logo_height}px` : '24px',
                                            width: themeSettings.small_logo_width ? `${themeSettings.small_logo_width}px` : 'auto'
                                        }}
                                    />
                                ) : (
                                    <img className="h-6" src="/logo/small.png" alt="Collapsed Logo" />
                                )}
                            </div>
                            <div
                                className={`flex items-center h-6 ml-3 transition-all duration-300 ease-in-out z-0 ${
                                    collapsed
                                        ? 'opacity-0 scale-90 ml-0 w-0 overflow-hidden pointer-events-none'
                                        : 'opacity-100 scale-100 w-auto'
                                }`}
                            >
                                {themeSettings?.large_logo_enabled && themeSettings?.large_logo_url ? (
                                    <img 
                                        className="h-6" 
                                        src={themeSettings.large_logo_url} 
                                        alt="Expanded Logo"
                                        style={{
                                            height: themeSettings.large_logo_height ? `${themeSettings.large_logo_height}px` : '24px',
                                            width: themeSettings.large_logo_width ? `${themeSettings.large_logo_width}px` : 'auto'
                                        }}
                                    />
                                ) : (
                                    <img className="h-6" src="/logo/large.png" alt="Expanded Logo" />
                                )}
                            </div>
                        </a>
                    </div>
                </div>
            </div>
            <div className="flex-1 p-4 overflow-y-auto">
                <div className="mb-6 overflow-x-hidden">
                    <div className="relative" ref={containerRef}>
                        {renderSidebarButtons()}
                        {route !== 'server' && renderAccountDropdown()}
                        {route !== 'server' && rootAdmin && renderThemeDropdown()}
                    </div>
                </div>
            </div>
            <div className="p-4 border-t border-hyper-primary">
                <AnimateIcon animateOnHover>
                    <div className={`flex items-center justify-between ${collapsed ? 'justify-center' : ''}`}>
                        <div className={`flex items-center flex-1 ${collapsed ? 'justify-center' : ''}`}>
                            <div
                                className="w-10 h-10 rounded-lg bg-hyper-accent flex items-center justify-center text-hyper-primary font-semibold"
                                dangerouslySetInnerHTML={{
                                    __html: getAvatarSvg(user?.username || 'user'),
                                }}
                            />
                            <div className={`ml-3 flex-1 transition-opacity duration-300 ${
                                collapsed ? 'opacity-0 w-0 overflow-hidden' : ''
                            }`}>
                                <div className="text-sm font-medium text-hyper-secondary">
                                    {user?.username}
                                </div>
                                <div className="text-xs text-hyper-muted-foreground">
                                    {user?.email}
                                </div>
                            </div>
                        </div>
                        {renderProfileDropdown()}
                    </div>
                </AnimateIcon>
            </div>
        </div>
    );
};

export default Sidebar;