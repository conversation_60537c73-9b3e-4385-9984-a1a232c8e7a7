'use client';

import * as React from 'react';
import { motion, type Variants } from 'framer-motion';

import {
  getVariants,
  useAnimateIconContext,
  IconWrapper,
  type IconProps,
} from '../../components/icons-move/icon';

type ChevronUpDownProps = IconProps<keyof typeof animations>;

const animations = {
  default: {
    path1: {
      initial: {
        y: 0,
        transition: { duration: 0.3, ease: 'easeInOut' },
      },
      animate: {
        y: 3,
        transition: { duration: 0.3, ease: 'easeInOut' },
      },
    },
    path2: {
      initial: {
        y: 0,
        transition: { duration: 0.3, ease: 'easeInOut' },
      },
      animate: {
        y: -3,
        transition: { duration: 0.3, ease: 'easeInOut' },
      },
    },
  },
  'default-loop': {
    path1: {
      initial: {
        y: 0,
      },
      animate: {
        y: [0, 3, 0],
        transition: { duration: 0.6, ease: 'easeInOut' },
      },
    },
    path2: {
      initial: {
        y: 0,
      },
      animate: {
        y: [0, -3, 0],
        transition: { duration: 0.6, ease: 'easeInOut' },
      },
    },
  },
};

function IconComponent({ size, ...props }: ChevronUpDownProps) {
    const { controls } = useAnimateIconContext();
    const variants = getVariants(animations);
    const { animate, onAnimateChange, animateOnHover, animateOnTap, animation, loop, loopDelay, onAnimateStart, onAnimateEnd, tw, ...svgProps } = props;    

  return (
    <motion.svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
      {...svgProps}
    >
      <motion.path
        d="m7 15 5 5 5-5"
        variants={variants.path1}
        initial="initial"
        animate={controls}
      />
      <motion.path
        d="m7 9 5-5 5 5"
        variants={variants.path2}
        initial="initial"
        animate={controls}
      />
    </motion.svg>
  );
}

function ChevronUpDown(props: ChevronUpDownProps) {
    const { tw: _, ...filteredProps } = props;
    return <IconWrapper icon={IconComponent} {...filteredProps} />;
}

export {
  animations,
  ChevronUpDown,
  ChevronUpDown as ChevronUpDownIcon,
  type ChevronUpDownProps,
  type ChevronUpDownProps as ChevronUpDownIconProps,
};