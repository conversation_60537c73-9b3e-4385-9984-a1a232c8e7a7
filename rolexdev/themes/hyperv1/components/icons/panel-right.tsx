'use client';

import * as React from 'react';
import { motion, type Variants } from 'framer-motion';

import {
  getVariants,
  useAnimateIconContext,
  IconWrapper,
  type IconProps,
} from '../../components/icons-move/icon';

type PanelRightProps = IconProps<keyof typeof animations>;

const animations = {
  default: {
    rect: {},
    line: {
      initial: { x1: 15, y1: 3, x2: 15, y2: 21 },
      animate: {
        x1: 17,
        y1: 3,
        x2: 17,
        y2: 21,
        transition: { type: 'spring', damping: 20, stiffness: 200 },
      },
    },
  },
};

function IconComponent({ size, ...props }: PanelRightProps) {
  const { controls } = useAnimateIconContext();
  const variants = getVariants(animations);
  const { animate, onAnimateChange, animateOnHover, animateOnTap, animation, loop, loopDelay, onAnimateStart, onAnimateEnd, tw, ...svgProps } = props;

  return (
    <motion.svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
      {...svgProps}
    >
      <motion.rect
        width={18}
        height={18}
        x={3}
        y={3}
        rx={2}
        ry={2}
        variants={variants.rect}
        initial="initial"
        animate={controls}
      />
      <motion.line
        x1={15}
        y1={3}
        x2={15}
        y2={21}
        variants={variants.line}
        initial="initial"
        animate={controls}
      />
    </motion.svg>
  );
}

function PanelRight(props: PanelRightProps) {
  const { tw: _, ...filteredProps } = props;
    return <IconWrapper icon={IconComponent} {...filteredProps} />;
}

export {
  animations,
  PanelRight,
  PanelRight as PanelRightIcon,
  type PanelRightProps,
  type PanelRightProps as PanelRightIconProps,
};