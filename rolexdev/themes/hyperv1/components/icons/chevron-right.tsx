'use client';

import * as React from 'react';
import { motion, type Variants } from 'framer-motion';

import {
  getVariants,
  useAnimateIconContext,
  IconWrapper,
  type IconProps,
} from '../../components/icons-move/icon';

type ChevronRightProps = IconProps<keyof typeof animations>;

const animations = {
  default: {
    path: {
      initial: {
        x: 0,
        transition: { duration: 0.3, ease: 'easeInOut' },
      },
      animate: {
        x: 4,
        transition: { duration: 0.3, ease: 'easeInOut' },
      },
    },
  },
  'default-loop': {
    path: {
      initial: {
        x: 0,
      },
      animate: {
        x: [0, 4, 0],
        transition: { duration: 0.6, ease: 'easeInOut' },
      },
    },
  },
};

function IconComponent({ size, ...props }: ChevronRightProps) {
  const { controls } = useAnimateIconContext();
  const variants = getVariants(animations);
  const { animate, onAnimateChange, animateOnHover, animateOnTap, animation, loop, loopDelay, onAnimateStart, onAnimateEnd, tw, ...svgProps } = props;

  return (
    <motion.svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
      {...svgProps}
    >
      <motion.path
        d="m9 18 6-6-6-6"
        variants={variants.path}
        initial="initial"
        animate={controls}
      />
    </motion.svg>
  );
}

function ChevronRight(props: ChevronRightProps) {
    const { tw: _, ...filteredProps } = props;
    return <IconWrapper icon={IconComponent} {...filteredProps} />;
}

export {
  animations,
  ChevronRight,
  ChevronRight as ChevronRightIcon,
  type ChevronRightProps,
  type ChevronRightProps as ChevronRightIconProps,
};