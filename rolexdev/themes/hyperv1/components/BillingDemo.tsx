import React from 'react';
import { PageTransition, AnimatedCard } from '@rolexdev/themes/hyperv1/components/elements/animations';

const BillingDemo: React.FC = () => {
    return (
        <PageTransition>
            <div className="p-8">
                <AnimatedCard delay={0.1} variant="hover" className="bg-hyper-card rounded-lg p-6 text-hyper-secondary">
                    <h1 className="text-2xl font-bold text-hyper-primary mb-4">Billing Dashboard</h1>
                    <p className="text-hyper-muted-foreground mb-4">
                        This is a demo billing page to showcase the sidebar navigation.
                    </p>
                    <p className="text-hyper-muted-foreground mb-4">
                        Here you would see your current subscription, usage, and payment history.
                    </p>
                </AnimatedCard>
            </div>
        </PageTransition>
    );
};

export default BillingDemo;
