import React, { forwardRef } from 'react';
import { Form } from 'formik';
import styled from 'styled-components/macro';
import { breakpoint } from '@/theme';
import FlashMessageRender from '@/components/FlashMessageRender';
import tw from 'twin.macro';
import Button, { LinkButton } from '@rolexdev/themes/hyperv1/components/elements/Button';
type Props = React.DetailedHTMLProps<React.FormHTMLAttributes<HTMLFormElement>, HTMLFormElement> & {
    title?: string;
    button?: string;
    button_link?: string;
    text_button?: string;
    text_link?: string;
};


export default forwardRef<HTMLFormElement, Props>(({ title,button,button_link,text_button,text_link, ...props }, ref) => (
    <div css={tw`min-h-screen flex flex-col justify-center items-center px-4`}>
        <div css={tw`w-full max-w-6xl md:flex items-center justify-center gap-8`}>
            {/* Form section - left side */}
            <div css={tw`flex-1 max-w-md`}>
                <FlashMessageRender css={tw`mb-4`} />
                <div css={tw`w-full shadow-2xl rounded-2xl p-8 border`} className="bg-hyper-card backdrop-blur-lg border-hyper-primary">
                    <Form {...props} ref={ref} className='block'>
                            {/*Title */}
                            <div css={tw`text-center mb-8`}>
                                <h1 css={tw`text-3xl font-bold mb-2`} className="text-hyper-foreground">{title}</h1>
                            </div>

                            {/* Form Fields */}
                            <div css={tw`space-y-6`}>
                                {props.children}
                            </div>
                    </Form>
                    {button && (
                        <div css={tw`flex items-center my-6`}>
                            <div css={tw`flex-1 h-px`} className="bg-hyper-secondary"></div>
                            <span css={tw`px-2 text-sm`} className="text-hyper-muted-foreground bg-hyper-muted">OR</span>
                            <div css={tw`flex-1 h-px`} className="bg-hyper-secondary"></div>
                        </div>
                    )}
                    
                    {/* Dynamic Button */}
                    {button && (
                        <div css={tw`text-center mb-4`}>
                            <Button
                                href={button_link || '#'}
                                isSecondary={true}
                                size="xlarge"
                            >
                                {button}
                            </Button>
                        </div>
                    )}
                    
                    {/* Dynamic Text Link */}
                    {text_button && (
                        <div css={tw`text-center`}>
                            <LinkButton
                                href={text_link || '#'}
                                isSecondary={true}
                                size="small"
                            >
                                {text_button}
                            </LinkButton>
                        </div>
                    )}
                </div>
            </div>
        </div>
        <p css={tw`text-center text-xs mt-8`} className="text-hyper-muted-foreground">
            &copy; 2015 - {new Date().getFullYear()}&nbsp;
            <a
                rel={'noopener nofollow noreferrer'}
                href={'https://pterodactyl.io'}
                target={'_blank'}
                css={tw`no-underline hover:underline`}
                className="text-hyper-muted-foreground"
            >
                Pterodactyl Software
            </a>
        </p>
    </div>
));
