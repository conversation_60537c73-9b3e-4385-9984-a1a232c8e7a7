import { useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';

// Color variable mappings from CSS variables to backend fields
const colorVariableMappings: Record<string, string> = {
    '--hyper-primary': 'primary_color',
    '--hyper-primary-hover': 'primary_hover_color',
    '--hyper-secondary': 'secondary_color',
    '--hyper-accent': 'accent_color',
    '--hyper-background': 'background_color',
    '--hyper-card': 'card_color',
    '--hyper-muted': 'muted_color',
    '--hyper-destructive': 'destructive_color',
    '--hyper-sidebar': 'sidebar_color',
    '--hyper-glass': 'glass_color',
    '--hyper-tooltip': 'tooltip_color',
    '--hyper-text-primary': 'text_primary_color',
    '--hyper-text-secondary': 'text_secondary_color',
    '--hyper-text-foreground': 'text_foreground_color',
    '--hyper-text-muted': 'text_muted_color',
    '--hyper-text-destructive': 'text_destructive_color',
    '--hyper-destructive-text': 'destructive_text_color',
};

// Default color values
const defaultColors: Record<string, string> = {
    '--hyper-primary': '#df3050',
    '--hyper-primary-hover': '#e44b63',
    '--hyper-secondary': '#27272A',
    '--hyper-accent': '#292524',
    '--hyper-background': '#0C0A09',
    '--hyper-card': 'rgba(28, 25, 23, 0.48)',
    '--hyper-muted': '#262626',
    '--hyper-destructive': '#7F1D1D',
    '--hyper-sidebar': 'rgba(25, 25, 25, 0.86)',
    '--hyper-glass': 'rgba(135, 135, 135, 0.27)',
    '--hyper-tooltip': '#1f2937',
    '--hyper-text-primary': '#ffffff',
    '--hyper-text-secondary': '#FAFAFA',
    '--hyper-text-foreground': '#F2F2F2',
    '--hyper-text-muted': '#A1A1AA',
    '--hyper-text-destructive': '#FEF2F2',
    '--hyper-destructive-text': '#ff0000',
};

const ThemeApplicator: React.FC = () => {
    const { settings } = useTheme();

    // Function to update CSS variable
    const updateCSSVariable = (cssVar: string, value: string) => {
        document.documentElement.style.setProperty(cssVar, value);
    };

    // Apply CSS variables when settings change
    useEffect(() => {
        Object.entries(colorVariableMappings).forEach(([cssVar, backendField]) => {
            let value = defaultColors[cssVar]; // Default fallback
            
            if (settings && settings[backendField as keyof typeof settings]) {
                const settingValue = settings[backendField as keyof typeof settings] as string;
                if (settingValue && settingValue !== 'null' && settingValue !== 'undefined') {
                    value = settingValue;
                }
            }
            
            updateCSSVariable(cssVar, value);
        });
    }, [settings]);

    return null; // This component doesn't render anything
};

export default ThemeApplicator;