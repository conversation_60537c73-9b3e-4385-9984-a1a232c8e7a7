import * as React from 'react';
import ContentBox from '@/components/elements/ContentBox';
import UpdatePasswordForm from '@rolexdev/themes/hyperv1/components/dashboard/forms/UpdatePasswordForm';
import ConfigureTwoFactorForm from '@rolexdev/themes/hyperv1/components/dashboard/forms/ConfigureTwoFactorForm';
import PageContentBlock from '@rolexdev/themes/hyperv1/components/elements/PageContentBlock';
import tw from 'twin.macro';
import MessageBox from '@/components/MessageBox';
import { useLocation } from 'react-router-dom';
import UpdateInfoForm from '@rolexdev/addons/AccountInfoUpdate/UpdateInfoForm';
import { PageTransition, AnimatedContainer, AnimatedCard } from '@rolexdev/themes/hyperv1/components/elements/animations';



export default () => {
    const { state } = useLocation<undefined | { twoFactorRedirect?: boolean }>();

    return (
        <PageContentBlock title={'Account Settings'}>
            <PageTransition>
                <div className='w-full h-10'>
                    <h1 className='text-4xl font-bold text-hyper-primary'>Account Settings</h1>
                </div>
                {state?.twoFactorRedirect && (
                    <AnimatedCard delay={0.1} variant="default" className="mb-6">
                        <MessageBox title={'2-Factor Required'} type={'error'}>
                            Your account must have two-factor authentication enabled in order to continue.
                        </MessageBox>
                    </AnimatedCard>
                )}
                <AnimatedContainer 
                    variant="stagger"
                    staggerChildren={0.15}
                    className="grid gap-6 mb-10 mt-10 lg:mt-4"
                    style={{
                        gridTemplateColumns: 'repeat(auto-fit, minmax(340px, 1fr))',
                    }}
                >
                    <AnimatedCard delay={0.2} variant="hover">
                        <UpdateInfoForm />
                    </AnimatedCard>
                    <AnimatedCard delay={0.3} variant="hover">
                        <UpdatePasswordForm />
                    </AnimatedCard>
                    <AnimatedCard delay={0.4} variant="hover">
                        <ConfigureTwoFactorForm />
                    </AnimatedCard>
                </AnimatedContainer>
            </PageTransition>
        </PageContentBlock>
    );
};
