import React from 'react';
import Dialog from '@rolexdev/themes/hyperv1/components/elements/dialog/Dialog';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import CopyOnClick from '@/components/elements/CopyOnClick';

interface ApiKeyModalProps {
    apiKey: string;
    open: boolean;
    onClose: () => void;
}

const ApiKeyModal = ({ apiKey, open, onClose }: ApiKeyModalProps) => {
    if (!open) return null;

    return (
        <Dialog
            open={open}
            onClose={onClose}
            preventExternalClose
            hideCloseIcon
        >
            <div className='max-w-full w-fit text-hyper-primary rounded-xl bg-hyper-card backdrop-blur-lg border border-hyper-primary p-5 max-h-fit'>
                <div className='w-full h-10 mb-4'>
                    <h1 className='text-2xl font-bold text-hyper-primary'>Your API Key</h1>
                </div>
                <p className='text-sm text-hyper-accent text-center mt-2 mb-2'>
                    The API key you have requested is shown below. Please store this in a safe location, it will not be shown again.
                </p>
                <div className='flex items-center justify-center w-full mt-6'>
                    <CopyOnClick text={apiKey}>
                        <pre className='bg-hyper-glass text-hyper-primary font-medium rounded p-2 w-fit overflow-x-auto whitespace-nowrap'>
                            <code className='font-mono whitespace-nowrap'>{apiKey}</code>
                        </pre>
                    </CopyOnClick>
                </div>
                <div className='flex justify-end gap-2 mt-6'>
                    <Button size='small' onClick={onClose}>Close</Button>
                </div>
            </div>
        </Dialog>
    );
};

export default ApiKeyModal;