import React, { useContext, useEffect, useState } from 'react';
import { Dialog, DialogWrapperContext } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import getTwoFactorTokenData, { TwoFactorTokenData } from '@/api/account/getTwoFactorTokenData';
import { useFlashKey } from '@/plugins/useFlash';
import tw from 'twin.macro';
import QRCode from 'qrcode.react';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import Spinner from '@/components/elements/Spinner';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import CopyOnClick from '@/components/elements/CopyOnClick';
import enableAccountTwoFactor from '@/api/account/enableAccountTwoFactor';
import FlashMessageRender from '@/components/FlashMessageRender';
import { Actions, useStoreActions } from 'easy-peasy';
import { ApplicationStore } from '@rolexdev/themes/hyperv1/state';
import asDialog from '@rolexdev/themes/hyperv1/hoc/asDialog';
import { Formik, Form, FormikHelpers } from 'formik';
import * as Yup from 'yup';

interface Values {
    totp_code: string;
    totp_password: string;
}

interface Props {
    onTokens: (tokens: string[]) => void;
}

const schema = Yup.object().shape({
    totp_code: Yup.string()
        .required('Authenticator code is required.')
        .matches(/^\d{6}$/, 'Authenticator code must be 6 digits.'),
    totp_password: Yup.string().required('Account password is required.'),
});

const ConfigureTwoFactorForm = ({ onTokens }: Props) => {
    const [submitting, setSubmitting] = useState(false);
    const [token, setToken] = useState<TwoFactorTokenData | null>(null);
    const { clearAndAddHttpError } = useFlashKey('account:two-step');
    const updateUserData = useStoreActions((actions: Actions<ApplicationStore>) => actions.user.updateUserData);

    const { close, setProps } = useContext(DialogWrapperContext);

    useEffect(() => {
        getTwoFactorTokenData()
            .then(setToken)
            .catch((error) => clearAndAddHttpError(error));
    }, []);

    useEffect(() => {
        setProps((state) => ({ ...state, preventExternalClose: submitting }));
    }, [submitting]);

    const submit = async (
        values: Values,
        { setSubmitting }: FormikHelpers<Values>
    ) => {
        setSubmitting(true);
        clearAndAddHttpError();
        try {
            const tokens = await enableAccountTwoFactor(values.totp_code, values.totp_password);
            updateUserData({ useTotp: true });
            onTokens(tokens);
        } catch (error) {
            clearAndAddHttpError(error as any); // <-- Fix: cast error
        }
        setSubmitting(false);
    };

    return (
        <div className='max-w-fit w-fit text-hyper-primary rounded-xl bg-hyper-card backdrop-blur-lg border border-hyper-primary p-5 max-h-fit'>
            <div className='w-full h-10 mb-4'>
                <h1 className='text-2xl font-bold text-hyper-primary'>Enable Two-Step Verification</h1>
            </div>
            <FlashMessageRender byKey={'account:two-step'} className={'mt-4'} />
            <div className={'flex items-center justify-center w-56 h-56 p-2 bg-gray-50 shadow mx-auto mt-6 rounded-xl'}>
                {!token ? (
                    <Spinner />
                ) : (
                    <QRCode renderAs={'svg'} value={token.image_url_data} css={tw`w-full h-full shadow-none`} />
                )}
            </div>
            <CopyOnClick text={token?.secret}>
                <p className={'font-medium text-sm text-hyper-accent text-center mt-2'}>
                    {token?.secret.match(/.{1,4}/g)!.join(' ') || 'Loading...'}
                </p>
            </CopyOnClick>
            <p id={'totp-code-description'} className={'mt-6 text-xs'}>
                Scan the QR code above using the two-step authentication app of your choice. Then, enter the 6-digit
                code generated into the field below.
            </p>
            <Formik
                initialValues={{ totp_code: '', totp_password: '' }}
                validationSchema={schema}
                onSubmit={submit}
            >
                {({ isSubmitting, isValid }) => (
                    <Form>
                        <div className="mb-6 mt-3">
                            <Field
                                light
                                type="text"
                                name="totp_code"
                                label="Authenticator Code"
                                placeholder="000000"
                                disabled={isSubmitting || submitting}
                                autoComplete="one-time-code"
                                inputMode="numeric"
                                pattern="\d{6}"
                            />
                        </div>
                        <div className="mb-6">
                            <Field
                                light
                                type="password"
                                name="totp_password"
                                label="Account Password"
                                disabled={isSubmitting || submitting}
                            />
                        </div>
                        <div className='flex justify-end gap-2 mt-6'>
                            <Button size='small' isSecondary onClick={close} type="button" disabled={isSubmitting || submitting}>
                                Cancel
                            </Button>
                            <Button
                                disabled={!token || isSubmitting || submitting || !isValid}
                                type={'submit'}
                                size='small'
                            >
                                Enable
                            </Button>
                        </div>
                    </Form>
                )}
            </Formik>
        </div>
    );
};

export default asDialog({
})(ConfigureTwoFactorForm);