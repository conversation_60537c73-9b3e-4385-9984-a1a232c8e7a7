import React, { useContext, useEffect, useState } from 'react';
import { DialogWrapperContext } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import asDialog from '@rolexdev/themes/hyperv1/hoc/asDialog';
import { Formik, Form, FormikHelpers } from 'formik';
import * as Yup from 'yup';
import disableAccountTwoFactor from '@/api/account/disableAccountTwoFactor';
import { useFlashKey } from '@/plugins/useFlash';
import { useStoreActions } from '@rolexdev/themes/hyperv1/state/hooks';
import FlashMessageRender from '@/components/FlashMessageRender';

interface Values {
    totp_password: string;
}

const schema = Yup.object().shape({
    totp_password: Yup.string().required('Account password is required.'),
});

const DisableTOTPDialog = () => {
    const { clearAndAddHttpError } = useFlashKey('account:two-step');
    const { close, setProps } = useContext(DialogWrapperContext);
    const updateUserData = useStoreActions((actions) => actions.user.updateUserData);
    const [submitting, setSubmitting] = useState(false);

    useEffect(() => {
        setProps((state) => ({ ...state, preventExternalClose: submitting }));
    }, [submitting]);

    const submit = async (
        values: Values,
        { setSubmitting }: FormikHelpers<Values>
    ) => {
        setSubmitting(true);
        clearAndAddHttpError();
        try {
            await disableAccountTwoFactor(values.totp_password);
            updateUserData({ useTotp: false });
            close();
        } catch (error) {
            clearAndAddHttpError(error as any);
        }
        setSubmitting(false);
    };

    return (
        <div className='max-w-fit w-fit text-hyper-primary rounded-xl bg-hyper-card backdrop-blur-lg border border-hyper-primary p-5 max-h-fit'>
            <div className='w-full h-10 mb-1'>
                <h1 className='text-2xl font-bold text-hyper-primary'>Disable Two-Step Verification</h1>
            </div>
            <p className='text-xs text-hyper-accent mt-1 mb-4 font-medium'>
                Disabling two-step verification will make your account less secure.
            </p>
            <FlashMessageRender byKey={'account:two-step'} className='-mt-2 mb-6' />
            <Formik
                initialValues={{ totp_password: '' }}
                validationSchema={schema}
                onSubmit={submit}
            >
                {({ isSubmitting, isValid }) => (
                    <Form id="disable-totp-form" className="mt-2">
                        <Field
                            light
                            type="password"
                            name="totp_password"
                            label="Account Password"
                            disabled={isSubmitting || submitting}
                        />
                        <div className='flex justify-end gap-2 mt-6'>
                            <Button
                                size='small'
                                isSecondary
                                onClick={close}
                                type="button"
                                disabled={isSubmitting || submitting}
                            >
                                Cancel
                            </Button>
                            <Button
                                size='small'
                                type='submit'
                                form='disable-totp-form'
                                disabled={isSubmitting || submitting || !isValid}
                            >
                                Disable
                            </Button>
                        </div>
                    </Form>
                )}
            </Formik>
        </div>
    );
};

export default asDialog({
})(DisableTOTPDialog);