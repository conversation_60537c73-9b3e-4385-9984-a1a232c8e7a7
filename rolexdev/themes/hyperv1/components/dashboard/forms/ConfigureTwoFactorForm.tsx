import React, { useEffect, useState } from 'react';
import { useStoreState } from 'easy-peasy';
import { ApplicationStore } from '@rolexdev/themes/hyperv1/state';
import tw from 'twin.macro';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import SetupTOTPDialog from '@rolexdev/themes/hyperv1/components/dashboard/forms/SetupTOTPDialog';
import RecoveryTokensDialog from '@rolexdev/themes/hyperv1/components/dashboard/forms/RecoveryTokensDialog';
import DisableTOTPDialog from '@rolexdev/themes/hyperv1/components/dashboard/forms/DisableTOTPDialog';
import { useFlashKey } from '@/plugins/useFlash';

export default () => {
    const [tokens, setTokens] = useState<string[]>([]);
    const [visible, setVisible] = useState<'enable' | 'disable' | null>(null);
    const isEnabled = useStoreState((state: ApplicationStore) => state.user.data!.useTotp);
    const { clearAndAddHttpError } = useFlashKey('account:two-step');

    useEffect(() => {
        return () => {
            clearAndAddHttpError();
        };
    }, [visible]);

    const onTokens = (tokens: string[]) => {
        setTokens(tokens);
        setVisible(null);
    };

    return (
        <div className='mx-auto max-w-[380px] w-fit text-hyper-primary rounded-xl bg-hyper-card backdrop-blur-lg border border-hyper-primary p-5 max-h-fit'>
            <div className='w-full h-fit mb-4'>
                <h1 className='text-2xl font-bold text-hyper-primary'>Two-Factor Authentication</h1>
            </div>
            <SetupTOTPDialog open={visible === 'enable'} onClose={() => setVisible(null)} onTokens={onTokens} />
            <RecoveryTokensDialog tokens={tokens} open={tokens.length > 0} onClose={() => setTokens([])} />
            <DisableTOTPDialog open={visible === 'disable'} onClose={() => setVisible(null)} />
            <p className='text-sm text-hyper-accent'>
                {isEnabled
                    ? 'Two-step verification is currently enabled on your account.'
                    : 'You do not currently have two-step verification enabled on your account. Click the button below to begin configuring it.'}
            </p>
            <div className='mt-6 flex gap-2'>
                {isEnabled ? (
                    <Button isSecondary onClick={() => setVisible('disable')} size='xlarge'>Disable Two-Step</Button>
                ) : (
                    <Button onClick={() => setVisible('enable')} size='xlarge'>Enable Two-Step</Button>
                )}
            </div>
        </div>
    );
};
