import React from 'react';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import CopyOnClick from '@/components/elements/CopyOnClick';
import { Alert } from '@/components/elements/alert';
import Dialog from '@rolexdev/themes/hyperv1/components/elements/dialog/Dialog'

interface RecoveryTokenDialogProps {
    tokens: string[];
    open: boolean;
    onClose: () => void;
}

const RecoveryTokensDialog = ({ tokens, open, onClose }: RecoveryTokenDialogProps) => {
    const grouped = [] as [string, string][];
    tokens.forEach((token, index) => {
        if (index % 2 === 0) {
            grouped.push([token, tokens[index + 1] || '']);
        }
    });

    if (!open) return null;

    return (
        <Dialog 
            open={open}
            onClose={onClose}
            preventExternalClose
            hideCloseIcon
        >
        <div className='max-w-fit w-fit text-hyper-primary rounded-xl bg-hyper-card backdrop-blur-lg border border-hyper-primary p-5 max-h-fit'>
            <div className='w-full h-10 mb-4'>
                <h1 className='text-2xl font-bold text-hyper-primary'>Two-Step Authentication Enabled</h1>
            </div>
            <p className="text-sm text-hyper-accent text-center mt-2 mb-2">
                Store the codes below somewhere safe. If you lose access to your phone you can use these backup codes to sign in.
            </p>
            <div className='flex items-center justify-center w-full mt-6'>
                <CopyOnClick text={tokens.join('\n')} showInNotification={false}>
                    <pre className={'bg-hyper-glass text-hyper-primary font-medium rounded p-2 w-fit'}>
                        {grouped.map((value) => (
                            <span key={value.join('_')} className={'block'}>
                                {value[0]}
                                <span className={'mx-2 font-semibold'}>&nbsp;</span>
                                {value[1]}
                                <span className={'font-semibold'}>&nbsp;</span>
                            </span>
                        ))}
                    </pre>
                </CopyOnClick>
            </div>
            <Alert type={'danger'} className={'mt-3'}>
                These codes will not be shown again.
            </Alert>
            <div className='flex justify-end gap-2 mt-6'>
                <Button size='small' onClick={onClose}>Done</Button>
            </div>
        </div>
        </Dialog>
    );
};

export default RecoveryTokensDialog;