import React from 'react';
import { Formik, Form, FormikHelpers } from 'formik';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import * as Yup from 'yup';
import SpinnerOverlay from '@/components/elements/SpinnerOverlay';
import updateAccountPassword from '@/api/account/updateAccountPassword';
import { httpErrorToHuman } from '@/api/http';
import { useStoreState, useStoreActions } from 'easy-peasy';
import { ApplicationStore } from '@rolexdev/themes/hyperv1/state';

interface Values {
    current: string;
    password: string;
    confirmPassword: string;
}

const schema = Yup.object().shape({
    current: Yup.string().min(1).required('You must provide your current password.'),
    password: Yup.string().min(8).required(),
    confirmPassword: Yup.string().test(
        'password',
        'Password confirmation does not match the password you entered.',
        function (value) {
            return value === this.parent.password;
        }
    ),
});

export default () => {
    const user = useStoreState((state: ApplicationStore) => state.user.data);
    const { clearFlashes, addFlash } = useStoreActions((actions) => actions.flashes);

    if (!user) return null;

    const submit = async (values: Values, { setSubmitting }: FormikHelpers<Values>) => {
        clearFlashes('account:password');
        try {
            await updateAccountPassword({ ...values });
            // @ts-expect-error this is valid
            window.location = '/auth/login';
        } catch (error) {
            addFlash({
                key: 'account:password',
                type: 'error',
                title: 'Error',
                message: httpErrorToHuman(error),
            });
        }
        setSubmitting(false);
    };

    return (
        <div className='mx-auto max-w-[380px] w-fit text-hyper-primary rounded-xl bg-hyper-card backdrop-blur-lg border border-hyper-primary p-5 max-h-fit'>
            <div className='w-full h-fit mb-4'>
                <h1 className='text-2xl font-bold text-hyper-primary'>Update Password</h1>
            </div>
            <Formik
                onSubmit={submit}
                validationSchema={schema}
                initialValues={{ current: '', password: '', confirmPassword: '' }}
            >
                {({ isSubmitting, isValid }) => (
                    <Form>
                        <SpinnerOverlay size={'large'} visible={isSubmitting} />
                        <div className='mb-6'>
                            <Field
                                light
                                id='current_password'
                                type='password'
                                name='current'
                                label='Current Password'
                                disabled={isSubmitting}
                            />
                        </div>
                        <div className='mb-6'>
                            <Field
                                light
                                id='new_password'
                                type='password'
                                name='password'
                                label='New Password'
                                description='Your new password should be at least 8 characters in length and unique to this website.'
                                disabled={isSubmitting}
                            />
                        </div>
                        <div className='mb-6'>
                            <Field
                                light
                                id='confirm_new_password'
                                type='password'
                                name='confirmPassword'
                                label='Confirm New Password'
                                disabled={isSubmitting}
                            />
                        </div>
                        <div className=''>
                            <Button type='submit' size='xlarge' disabled={isSubmitting || !isValid}>
                                Update Password
                            </Button>
                        </div>
                    </Form>
                )}
            </Formik>
        </div>
    );
};