import React, { useState } from 'react';
import { Formik, Form, FormikHelpers } from 'formik';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import * as Yup from 'yup';
import SpinnerOverlay from '@/components/elements/SpinnerOverlay';
import createApiKey from '@/api/account/createApiKey';
import ApiKeyModal from '@rolexdev/themes/hyperv1/components/dashboard/ApiKeyModal';

interface Values {
    description: string;
    allowedIps: string;
}

const schema = Yup.object().shape({
    description: Yup.string().required('A description is required.').min(4, 'Must be at least 4 characters.'),
    allowedIps: Yup.string(),
});

export default ({ onKeyCreated }: { onKeyCreated: (key: any) => void }) => {
    const [apiKey, setApiKey] = useState('');


    const submit = async (values: Values, { setSubmitting, resetForm }: FormikHelpers<Values>) => {
        try {
            const { secretToken, ...key } = await createApiKey(values.description, values.allowedIps);
            resetForm();
            setSubmitting(false);
            setApiKey(`${key.identifier}${secretToken}`);
            onKeyCreated(key);
        } catch (error) {
            setSubmitting(false);
        }
    };

    return (
        <>
            <ApiKeyModal open={apiKey.length > 0} onClose={() => setApiKey('')} apiKey={apiKey} />
            <div className='mx-auto max-w-[380px] w-fit text-hyper-primary rounded-xl bg-hyper-card backdrop-blur-lg border border-hyper-primary p-5 max-h-fit'>
                <div className='w-full h-fit mb-4'>
                    <h1 className='text-2xl font-bold text-hyper-primary'>Create API Key</h1>
                </div>
                <Formik
                    onSubmit={submit}
                    validationSchema={schema}
                    initialValues={{ description: '', allowedIps: '' }}
                >
                    {({ isSubmitting, isValid }) => (
                        <Form>
                            <SpinnerOverlay size={'large'} visible={isSubmitting} />
                            <div className='mb-6'>
                                <Field
                                    light
                                    name='description'
                                    label='Description'
                                    description='A description of this API key.'
                                    disabled={isSubmitting}
                                />
                            </div>
                            <div className='mb-6'>
                                <Field
                                    light
                                    name='allowedIps'
                                    label='Allowed IPs'
                                    description='Leave blank to allow any IP address to use this API key, otherwise provide each IP address on a new line.'
                                    disabled={isSubmitting}
                                    multiline
                                />
                            </div>
                            <div>
                                <Button type='submit' size='xlarge' disabled={isSubmitting || !isValid}>
                                    Create
                                </Button>
                            </div>
                        </Form>
                    )}
                </Formik>
            </div>
        </>
    );
};