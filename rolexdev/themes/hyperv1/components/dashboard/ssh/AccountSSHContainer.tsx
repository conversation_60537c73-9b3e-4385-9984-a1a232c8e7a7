import React, { useEffect, useState } from 'react';
import CreateSSHKeyForm from '@rolexdev/themes/hyperv1/components/dashboard/ssh/CreateSSHKeyForm';
import { useSSHKeys } from '@/api/account/ssh-keys';
import SpinnerOverlay from '@/components/elements/SpinnerOverlay';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faKey, faTrashAlt } from '@fortawesome/free-solid-svg-icons';
import FlashMessageRender from '@/components/FlashMessageRender';
import { format } from 'date-fns';
import PageContentBlock from '@rolexdev/themes/hyperv1/components/elements/PageContentBlock';
import tw from 'twin.macro';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import { useFlashKey } from '@/plugins/useFlash';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { PageTransition, <PERSON><PERSON>ontaine<PERSON>, Animated<PERSON><PERSON>, AnimatedList } from '@rolexdev/themes/hyperv1/components/elements/animations';

export default () => {
    const [deleteFingerprint, setDeleteFingerprint] = useState('');
    const { clearAndAddHttpError } = useFlashKey('account');
    const { data: keys, isValidating, error, mutate } = useSSHKeys({
        revalidateOnMount: true,
        revalidateOnFocus: false,
    });

    useEffect(() => {
        if (error) clearAndAddHttpError(error as any);
    }, [error]);

    const doDeletion = async (fingerprint: string) => {
        clearAndAddHttpError();
        try {
            await import('@/api/account/ssh-keys').then(mod => mod.deleteSSHKey(fingerprint));
            mutate((prev: any) => (prev || []).filter((k: any) => k.fingerprint !== fingerprint), false);
        } catch (e) {
            clearAndAddHttpError(error as any);
        } finally {
            setDeleteFingerprint('');
        }
    };

    return (
       <PageContentBlock title={'SSH Keys'}>
            <PageTransition>
                <FlashMessageRender byKey={'account'} />
                <div className='w-full h-10'>
                    <h1 className='text-4xl font-bold text-hyper-primary'>Account SSH Keys</h1>
                </div>
                <AnimatedContainer
                    variant="stagger"
                    staggerChildren={0.15}
                    className="grid gap-6 mb-10 mt-10 lg:mt-4"
                    style={{
                        gridTemplateColumns: 'repeat(auto-fit, minmax(340px, 1fr))',
                    }}
                >
                    <AnimatedCard delay={0.2} variant="hover">
                        <CreateSSHKeyForm onKeyCreated={() => mutate()} />
                    </AnimatedCard>
                    <AnimatedCard delay={0.3} variant="hover" className='mx-auto w-full max-w-[600px] text-hyper-primary rounded-xl bg-hyper-card backdrop-blur-lg border border-hyper-primary p-5 max-h-fit'>
                        <div className='w-full h-fit mb-4'>
                            <h1 className='text-2xl font-bold text-hyper-primary'>SSH Keys</h1>
                        </div>
                        <SpinnerOverlay visible={!keys && isValidating} />
                        <Dialog open={!!deleteFingerprint} onClose={() => setDeleteFingerprint('')}>
                            <AnimatedCard variant="default" className='max-w-fit w-fit text-hyper-primary rounded-xl bg-hyper-card backdrop-blur-lg border border-hyper-primary p-5 max-h-fit'>
                                <div className='w-full h-fit mb-4'>
                                    <h1 className='text-2xl font-bold text-hyper-primary'>Delete SSH Key</h1>
                                </div>
                                <p className='text-sm text-hyper-primary mb-6'>
                                    This SSH key will be removed from your account and cannot be used anymore.
                                </p>
                                <div className='flex justify-end gap-2 mt-6'>
                                    <Button
                                        size='small'
                                        isSecondary
                                        onClick={() => setDeleteFingerprint('')}
                                        type='button'
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        size='small'
                                        color='red'
                                        onClick={() => doDeletion(deleteFingerprint)}
                                        type='button'
                                    >
                                        Delete Key
                                    </Button>
                                </div>
                            </AnimatedCard>
                        </Dialog>
                        {!keys || keys.length === 0 ? (
                            <p css={tw`text-center text-sm font-medium`} className='text-hyper-accent'>
                                {!keys ? 'Loading...' : 'No SSH Keys exist for this account.'}
                            </p>
                        ) : (
                            <AnimatedList variant="slide" staggerDelay={0.1}>
                                {keys.map((key: any, index: number) => (
                                    <div
                                        key={key.fingerprint}
                                        css={[tw`flex items-center`, index > 0 && tw`mt-2`]}
                                        className='bg-hyper-background-50 text-hyper-primary border border-hyper-accent rounded-lg backdrop-blur-lg p-3'
                                    >
                                        <FontAwesomeIcon icon={faKey} className='text-hyper-accent' />
                                        <div css={tw`ml-4 flex-1 overflow-hidden`} className='text-hyper-primary'>
                                            <p css={tw`text-sm break-words font-medium`}>{key.name}</p>
                                            <p css={tw`text-xs mt-1 font-mono truncate`}>SHA256</p>
                                            <p css={tw`text-2xs text-neutral-300 uppercase`}>
                                                Added on:&nbsp;
                                                {format(key.createdAt, 'MMM do, yyyy HH:mm')}
                                            </p>
                                        </div>
                                        <p css={tw`text-sm ml-4 hidden md:block`}>
                                            <code css={tw`font-mono py-1 px-2 rounded`} className='bg-hyper-glass text-hyper-primary'>
                                                {key.fingerprint.length > 15 ? key.fingerprint.slice(0, 15) + '…' : key.fingerprint}
                                            </code>
                                        </p>
                                        <button css={tw`ml-4 p-2 text-sm`} onClick={() => setDeleteFingerprint(key.fingerprint)}>
                                            <FontAwesomeIcon
                                                icon={faTrashAlt}
                                                css={tw`hover:text-red-500 transition-colors duration-150`}
                                                className='text-hyper-primary hover:text-red-500'
                                            />
                                        </button>
                                    </div>
                                ))}
                            </AnimatedList>
                        )}
                    </AnimatedCard>
                </AnimatedContainer>
            </PageTransition>
        </PageContentBlock>
    );
};