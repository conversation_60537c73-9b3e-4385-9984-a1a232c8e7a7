import React from 'react';
import { Formik, Form, FormikHelpers } from 'formik';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import * as Yup from 'yup';
import SpinnerOverlay from '@/components/elements/SpinnerOverlay';
import { useFlashKey } from '@/plugins/useFlash';
import { createSSHKey, useSSHKeys } from '@/api/account/ssh-keys';

interface Values {
    name: string;
    publicKey: string;
}

const schema = Yup.object().shape({
    name: Yup.string().required('You must provide a name for this SSH key.'),
    publicKey: Yup.string().required('You must provide your public SSH key.'),
});


interface Props {
    onKeyCreated?: () => void;
}

const CreateSSHKeyForm: React.FC<Props> = ({ onKeyCreated }) => {
    const { clearAndAddHttpError } = useFlashKey('account');
    const { mutate } = useSSHKeys();

    const submit = async (values: Values, { setSubmitting, resetForm }: FormikHelpers<Values>) => {
        clearAndAddHttpError();
        try {
            const key = await createSSHKey(values.name, values.publicKey);
            resetForm();
            mutate((data) => (data || []).concat(key));
            if (onKeyCreated) onKeyCreated();
        } catch (error) {
            clearAndAddHttpError(error as any);
        }
        setSubmitting(false);
    };

    return (
        <div className='w-full mx-auto max-w-[380px] min-w-[320px] text-hyper-primary rounded-xl bg-hyper-card backdrop-blur-lg border border-hyper-primary p-5 max-h-fit'>
            <div className='w-full h-fit mb-4'>
                <h1 className='text-2xl font-bold text-hyper-primary'>Add SSH Key</h1>
            </div>
            <Formik
                onSubmit={submit}
                validationSchema={schema}
                initialValues={{ name: '', publicKey: '' }}
            >
                {({ isSubmitting, isValid, values, handleChange, handleBlur }) => (
                    <Form>
                        <SpinnerOverlay size={'large'} visible={isSubmitting} />
                        <div className='mb-6'>
                            <Field
                                light
                                id='ssh_key_name'
                                name='name'
                                label='SSH Key Name'
                                disabled={isSubmitting}
                                value={values.name}
                                onChange={handleChange}
                                onBlur={handleBlur}
                            />
                        </div>
                        <div className='mb-6'>
                            <Field
                                light
                                id='ssh_public_key'
                                name='publicKey'
                                label='Public Key'
                                description='Enter your public SSH key.'
                                disabled={isSubmitting}
                                multiline
                                value={values.publicKey}
                                onChange={handleChange}
                                onBlur={handleBlur}
                            />
                        </div>
                        <div>
                            <Button type='submit' size='xlarge' disabled={isSubmitting || !isValid}>
                                Save
                            </Button>
                        </div>
                    </Form>
                )}
            </Formik>
        </div>
    );
};

export default CreateSSHKeyForm;