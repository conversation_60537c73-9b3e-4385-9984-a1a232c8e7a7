import React, { useEffect, useState, useMemo, useCallback, useRef } from 'react';
import { Server } from '@/api/server/getServer';
import getServers from '@rolexdev/themes/hyperv1/api/getServers';
import Spinner from '@/components/elements/Spinner';
import PageContentBlock from '@rolexdev/themes/hyperv1/components/elements/PageContentBlock';
import useFlash from '@/plugins/useFlash';
import { useStoreState } from 'easy-peasy';
import { usePersistedState } from '@/plugins/usePersistedState';
import tw from 'twin.macro';
import useSWR from 'swr';
import { PaginatedResult } from '@/api/http';
import PaginationFooter from '@rolexdev/themes/hyperv1/components/elements/table/PaginationFooter';
import { useLocation } from 'react-router-dom';
import ServerMiddleware from '@rolexdev/themes/hyperv1/components/dashboard/ServerMiddleware';
import { Server as ServerI<PERSON>, Joystick, FlaskConical, WholeWord, PcCase, MoveUp, MoveDown, ChevronDown, ChevronRight, RectangleVertical, RectangleHorizontal, Pin, PinOff } from 'lucide-react';
import getServerResourceUsage from '@/api/server/getServerResourceUsage';
import Tooltip from '@rolexdev/themes/hyperv1/components/elements/tooltip/Tooltip';
import useMobileView from '@rolexdev/themes/hyperv1/hooks/useMobileView';
import { AnimatePresence, motion } from 'framer-motion';

const STATUS_LABELS: Record<string, string> = {
    running: 'Online',
    starting: 'Online',
    stopping: 'Offline',
    offline: 'Offline',
    suspended: 'Suspended',
    installing: 'Other',
    restoring_backup: 'Other',
    transferring: 'Other',
    stopped: 'Offline',
    shutdown: 'Offline',
};

export default function DashboardContainer() {
    const mobileView = useMobileView(700);
    const { search } = useLocation();
    const defaultPage = Number(new URLSearchParams(search).get('page') || '1');
    const [page, setPage] = useState(!isNaN(defaultPage) && defaultPage > 0 ? defaultPage : 1);

    const { clearFlashes, clearAndAddHttpError } = useFlash();
    const uuid = useStoreState((state) => state.user.data!.uuid);
    const rootAdmin = useStoreState((state) => state.user.data!.rootAdmin);
    const [showOnlyAdmin, setShowOnlyAdmin] = usePersistedState(`${uuid}:show_all_servers`, false);

    const [perPage, setPerPage] = useState(5);

    const { data: servers, error } = useSWR<PaginatedResult<Server>>(
        ['/api/client/servers', showOnlyAdmin && rootAdmin, page, perPage],
        () => getServers({ page, perPage, type: showOnlyAdmin && rootAdmin ? 'admin' : undefined })
    );

    const [sorterCollapsed, setSorterCollapsed] = useState(false);
    const [sorterData, setSorterData] = useState<{
        statusFilters: typeof statusFiltersMemo,
        sortTypeFilters: typeof sortTypeFiltersMemo,
        serverCount: number,
    } | null>(null);

    const handlePageSelect = (newPage: number, newPerPage: number) => {
        setPage(newPage);
        setPerPage(newPerPage);
    };

    useEffect(() => {
        if (!servers) return;
        if (servers.pagination.currentPage > 1 && !servers.items.length) setPage(1);
    }, [servers?.pagination.currentPage]);

    useEffect(() => {
        window.history.replaceState(null, document.title, `/${page <= 1 ? '' : `?page=${page}`}`);
    }, [page]);

    useEffect(() => {
        if (error) clearAndAddHttpError({ key: 'dashboard', error });
        else clearFlashes('dashboard');
    }, [error]);

    // Live status polling
    const [liveStatuses, setLiveStatuses] = useState<Record<string, string>>({});
    useEffect(() => {
        if (sorterCollapsed || !servers?.items) return;
        let mounted = true;
        const fetchStatuses = async () => {
            const statusObj: Record<string, string> = {};
            await Promise.all(servers.items.map(async (server) => {
                try {
                    const data = await getServerResourceUsage(server.uuid);
                    statusObj[server.uuid] = data.status;
                } catch {
                    statusObj[server.uuid] = server.status || 'offline';
                }
            }));
            if (mounted) setLiveStatuses(statusObj);
        };
        fetchStatuses();
        const interval = setInterval(fetchStatuses, 5000);
        return () => {
            mounted = false;
            clearInterval(interval);
        };
    }, [servers?.items, sorterCollapsed]);

    // Memoize all derived data
    const {
        allCount,
        onlineCount,
        offlineCount,
        suspendedCount,
        otherCount,
        uniqueGames,
        uniqueVersions,
        uniqueNames,
        uniqueNodes,
    } = useMemo(() => {
        const items = servers?.items || [];
        const getStatusType = (server: Server) => {
            const status = typeof liveStatuses[server.uuid] === 'string'
                ? liveStatuses[server.uuid].toLowerCase()
                : (typeof server.status === 'string' ? server.status.toLowerCase() : '');
            return STATUS_LABELS[status] || 'Other';
        };
        return {
            allCount: items.length,
            onlineCount: items.filter(s => getStatusType(s) === 'Online').length,
            offlineCount: items.filter(s => getStatusType(s) === 'Offline').length,
            suspendedCount: items.filter(s => getStatusType(s) === 'Suspended').length,
            otherCount: items.filter(s => getStatusType(s) === 'Other').length,
            uniqueGames: new Set(items.map(s => s.nestId).filter(Boolean)),
            uniqueVersions: new Set(items.map(s => s.eggId).filter(Boolean)),
            uniqueNames: new Set(items.map(s => s.name).filter(Boolean)),
            uniqueNodes: new Set(items.map(s => s.node).filter(Boolean)),
        };
    }, [servers?.items, liveStatuses]);

    // Status and sort filters
    const statusFiltersMemo = useMemo(() => [
        { label: 'All Server', value: 'All', count: allCount, icon: <ServerIcon size={16} className='text-hyper-accent' /> },
        { label: 'Online', value: 'Online', count: onlineCount, icon: <span className="relative flex items-center mr-1"><span className="absolute inline-flex h-[14px] w-[14px] rounded-full opacity-75 bg-green-500 animate-ping"/><span className="relative inline-flex h-[14px] w-[14px] rounded-full bg-green-500"/></span> },
        { label: 'Offline', value: 'Offline', count: offlineCount, icon: <span className="relative flex items-center mr-1"><span className="absolute inline-flex h-[14px] w-[14px] rounded-full opacity-75 bg-red-500 animate-ping"/><span className="relative inline-flex h-[14px] w-[14px] rounded-full bg-red-500"/></span> },
        { label: 'Suspend', value: 'Suspended', count: suspendedCount, icon: <span className="relative flex items-center mr-1"><span className="absolute inline-flex h-[14px] w-[14px] rounded-full opacity-75 bg-yellow-400 animate-ping"/><span className="relative inline-flex h-[14px] w-[14px] rounded-full bg-yellow-400"/></span> },
        { label: 'Other', value: 'Other', count: otherCount, icon: <span className="relative flex items-center mr-1"><span className="absolute inline-flex h-[14px] w-[14px] rounded-full opacity-75 bg-blue-500 animate-ping"/><span className="relative inline-flex h-[14px] w-[14px] rounded-full bg-blue-500"/></span> },
    ].filter(f => f.count > 0), [allCount, onlineCount, offlineCount, suspendedCount, otherCount]);

    const sortTypeFiltersMemo = useMemo(() => [
        uniqueGames.size > 1 ? { label: 'By Game', value: 'Game', icon: <Joystick size={16} className='text-hyper-accent' /> } : null,
        uniqueVersions.size > 1 ? { label: 'By Version', value: 'Version', icon: <FlaskConical size={16} className='text-hyper-accent' /> } : null,
        uniqueNames.size > 1 ? { label: 'By Name', value: 'Name', icon: <WholeWord size={16} className='text-hyper-accent' /> } : null,
        uniqueNodes.size > 1 ? { label: 'By Node', value: 'Node', icon: <PcCase size={16} className='text-hyper-accent' /> } : null,
    ].filter((x): x is { label: string; value: string; icon: JSX.Element } => !!x), [uniqueGames.size, uniqueVersions.size, uniqueNames.size, uniqueNodes.size]);

    // State for filters
    const [statusFilter, setStatusFilter] = useState<'All' | 'Online' | 'Offline' | 'Suspended' | 'Other'>('All');
    const [sortType, setSortType] = useState<'Game' | 'Version' | 'Name' | 'Node'>('Game');
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

    // Memoized getStatusType for sorting/filtering
    const getStatusType = useCallback((server: Server) => {
        const status = typeof liveStatuses[server.uuid] === 'string'
            ? liveStatuses[server.uuid].toLowerCase()
            : (typeof server.status === 'string' ? server.status.toLowerCase() : '');
        return STATUS_LABELS[status] || 'Other';
    }, [liveStatuses]);

    // Sorting/filtering functions
    const sortServers = useCallback((servers: Server[]) => {
        let sorted = [...servers];
        switch (sortType) {
            case 'Game':
                sorted.sort((a, b) => (a.nestId ?? 0) - (b.nestId ?? 0));
                break;
            case 'Version':
                sorted.sort((a, b) => (a.eggId ?? 0) - (b.eggId ?? 0));
                break;
            case 'Name':
                sorted.sort((a, b) => a.name.localeCompare(b.name));
                break;
            case 'Node':
                sorted.sort((a, b) => (a.node ?? '').localeCompare(b.node ?? ''));
                break;
        }
        if (sortOrder === 'desc') sorted.reverse();
        return sorted;
    }, [sortType, sortOrder]);

    const filterServers = useCallback((servers: Server[]) => {
        if (statusFilter === 'All') return servers;
        return servers.filter(s => getStatusType(s) === statusFilter);
    }, [statusFilter, getStatusType]);

    // Handlers
    const handleClear = useCallback(() => {
        setStatusFilter('All');
        setSortType('Game');
        setSortOrder('asc');
    }, []);

    const handleAdminToggle = useCallback(() => setShowOnlyAdmin((s) => !s), [setShowOnlyAdmin]);

    // Update sorterData when expanded
    useEffect(() => {
        if (!sorterCollapsed) {
            setSorterData({
                statusFilters: statusFiltersMemo,
                sortTypeFilters: sortTypeFiltersMemo,
                serverCount: allCount,
            });
        }
    }, [sorterCollapsed, allCount, statusFiltersMemo, sortTypeFiltersMemo]);

    const [serverLayoutRaw, setServerLayout] = usePersistedState<'card' | 'banner' | 'row' | undefined>(`${uuid}:server_layout`, 'card');
    const serverLayout: 'card' | 'banner' | 'row' = serverLayoutRaw ?? 'card';
    const [userSelectedLayout, setUserSelectedLayout] = useState<'card' | 'banner' | 'row'>(serverLayout);
    
    const gridRef = useRef<HTMLDivElement>(null);
        const handleLayoutChange = (layout: 'card' | 'banner' | 'row') => {
        setUserSelectedLayout(layout);
        setServerLayout(layout);
    };

    useEffect(() => {
        const handleResize = () => {
            if (gridRef.current) {
                const width = gridRef.current.offsetWidth;
                // Only auto-switch if user selected 'row'
                if (userSelectedLayout === 'row') {
                    if (width < 1200 && serverLayout === 'row') {
                        setServerLayout('banner');
                    } else if (width >= 1200 && serverLayout === 'banner') {
                        setServerLayout('row');
                    }
                }
            }
        };
    
        window.addEventListener('resize', handleResize);
        handleResize();
    
        return () => window.removeEventListener('resize', handleResize);
    }, [serverLayout, userSelectedLayout]);

    const [pinnedServers, setPinnedServers] = useState<string[]>(() => {
        try {
            const stored = localStorage.getItem(`${uuid}:pinned_servers`);
            return stored ? JSON.parse(stored) : [];
        } catch {
            return [];
        }
    });

    const handlePinServer = useCallback((serverUuid: string) => {
        setPinnedServers(prev => {
            const updated = prev.includes(serverUuid)
                ? prev.filter(uuid => uuid !== serverUuid)
                : [serverUuid, ...prev];
            localStorage.setItem(`${uuid}:pinned_servers`, JSON.stringify(updated));
            return updated;
        });
    }, [uuid]);

    return (
        <PageContentBlock title={'Dashboard'} showFlashKey={'dashboard'}>
            <div className='w-full h-10'>
                <h1 className='text-4xl font-bold text-hyper-primary'>Dashboard</h1>
            </div>
            <div className='w-full h-full justify-center items-center flex my-10 text-hyper-primary'>
                {(rootAdmin || allCount > 0) && (
                    <div
                        className={`transition-all duration-500 overflow-hidden ${!sorterCollapsed ? 'max-h-[350px] max-w-full opacity-100' : 'max-h-[60px] max-w-[400px] opacity-0'} bg-hyper-card backdrop-blur-[16px] rounded-2xl border border-hyper-accent p-3`}
                        style={{
                            transition: 'max-height 0.5s cubic-bezier(0.4,0,0.2,1), opacity 0.5s cubic-bezier(0.4,0,0.2,1)',
                            opacity: 1,
                            maxHeight: !sorterCollapsed ? 350 : 60,
                        }}
                    >
                        <div className='flex justify-between flex-wrap items-center'>
                            <div
                                className={`flex items-center cursor-pointer select-none ${!sorterCollapsed ? 'mb-3 mr-5' : rootAdmin ? 'mb-3 mr-5' : ''}`}
                                onClick={() => setSorterCollapsed((c) => !c)}
                            >

                                <h1 className='text-xl font-semibold'>Server Sorter</h1>
                                <span className='ml-2'>
                                    {sorterCollapsed
                                        ? <ChevronRight size={18} className='text-hyper-accent' />
                                        : <ChevronDown size={18} className='text-hyper-accent' />}
                                </span>
                                    
                            </div>
                            <div className='flex gap-3'>
                                {allCount > 0 && !sorterCollapsed && (
                                    <div className='flex gap-3 mt-auto mb-3'>
                                        <div
                                            className='px-2 button-primary bg-hyper-text-primary text-hyper-accent rounded-lg justify-center items-center flex min-w-[80px] h-[20px] cursor-pointer'
                                            onClick={handleClear}
                                        >
                                            <span className='font-semibold text-sm'>CLEAR</span>
                                        </div>
                                    </div>
                                )}
                                {rootAdmin && (
                                    <div className='flex gap-3 mt-auto mb-3'>
                                        <div
                                            className='px-2 button-primary bg-hyper-text-primary text-hyper-accent rounded-lg justify-center items-center flex min-w-[190px] h-[20px] cursor-pointer'
                                            onClick={handleAdminToggle}
                                        >
                                            <span className='font-semibold text-sm'>
                                                {showOnlyAdmin ? "Showing Others Servers" : "Showing Your Servers"}
                                            </span>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                        {!sorterCollapsed && sorterData && sorterData.serverCount > 0 && (
                            <div className='flex text-sm font-semibold mb-3 justify-center gap-2 flex-wrap leading-loose mt-4'>
                                {sorterData.statusFilters.map(({ label, value, count, icon }) => (
                                    <div
                                        key={value}
                                        className={`flex justify-between px-2 items-center rounded-xl min-h-[32px] min-w-[100px] cursor-pointer bg-hyper-glass ${statusFilter === value ? 'border border-hyper-accent' : 'button-primary'}`}
                                        onClick={() => setStatusFilter(value as any)}
                                    >
                                        <span className='mr-2'>{icon}</span>
                                        <span className='mr-2'>{label} :</span>
                                        <span>{count}</span>
                                    </div>
                                ))}
                                {sorterData.sortTypeFilters.map(({ label, value, icon }) => (
                                    <div
                                        key={value}
                                        className={`flex justify-between px-2 items-center rounded-xl min-h-[32px] min-w-[80px] cursor-pointer bg-hyper-glass ${sortType === value ? 'border border-hyper-accent' : 'button-primary'}`}
                                        onClick={() => setSortType(value as any)}
                                    >
                                        <span className='mr-2'>{icon}</span>
                                        <span className='mr-2'>{label}</span>
                                    </div>
                                ))}
                                {[
                                    { label: 'Ascending', value: 'asc', icon: <MoveUp size={16} className='text-hyper-accent' /> },
                                    { label: 'Descending', value: 'desc', icon: <MoveDown size={16} className='text-hyper-accent' /> },
                                ].map(({ label, value, icon }) => (
                                    <div
                                        key={value}
                                        className={`flex justify-between px-2 items-center rounded-xl min-h-[32px] min-w-[80px] cursor-pointer bg-hyper-glass ${sortOrder === value ? 'border border-hyper-accent' : 'button-primary'}`}
                                        onClick={() => setSortOrder(value as any)}
                                    >
                                        <span className='mr-2'>{icon}</span>
                                        <span className='mr-2'>{label}</span>
                                    </div>
                                ))}
                            </div>
                        )}
                        {!sorterCollapsed && sorterData && sorterData.serverCount > 0 && (
                        <div className='w-fit ml-auto flex gap-2 mt-auto mb-3 bg-hyper-glass p-2 rounded-xl'>
                            <div
                                className={`p-1 button-primary rounded-lg justify-center items-center flex cursor-pointer ${serverLayout === 'card' ? 'bg-hyper-primary text-hyper-primary border border-hyper-accent' : 'bg-hyper-text-primary text-hyper-accent'}`}
                                onClick={() => handleLayoutChange('card')}
                            >
                                <RectangleVertical size={12} strokeWidth={3}/>
                            </div>
                            <div
                                className={`p-1 button-primary rounded-lg justify-center items-center flex cursor-pointer ${serverLayout === 'banner' ? 'bg-hyper-primary text-hyper-primary border border-hyper-accent' : 'bg-hyper-text-primary text-hyper-accent'}`}
                                onClick={() => handleLayoutChange('banner')}
                            >
                                <RectangleHorizontal size={12} strokeWidth={3} />
                            </div>
                            <div
                                className={`p-1 button-primary rounded-lg justify-center items-center flex cursor-pointer ${serverLayout === 'row' ? 'bg-hyper-primary text-hyper-primary border border-hyper-accent' : 'bg-hyper-text-primary text-hyper-accent'}`}
                                onClick={() => handleLayoutChange('row')}
                            >
                                <RectangleHorizontal size={12} strokeWidth={3} />
                            </div>
                        </div>
                        )}
                    </div>
                )}
            </div>
            {!servers ? (
                <Spinner centered size={'large'} />
            ) : (
                <>
                    {/* Render your server grid as before, but without Pagination wrapper */}
                    {(() => {
                        const filtered = filterServers(servers.items);
                        const sorted = sortServers(filtered);
                        const pinned = sorted.filter(s => pinnedServers.includes(s.uuid));
                        const unpinned = sorted.filter(s => !pinnedServers.includes(s.uuid));
                        const orderedPinned = pinnedServers
                            .map(uuid => pinned.find(s => s.uuid === uuid))
                            .filter(Boolean) as Server[];
                        const finalSorted = [...orderedPinned, ...unpinned];
                        return sorted.length > 0 ? (
                            <div
                                ref={gridRef}
                                css={tw`grid min-w-0 gap-4`}
                                style={{
                                    gridTemplateColumns:
                                        serverLayout === 'row'
                                            ? '1fr'
                                            : `repeat(auto-fit, minmax(${
                                                serverLayout === 'banner' && mobileView
                                                    ? 300
                                                    : serverLayout === 'banner'
                                                    ? 600
                                                    : 300
                                            }px, 1fr))`
                                }}
                            >
                                <AnimatePresence>
                                    {finalSorted.map((server) => (
                                        <motion.div
                                            key={server.uuid}
                                            className='group server-card'
                                            css={tw`flex justify-center items-center min-w-0 relative`}
                                            style={{ position: 'relative' }}
                                            tabIndex={0}
                                            initial={{ opacity: 0, scale: 0.95, y: 20 }}
                                            animate={{ opacity: 1, scale: 1, y: 0 }}
                                            exit={{ opacity: 0, scale: 0.95, y: -20 }}
                                            transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                                            layout
                                            onMouseEnter={e => e.currentTarget.classList.add('show-pin')}
                                            onMouseLeave={e => e.currentTarget.classList.remove('show-pin')}
                                            onFocus={e => e.currentTarget.classList.add('show-pin')}
                                            onBlur={e => e.currentTarget.classList.remove('show-pin')}
                                        >
                                            {/* Pin Icon Overlay */}
                                            <Tooltip content={pinnedServers.includes(server.uuid) ? "Unpin Server" : "Pin Server"} placement='top' interactions={['hover']}>
                                                <div
                                                    style={{
                                                        position: 'absolute',
                                                        top: 8,
                                                        left: '50%',
                                                        transform: 'translateX(-50%)',
                                                        zIndex: 10,
                                                        transition: 'opacity 0.2s',
                                                        cursor: 'pointer',
                                                        background: 'rgba(0,0,0,0.2)',
                                                        borderRadius: '50%',
                                                        padding: 2,
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                    }}
                                                    className="server-pin-icon"
                                                    onClick={() => handlePinServer(server.uuid)}
                                                >
                                                    {pinnedServers.includes(server.uuid)
                                                        ? <PinOff size={22} className="text-hyper-accent" />
                                                        : <Pin size={22} className="text-hyper-accent" />}
                                                </div>
                                            </Tooltip>
                                            <ServerMiddleware server={server} layout={serverLayout} mobileView={serverLayout === 'banner' && mobileView} />
                                        </motion.div>
                                    ))}
                                </AnimatePresence>
                            </div>
                        ) : (
                            <p css={tw`text-center text-sm text-neutral-400`}>
                                {showOnlyAdmin
                                    ? 'There are no other servers to display.'
                                    : 'There are no servers associated with your account.'}
                            </p>
                        );
                    })()}
                    {servers.pagination.total > perPage && (
                        <PaginationFooter
                            className='mt-10'
                            pagination={servers.pagination}
                            onPageSelect={handlePageSelect}
                        />
                    )}
                </>
            )}
        </PageContentBlock>
    );
}