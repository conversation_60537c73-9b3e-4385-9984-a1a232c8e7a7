import React, { useState } from 'react';
import useEventListener from '@/plugins/useEventListener';
import SearchModal from '@rolexdev/themes/hyperv1/components/dashboard/search/SearchModal';
import { Search } from '@rolexdev/themes/hyperv1/components/icons/search';

interface Props {
    onSearchIconMouseEnter?: (e: React.MouseEvent<HTMLDivElement>) => void;
    onSearchIconMouseLeave?: () => void;
}

const SearchContainer: React.FC<Props> = (props) => {
    const [visible, setVisible] = useState(false);

    useEventListener('keydown', (e: KeyboardEvent) => {
        if (['input', 'textarea'].indexOf(((e.target as HTMLElement).tagName || 'input').toLowerCase()) < 0) {
            if (!visible && e.metaKey && e.key.toLowerCase() === '/') {
                setVisible(true);
            }
        }
    });

    return (
        <>
            {visible && <SearchModal appear visible={visible} onDismissed={() => setVisible(false)} />}
            <div
                className={'navigation-link'}
                onClick={() => setVisible(true)}
                onMouseEnter={props.onSearchIconMouseEnter}
                onMouseLeave={props.onSearchIconMouseLeave}
            >
                <Search size={20} animateOnHover />
            </div>
        </>
    );
};

export default SearchContainer;
