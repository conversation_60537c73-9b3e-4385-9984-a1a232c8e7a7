import React, { useEffect, useState } from 'react';
import ServerCard, { ServerCardProps } from '@rolexdev/themes/hyperv1/components/elements/ServerRows/ServerCard';
import ServerBanner from '@rolexdev/themes/hyperv1/components/elements/ServerRows/ServerBanner';
import ServerRow from '@rolexdev/themes/hyperv1/components/elements/ServerRows/ServerRow';
import { Server } from '@/api/server/getServer';
import getServerResourceUsage from '@/api/server/getServerResourceUsage';

interface ServerMiddlewareProps {
  server: Server;
  layout?: 'card' | 'banner' | 'row';
  mobileView?: boolean;
  hideManageButton?: boolean;
}

const statusMap: Record<string, string> = {
  running: 'Running',
  starting: 'Starting',
  stopping: 'Stopping',
  offline: 'Offline',
  suspended: 'Suspended',
  installing: 'Installing',
  restoring_backup: 'Restoring Backup',
  transferring: 'Transferring',
};


const ServerMiddleware: React.FC<ServerMiddlewareProps> = ({ server, layout = 'card', mobileView = false, hideManageButton = false }) => {
    const [liveStats, setLiveStats] = useState<{
        cpuUsagePercent?: number;
        memoryUsageInBytes?: number;
        diskUsageInBytes?: number;
      }>({});
    const [liveStatus, setLiveStatus] = useState<string | undefined>(undefined);
    const [liveCpu, setLiveCpu] = useState<string>('0');
    const [liveRam, setLiveRam] = useState<string>('0');
    const [liveDisk, setLiveDisk] = useState<string>('0');

    useEffect(() => {
      let mounted = true;
      let interval: NodeJS.Timeout | undefined;
        
      const fetchStats = () => {
        if (server.uuid) {
          getServerResourceUsage(server.uuid)
            .then((data) => {
              if (mounted) {
                setLiveStats({
                  cpuUsagePercent: data.cpuUsagePercent,
                  memoryUsageInBytes: data.memoryUsageInBytes,
                  diskUsageInBytes: data.diskUsageInBytes,
                });
                setLiveStatus(statusMap[data.status] || data.status.charAt(0).toUpperCase() + data.status.slice(1));
                setLiveCpu(data.cpuUsagePercent.toFixed(2)); // percent
                setLiveRam((data.memoryUsageInBytes / 1024 / 1024).toFixed(2)); // MB
                setLiveDisk((data.diskUsageInBytes / 1024 / 1024).toFixed(2)); // MB
              }
            })
            .catch(() => {
              if (mounted) {
                setLiveStats({});
                setLiveStatus(undefined);
                setLiveCpu('0');
                setLiveRam('0');
                setLiveDisk('0');
              }
            });
        }
      };
    
      fetchStats();
      interval = setInterval(fetchStats, 5000);
    
      return () => {
        mounted = false;
        if (interval) clearInterval(interval);
      };
    }, [server.uuid]);

    const cardProps: ServerCardProps = {
      uuid: server.uuid,
      name: server.name,
      description: server.description,
      status: liveStatus || (typeof server.status === 'string' ? (statusMap[server.status] || server.status.charAt(0).toUpperCase() + server.status.slice(1)) : 'Offline'),
      player: '0 / 100',
      ip: server.allocations && server.allocations.length > 0
        ? `${server.allocations[0].alias || server.allocations[0].ip}:${server.allocations[0].port}`
        : undefined,
      cpu: liveCpu,
      cpu_limit: server.limits?.cpu?.toString(),
      ram: liveRam,
      ram_limit: server.limits?.memory
        ? (server.limits.memory / 1024).toString()
        : undefined,
      disk: liveDisk,
      disk_limit: server.limits?.disk
        ? (server.limits.disk / 1024).toString()
        : undefined,
      egg_name: server.eggName,
      egg_id: server.eggId,
      nest_name: server.nestName,
      nest_id: server.nestId,
      liveStats,
    };

    if (layout === 'banner') {
      return <ServerBanner {...cardProps} mobileView={mobileView}/>;
    }
    if (layout === 'row') {
      return <ServerRow {...cardProps} mobileView={mobileView} hideManageButton={hideManageButton} />;
    }
    return <ServerCard {...cardProps} />;
};

export default ServerMiddleware;
