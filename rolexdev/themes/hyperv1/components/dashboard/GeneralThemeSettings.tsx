import React, { useState, useEffect, useMemo } from 'react';
import { useStoreState } from 'easy-peasy';
import { ApplicationStore } from '@/state';
import PageContentBlock from '../elements/PageContentBlock';
import { PageTransition, AnimatedContainer, AnimatedCard } from '../elements/animations';
import Button from '../elements/Button';
import Field from '../elements/Field';
import Label from '../elements/Label';
import { Settings, Save, RotateCcw, Image, ExternalLink, Eye, EyeOff } from 'lucide-react';
import { Formik, Form } from 'formik';
import SpinnerOverlay from '@/components/elements/SpinnerOverlay';
import { useTheme } from '../../contexts/ThemeContext';
import { ThemeSettings } from '../../api/admin/themeSettings';
import * as Yup from 'yup';
import { useStoreActions } from 'easy-peasy';
import PreviewPanel from './PreviewPanel';

const validationSchema = Yup.object({
    large_logo_url: Yup.string()
        .test('url-or-path', 'Must be a valid URL or path', (value) => {
            if (!value) return true; // Allow empty values
            // Allow relative paths starting with / or full URLs
            return value.startsWith('/') || Yup.string().url().isValidSync(value);
        })
        .nullable()
        .notRequired(),
    large_logo_width: Yup.number().min(10, 'Minimum width is 10px').max(1000, 'Maximum width is 1000px').required(),
    large_logo_height: Yup.number().min(10, 'Minimum height is 10px').max(300, 'Maximum height is 300px').required(),
    large_logo_enabled: Yup.boolean().required(),
    small_logo_url: Yup.string()
        .test('url-or-path', 'Must be a valid URL or path', (value) => {
            if (!value) return true; // Allow empty values
            // Allow relative paths starting with / or full URLs
            return value.startsWith('/') || Yup.string().url().isValidSync(value);
        })
        .nullable()
        .notRequired(),
    small_logo_width: Yup.number().min(10, 'Minimum width is 10px').max(200, 'Maximum width is 200px').required(),
    small_logo_height: Yup.number().min(10, 'Minimum height is 10px').max(200, 'Maximum height is 200px').required(),
    small_logo_enabled: Yup.boolean().required(),
    discord_url: Yup.string()
        .test('url-or-path', 'Must be a valid URL or path', (value) => {
            if (!value) return true; // Allow empty values
            // Allow relative paths starting with / or full URLs
            return value.startsWith('/') || Yup.string().url().isValidSync(value);
        })
        .nullable()
        .notRequired(),
    knowledge_base_url: Yup.string()
        .test('url-or-path', 'Must be a valid URL or path', (value) => {
            if (!value) return true; // Allow empty values
            // Allow relative paths starting with / or full URLs
            return value.startsWith('/') || Yup.string().url().isValidSync(value);
        })
        .nullable()
        .notRequired(),
    support_url: Yup.string()
        .test('url-or-path', 'Must be a valid URL or path', (value) => {
            if (!value) return true; // Allow empty values
            // Allow relative paths starting with / or full URLs
            return value.startsWith('/') || Yup.string().url().isValidSync(value);
        })
        .nullable()
        .notRequired(),
    billing_url: Yup.string()
        .test('url-or-path', 'Must be a valid URL or path', (value) => {
            if (!value) return true; // Allow empty values
            // Allow relative paths starting with / or full URLs
            return value.startsWith('/') || Yup.string().url().isValidSync(value);
        })
        .nullable()
        .notRequired(),
});

const GeneralThemeSettings: React.FC = () => {
    const rootAdmin = useStoreState((state: ApplicationStore) => state.user.data?.rootAdmin);
    const { addFlash } = useStoreActions((actions: any) => actions.flashes);
    const { 
        settings, 
        loading, 
        error, 
        updateSettings, 
        resetSettings, 
        setPreviewSettings, 
        previewSettings, 
        isPreviewMode,
        getCurrentSettings 
    } = useTheme();
    
    const [showPreview, setShowPreview] = useState(true);

    // Redirect non-admin users
    if (!rootAdmin) {
        return (
            <PageContentBlock title="Access Denied">
                <PageTransition>
                    <div className="w-full">
                        <div className="mx-auto max-w-lg text-center py-12">
                            <div className="mx-auto max-w-[380px] w-fit text-hyper-primary rounded-xl bg-hyper-card backdrop-blur-lg border border-hyper-primary p-8">
                                <h2 className="text-2xl font-bold text-hyper-primary mb-4">
                                    Access Denied
                                </h2>
                                <p className="text-hyper-muted-foreground">
                                    You don't have permission to access the theme settings. Only administrators can modify theme settings.
                                </p>
                            </div>
                        </div>
                    </div>
                </PageTransition>
            </PageContentBlock>
        );
    }

    if (loading) {
        return (
            <PageContentBlock title="General Theme Settings">
                <SpinnerOverlay visible={true} />
            </PageContentBlock>
        );
    }

    if (error || !settings) {
        return (
            <PageContentBlock title="General Theme Settings">
                <div className="text-center py-12">
                    <div className="text-red-400 mb-4">Error loading theme settings</div>
                    <p className="text-hyper-muted-foreground">{error}</p>
                </div>
            </PageContentBlock>
        );
    }

    const initialValues = {
        large_logo_url: settings.large_logo_url || '',
        large_logo_width: settings.large_logo_width,
        large_logo_height: settings.large_logo_height,
        large_logo_enabled: settings.large_logo_enabled,
        small_logo_url: settings.small_logo_url || '',
        small_logo_width: settings.small_logo_width,
        small_logo_height: settings.small_logo_height,
        small_logo_enabled: settings.small_logo_enabled,
        discord_url: settings.discord_url || '',
        knowledge_base_url: settings.knowledge_base_url || '',
        support_url: settings.support_url || '',
        billing_url: settings.billing_url || '',
    };

    const handleSubmit = async (values: typeof initialValues, { setSubmitting }: any) => {
        try {
            const payload = {
                ...values,
                large_logo_url: values.large_logo_url || null,
                small_logo_url: values.small_logo_url || null,
                discord_url: values.discord_url || null,
                knowledge_base_url: values.knowledge_base_url || null,
                support_url: values.support_url || null,
                billing_url: values.billing_url || null,
            };

            await updateSettings(payload);
            
            addFlash({
                type: 'success',
                title: 'Success',
                message: 'General theme settings saved successfully!',
            });
        } catch (error) {
            addFlash({
                type: 'error',
                title: 'Error',
                message: 'Failed to save general theme settings',
            });
        } finally {
            setSubmitting(false);
        }
    };

    const handleReset = async (resetForm: any) => {
        try {
            await resetSettings();
            resetForm();
            addFlash({
                type: 'success',
                title: 'Reset',
                message: 'Settings reset to defaults',
            });
        } catch (error) {
            addFlash({
                type: 'error',
                title: 'Error',
                message: 'Failed to reset settings',
            });
        }
    };

    return (
        <PageContentBlock title="General Theme Settings">
            <PageTransition>
                <div className="w-full h-10">
                    <h1 className="text-4xl font-bold text-hyper-primary flex items-center gap-3">
                        <Settings size={36} />
                        General Settings
                    </h1>
                </div>
                
                <p className="text-hyper-muted-foreground mt-4 mb-8">
                    Configure logos, external links, and basic theme settings. Changes are previewed in real-time on the right panel.
                </p>

                <div className="flex gap-6">
                    {/* Settings Panel - Left Side */}
                    <div className="flex-1 max-w-2xl">
                        <Formik
                            initialValues={initialValues}
                            validationSchema={validationSchema}
                            onSubmit={handleSubmit}
                            enableReinitialize={true}
                            validateOnChange={false}
                            validateOnBlur={false}
                            validateOnMount={false}
                        >
                            {({ isSubmitting, resetForm, values, setFieldValue, handleSubmit: formikHandleSubmit, errors, touched, submitForm, validateForm, setSubmitting }) => {
                                // Compute preview data using useMemo to avoid unnecessary recalculations
                                const previewData = useMemo(() => {
                                    if (!settings) return null;
                                    return {
                                        ...settings,
                                        ...values,
                                        large_logo_url: values.large_logo_url || null,
                                        small_logo_url: values.small_logo_url || null,
                                        discord_url: values.discord_url || null,
                                        knowledge_base_url: values.knowledge_base_url || null,
                                        support_url: values.support_url || null,
                                        billing_url: values.billing_url || null,
                                    };
                                }, [settings, values]);

                                // Update preview when computed data changes
                                useEffect(() => {
                                    if (showPreview && previewData) {
                                        setPreviewSettings(previewData);
                                    } else if (!showPreview) {
                                        setPreviewSettings(null);
                                    }
                                }, [showPreview, previewData, setPreviewSettings]);

                                return (
                                    <Form autoComplete="off">
                                        <SpinnerOverlay visible={isSubmitting} />
                                        
                                        <AnimatedContainer 
                                            variant="stagger"
                                            staggerChildren={0.1}
                                            className="space-y-6"
                                        >
                                            {/* Preview Toggle */}
                                            <AnimatedCard delay={0.1} variant="hover">
                                                <div className="bg-hyper-card backdrop-blur-lg border border-hyper-primary rounded-xl p-6">
                                                    <div className="flex items-center justify-between">
                                                        <div>
                                                            <h3 className="text-lg font-bold text-hyper-primary flex items-center gap-2">
                                                                <Eye size={18} />
                                                                Live Preview
                                                            </h3>
                                                            <p className="text-hyper-muted-foreground text-sm mt-1">
                                                                Preview changes in real-time
                                                            </p>
                                                        </div>
                                                        <button
                                                            type="button"
                                                            onClick={() => {
                                                                setShowPreview(!showPreview);
                                                                if (!showPreview) {
                                                                    // Enable preview - will be handled by useEffect
                                                                } else {
                                                                    setPreviewSettings(null);
                                                                }
                                                            }}
                                                            className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                                                                showPreview 
                                                                    ? 'bg-hyper-primary text-white' 
                                                                    : 'bg-hyper-background border border-hyper-primary text-hyper-primary hover:bg-hyper-primary-10'
                                                            }`}
                                                        >
                                                            {showPreview ? <Eye size={16} /> : <EyeOff size={16} />}
                                                            {showPreview ? 'Disable Preview' : 'Enable Preview'}
                                                        </button>
                                                    </div>
                                                </div>
                                            </AnimatedCard>

                                            {/* Large Logo Settings */}
                                            <AnimatedCard delay={0.2} variant="hover">
                                                <div className="bg-hyper-card backdrop-blur-lg border border-hyper-primary rounded-xl p-6">
                                                    <div className="mb-6">
                                                        <h3 className="text-xl font-bold text-hyper-primary flex items-center gap-2">
                                                            <Image size={20} />
                                                            Large Logo Settings
                                                        </h3>
                                                        <p className="text-hyper-muted-foreground text-sm mt-1">
                                                            Configure the main logo displayed in the sidebar and navigation
                                                        </p>
                                                    </div>
                                                    
                                                    <div className="space-y-4">
                                                        <div className="flex items-center justify-between">
                                                            <div>
                                                                <Label className="text-hyper-primary">Enable Large Logo</Label>
                                                                <p className="text-sm text-hyper-muted-foreground">
                                                                    Show/hide the large logo
                                                                </p>
                                                            </div>
                                                            <input
                                                                type="checkbox"
                                                                checked={values.large_logo_enabled}
                                                                onChange={(e) => setFieldValue('large_logo_enabled', e.target.checked)}
                                                                className="w-5 h-5 accent-hyper-primary"
                                                            />
                                                        </div>
                                                        
                                                        <Field
                                                            name="large_logo_url"
                                                            label="Large Logo URL"
                                                            placeholder="https://example.com/logo.png"
                                                            description="URL to the large logo image"
                                                            light={true}
                                                        />
                                                        
                                                        <div className="grid grid-cols-2 gap-4">
                                                            <Field
                                                                name="large_logo_width"
                                                                label="Width (px)"
                                                                type="number"
                                                                min="10"
                                                                max="1000"
                                                                light={true}
                                                            />
                                                            <Field
                                                                name="large_logo_height"
                                                                label="Height (px)"
                                                                type="number"
                                                                min="10"
                                                                max="300"
                                                                light={true}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            </AnimatedCard>

                                            {/* Small Logo Settings */}
                                            <AnimatedCard delay={0.3} variant="hover">
                                                <div className="bg-hyper-card backdrop-blur-lg border border-hyper-primary rounded-xl p-6">
                                                    <div className="mb-6">
                                                        <h3 className="text-xl font-bold text-hyper-primary flex items-center gap-2">
                                                            <Image size={20} />
                                                            Small Logo Settings
                                                        </h3>
                                                        <p className="text-hyper-muted-foreground text-sm mt-1">
                                                            Configure the compact logo for collapsed sidebar and mobile view
                                                        </p>
                                                    </div>
                                                    
                                                    <div className="space-y-4">
                                                        <div className="flex items-center justify-between">
                                                            <div>
                                                                <Label className="text-hyper-primary">Enable Small Logo</Label>
                                                                <p className="text-sm text-hyper-muted-foreground">
                                                                    Show/hide the small logo
                                                                </p>
                                                            </div>
                                                            <input
                                                                type="checkbox"
                                                                checked={values.small_logo_enabled}
                                                                onChange={(e) => setFieldValue('small_logo_enabled', e.target.checked)}
                                                                className="w-5 h-5 accent-hyper-primary"
                                                            />
                                                        </div>
                                                        
                                                        <Field
                                                            name="small_logo_url"
                                                            label="Small Logo URL"
                                                            placeholder="https://example.com/small-logo.png"
                                                            description="URL to the small logo image"
                                                            light={true}
                                                        />
                                                        
                                                        <div className="grid grid-cols-2 gap-4">
                                                            <Field
                                                                name="small_logo_width"
                                                                label="Width (px)"
                                                                type="number"
                                                                min="10"
                                                                max="200"
                                                                light={true}
                                                            />
                                                            <Field
                                                                name="small_logo_height"
                                                                label="Height (px)"
                                                                type="number"
                                                                min="10"
                                                                max="200"
                                                                light={true}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            </AnimatedCard>

                                            {/* External Links */}
                                            <AnimatedCard delay={0.4} variant="hover">
                                                <div className="bg-hyper-card backdrop-blur-lg border border-hyper-primary rounded-xl p-6">
                                                    <div className="mb-6">
                                                        <h3 className="text-xl font-bold text-hyper-primary flex items-center gap-2">
                                                            <ExternalLink size={20} />
                                                            External Links
                                                        </h3>
                                                        <p className="text-hyper-muted-foreground text-sm mt-1">
                                                            Configure external service URLs for sidebar navigation
                                                        </p>
                                                    </div>
                                                    
                                                    <div className="space-y-4">
                                                        <Field
                                                            name="discord_url"
                                                            label="Discord URL"
                                                            placeholder="https://discord.gg/your-server"
                                                            description="Link to your Discord server"
                                                            light={true}
                                                        />
                                                        
                                                        <Field
                                                            name="knowledge_base_url"
                                                            label="Knowledge Base URL"
                                                            placeholder="https://docs.example.com"
                                                            description="Link to your documentation or knowledge base"
                                                            light={true}
                                                        />
                                                        
                                                        <Field
                                                            name="support_url"
                                                            label="Support URL"
                                                            placeholder="https://support.example.com"
                                                            description="Link to your support portal"
                                                            light={true}
                                                        />
                                                        
                                                        <Field
                                                            name="billing_url"
                                                            label="Billing URL"
                                                            placeholder="https://billing.example.com"
                                                            description="Link to your billing or payment portal"
                                                            light={true}
                                                        />
                                                    </div>
                                                </div>
                                            </AnimatedCard>

                                            {/* Action Buttons */}
                                            <AnimatedCard delay={0.5} variant="hover">
                                                <div className="bg-hyper-card backdrop-blur-lg border border-hyper-primary rounded-xl p-6">
                                                    <div className="flex items-center justify-between gap-4">
                                                        <button 
                                                            type="button"
                                                            onClick={() => handleReset(resetForm)}
                                                            className="text-hyper-muted-foreground hover:text-hyper-primary transition-colors flex items-center gap-2 font-medium"
                                                        >
                                                            <RotateCcw size={16} />
                                                            Reset to Defaults
                                                        </button>
                                        <Button
                                                            type="button"
                                                            disabled={isSubmitting}
                                                            className="flex items-center gap-2"
                                                            onClick={async (e) => {
                                                                // Validate the form first
                                                                const validationErrors = await validateForm();
                                                                
                                                                if (Object.keys(validationErrors).length > 0) {
                                                                    return;
                                                                }
                                                                
                                                                // Directly call our handleSubmit function
                                                                await handleSubmit(values, { setSubmitting });
                                                            }}
                                                        >
                                                            {isSubmitting ? (
                                                                <>
                                                                    <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full" />
                                                                    Saving...
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <Save size={16} />
                                                                    Save Settings
                                                                </>
                                                            )}
                                                        </Button>
                                                    </div>
                                                </div>
                                            </AnimatedCard>
                                        </AnimatedContainer>
                                    </Form>
                                );
                            }}
                        </Formik>
                    </div>

                    {/* Preview Panel - Right Side */}
                    {showPreview && (
                        <div className="w-96 flex-shrink-0">
                            <div className="sticky top-6">
                                <PreviewPanel />
                            </div>
                        </div>
                    )}
                </div>
            </PageTransition>
        </PageContentBlock>
    );
};

export default GeneralThemeSettings;