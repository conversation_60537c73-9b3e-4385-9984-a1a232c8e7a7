import React, { useEffect, useState } from 'react';
import CreateApiKeyForm from '@rolexdev/themes/hyperv1/components/dashboard/forms/CreateApiKeyForm';
import getApiKeys, { ApiKey } from '@/api/account/getApiKeys';
import SpinnerOverlay from '@/components/elements/SpinnerOverlay';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faKey, faTrashAlt } from '@fortawesome/free-solid-svg-icons';
import deleteApiKey from '@/api/account/deleteApiKey';
import FlashMessageRender from '@/components/FlashMessageRender';
import { format } from 'date-fns';
import PageContentBlock from '@rolexdev/themes/hyperv1/components/elements/PageContentBlock';
import tw from 'twin.macro';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import { useFlashKey } from '@/plugins/useFlash';
import Code from '@/components/elements/Code';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { PageTransition, AnimatedContainer, AnimatedCard, AnimatedList } from '@rolexdev/themes/hyperv1/components/elements/animations';

export default () => {
    const [deleteIdentifier, setDeleteIdentifier] = useState('');
    const [keys, setKeys] = useState<ApiKey[]>([]);
    const [loading, setLoading] = useState(true);
    const { clearAndAddHttpError } = useFlashKey('account');

    useEffect(() => {
        getApiKeys()
            .then((keys) => setKeys(keys))
            .then(() => setLoading(false))
            .catch((error) => clearAndAddHttpError(error));
    }, []);

    const doDeletion = (identifier: string) => {
        setLoading(true);

        clearAndAddHttpError();
        deleteApiKey(identifier)
            .then(() => setKeys((s) => [...(s || []).filter((key) => key.identifier !== identifier)]))
            .catch((error) => clearAndAddHttpError(error))
            .then(() => {
                setLoading(false);
                setDeleteIdentifier('');
            });
    };

    return (
        <PageContentBlock title={'Account API'}>
            <PageTransition>
                <FlashMessageRender byKey={'account'} />
                <div className='w-full h-10'>
                    <h1 className='text-4xl font-bold text-hyper-primary'>Account API</h1>
                </div>
                <AnimatedContainer
                    variant="stagger"
                    staggerChildren={0.15}
                    className="grid gap-6 mb-10 mt-10 lg:mt-4"
                    style={{
                        gridTemplateColumns: 'repeat(auto-fit, minmax(340px, 1fr))',
                    }}
                >
                    <AnimatedCard delay={0.2} variant="hover">
                        <CreateApiKeyForm onKeyCreated={(key) => setKeys((s) => [...s!, key])} />
                    </AnimatedCard>
                    <AnimatedCard delay={0.3} variant="hover" className='mx-auto w-full max-w-[600px] text-hyper-primary rounded-xl bg-hyper-card backdrop-blur-lg border border-hyper-primary p-5 max-h-fit'>
                        <div className='w-full h-fit mb-4'>
                            <h1 className='text-2xl font-bold text-hyper-primary'>API Keys</h1>
                        </div>
                        <SpinnerOverlay visible={loading} />
                        <Dialog open={!!deleteIdentifier} onClose={() => setDeleteIdentifier('')}>
                            <AnimatedCard variant="default" className='max-w-fit w-fit text-hyper-primary rounded-xl bg-hyper-card backdrop-blur-lg border border-hyper-primary p-5 max-h-fit'>
                                <div className='w-full h-fit mb-4'>
                                    <h1 className='text-2xl font-bold text-hyper-primary'>Delete API Key</h1>
                                </div>
                                <p className='text-sm text-hyper-primary mb-6'>
                                    All requests using the <Code className='text-hyper-accent bg-hyper-glass font-medium'>{deleteIdentifier}</Code> key will be invalidated.
                                </p>
                                <div className='flex justify-end gap-2 mt-6'>
                                    <Button
                                        size='small'
                                        isSecondary
                                        onClick={() => setDeleteIdentifier('')}
                                        type='button'
                                        disabled={loading}
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        size='small'
                                        color='red'
                                        onClick={() => doDeletion(deleteIdentifier)}
                                        type='button'
                                        disabled={loading}
                                    >
                                        Delete Key
                                    </Button>
                                </div>
                            </AnimatedCard>
                        </Dialog>
                        {keys.length === 0 ? (
                            <p css={tw`text-center text-sm font-medium`} className='text-hyper-accent'>
                                {loading ? 'Loading...' : 'No API keys exist for this account.'}
                            </p>
                        ) : (
                            <AnimatedList variant="slide" staggerDelay={0.1}>
                                {keys.map((key, index) => (
                                    <div
                                        key={key.identifier}
                                        css={[tw`flex items-center`, index > 0 && tw`mt-2`]}
                                        className='bg-hyper-background-50 text-hyper-primary border border-hyper-accent rounded-lg backdrop-blur-lg p-3'
                                    >
                                        <FontAwesomeIcon icon={faKey} className='text-hyper-accent' />
                                        <div css={tw`ml-4 flex-1 overflow-hidden`} className='text-hyper-primary'>
                                            <p css={tw`text-sm break-words`} className='text-hyper-primary'>{key.description}</p>
                                            <p css={tw`text-2xs text-neutral-300 uppercase`}>
                                                Last used:&nbsp;
                                                {key.lastUsedAt ? format(key.lastUsedAt, 'MMM do, yyyy HH:mm') : 'Never'}
                                            </p>
                                        </div>
                                        <p css={tw`text-sm ml-4 hidden md:block`}>
                                            <code css={tw`font-mono py-1 px-2 rounded`} className='bg-hyper-glass text-hyper-primary'>{key.identifier}</code>
                                        </p>
                                        <button css={tw`ml-4 p-2 text-sm`} onClick={() => setDeleteIdentifier(key.identifier)}>
                                            <FontAwesomeIcon
                                                icon={faTrashAlt}
                                                css={tw`hover:text-red-500 transition-colors duration-150`}
                                                className='text-hyper-primary hover:text-red-500'
                                            />
                                        </button>
                                    </div>
                                ))}
                            </AnimatedList>
                        )}
                    </AnimatedCard>
                </AnimatedContainer>
            </PageTransition>
        </PageContentBlock>
    );
};
