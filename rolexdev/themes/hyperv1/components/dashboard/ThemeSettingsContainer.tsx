import React, { useEffect } from 'react';
import { useStoreState } from 'easy-peasy';
import { ApplicationStore } from '@/state';
import { useHistory } from 'react-router-dom';
import PageContentBlock from '@rolexdev/themes/hyperv1/components/elements/PageContentBlock';
import { PageTransition } from '@rolexdev/themes/hyperv1/components/elements/animations';
import { Palette } from 'lucide-react';

const ThemeSettingsContainer: React.FC = () => {
    const rootAdmin = useStoreState((state: ApplicationStore) => state.user.data?.rootAdmin);
    const history = useHistory();

    // Redirect to general settings
    useEffect(() => {
        if (rootAdmin) {
            history.push('/theme/general');
        }
    }, [rootAdmin, history]);

    // Redirect non-admin users
    if (!rootAdmin) {
        return (
            <PageContentBlock title="Access Denied">
                <PageTransition>
                    <div className="w-full">
                        <div className="mx-auto max-w-lg text-center py-12">
                            <div className="mx-auto max-w-[380px] w-fit text-hyper-primary rounded-xl bg-hyper-card backdrop-blur-lg border border-hyper-primary p-8">
                                <h2 className="text-2xl font-bold text-hyper-primary mb-4">
                                    Access Denied
                                </h2>
                                <p className="text-hyper-muted-foreground">
                                    You don't have permission to access the theme settings. Only administrators can modify theme settings.
                                </p>
                            </div>
                        </div>
                    </div>
                </PageTransition>
            </PageContentBlock>
        );
    }

    // Show loading while redirecting
    return (
        <PageContentBlock title="Theme Settings">
            <PageTransition>
                <div className="w-full h-10">
                    <h1 className="text-4xl font-bold text-hyper-primary flex items-center gap-3">
                        <Palette size={36} />
                        Redirecting to General Settings...
                    </h1>
                </div>
            </PageTransition>
        </PageContentBlock>
    );
};

export default ThemeSettingsContainer;