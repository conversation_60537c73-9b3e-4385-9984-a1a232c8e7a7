import React from 'react';
import { Monitor, Sidebar, Navigation, ExternalLink, Image as ImageIcon } from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';

const PreviewPanel: React.FC = () => {
    const { getCurrentSettings, isPreviewMode } = useTheme();
    const settings = getCurrentSettings();

    if (!settings) {
        return (
            <div className="bg-hyper-card backdrop-blur-lg border border-hyper-primary rounded-xl p-6">
                <div className="text-center text-hyper-muted-foreground">
                    Loading preview...
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {/* Preview Header */}
            <div className="bg-hyper-card backdrop-blur-lg border border-hyper-primary rounded-xl p-4">
                <div className="flex items-center gap-2 mb-2">
                    <Monitor size={18} className="text-hyper-primary" />
                    <h3 className="font-bold text-hyper-primary">Live Preview</h3>
                    {isPreviewMode && (
                        <span className="text-xs bg-hyper-primary text-white px-2 py-1 rounded-full">
                            Preview Mode
                        </span>
                    )}
                </div>
                <p className="text-xs text-hyper-muted-foreground">
                    This shows how your settings will appear in the interface
                </p>
            </div>

            {/* Sidebar Preview */}
            <div className="bg-hyper-card backdrop-blur-lg border border-hyper-primary rounded-xl p-4">
                <div className="flex items-center gap-2 mb-3">
                    <Sidebar size={16} className="text-hyper-primary" />
                    <h4 className="font-semibold text-hyper-primary">Sidebar Preview</h4>
                </div>
                
                <div className="bg-hyper-sidebar border border-hyper-primary rounded-lg p-3 space-y-3">
                    {/* Large Logo Preview */}
                    <div className="border-b border-hyper-primary pb-3">
                        <div className="text-xs text-hyper-muted-foreground mb-2">Large Logo:</div>
                        {settings.large_logo_enabled && settings.large_logo_url ? (
                            <div className="flex items-center justify-center">
                                <img
                                    src={settings.large_logo_url}
                                    alt="Large Logo Preview"
                                    style={{
                                        width: `${settings.large_logo_width}px`,
                                        height: `${settings.large_logo_height}px`,
                                        maxWidth: '150px',
                                        maxHeight: '40px',
                                        objectFit: 'contain'
                                    }}
                                    className="border border-hyper-primary rounded"
                                    onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.style.display = 'none';
                                        target.nextElementSibling?.classList.remove('hidden');
                                    }}
                                />
                                <div className="hidden flex items-center gap-2 text-hyper-muted-foreground text-xs">
                                    <ImageIcon size={16} />
                                    Invalid image URL
                                </div>
                            </div>
                        ) : (
                            <div className="flex items-center justify-center gap-2 text-hyper-muted-foreground text-xs py-2">
                                <ImageIcon size={16} />
                                {settings.large_logo_enabled ? 'No logo URL provided' : 'Large logo disabled'}
                            </div>
                        )}
                    </div>

                    {/* Small Logo Preview */}
                    <div className="border-b border-hyper-primary pb-3">
                        <div className="text-xs text-hyper-muted-foreground mb-2">Small Logo (Collapsed):</div>
                        {settings.small_logo_enabled && settings.small_logo_url ? (
                            <div className="flex items-center justify-center">
                                <img
                                    src={settings.small_logo_url}
                                    alt="Small Logo Preview"
                                    style={{
                                        width: `${settings.small_logo_width}px`,
                                        height: `${settings.small_logo_height}px`,
                                        maxWidth: '30px',
                                        maxHeight: '30px',
                                        objectFit: 'contain'
                                    }}
                                    className="border border-hyper-primary rounded"
                                    onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.style.display = 'none';
                                        target.nextElementSibling?.classList.remove('hidden');
                                    }}
                                />
                                <div className="hidden flex items-center gap-2 text-hyper-muted-foreground text-xs">
                                    <ImageIcon size={12} />
                                    Invalid
                                </div>
                            </div>
                        ) : (
                            <div className="flex items-center justify-center gap-2 text-hyper-muted-foreground text-xs py-2">
                                <ImageIcon size={12} />
                                {settings.small_logo_enabled ? 'No logo URL' : 'Small logo disabled'}
                            </div>
                        )}
                    </div>

                    {/* Navigation Links Preview */}
                    <div>
                        <div className="text-xs text-hyper-muted-foreground mb-2">External Links:</div>
                        <div className="space-y-1">
                            {settings.discord_url && (
                                <div className="flex items-center gap-2 text-xs text-hyper-primary">
                                    <ExternalLink size={12} />
                                    Discord
                                </div>
                            )}
                            {settings.knowledge_base_url && (
                                <div className="flex items-center gap-2 text-xs text-hyper-primary">
                                    <ExternalLink size={12} />
                                    Knowledge Base
                                </div>
                            )}
                            {settings.support_url && (
                                <div className="flex items-center gap-2 text-xs text-hyper-primary">
                                    <ExternalLink size={12} />
                                    Support
                                </div>
                            )}
                            {settings.billing_url && (
                                <div className="flex items-center gap-2 text-xs text-hyper-primary">
                                    <ExternalLink size={12} />
                                    Billing
                                </div>
                            )}
                            {!settings.discord_url && !settings.knowledge_base_url && !settings.support_url && !settings.billing_url && (
                                <div className="text-xs text-hyper-muted-foreground">
                                    No external links configured
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Navigation Bar Preview */}
            <div className="bg-hyper-card backdrop-blur-lg border border-hyper-primary rounded-xl p-4">
                <div className="flex items-center gap-2 mb-3">
                    <Navigation size={16} className="text-hyper-primary" />
                    <h4 className="font-semibold text-hyper-primary">Navigation Preview</h4>
                </div>
                
                <div className="bg-hyper-sidebar border border-hyper-primary rounded-lg p-3">
                    <div className="flex items-center justify-between">
                        <div>
                            {settings.large_logo_enabled && settings.large_logo_url ? (
                                <img
                                    src={settings.large_logo_url}
                                    alt="Nav Logo Preview"
                                    style={{
                                        width: `${Math.min(settings.large_logo_width, 100)}px`,
                                        height: `${Math.min(settings.large_logo_height, 30)}px`,
                                        objectFit: 'contain'
                                    }}
                                    className="border border-hyper-primary rounded"
                                    onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.style.display = 'none';
                                        target.nextElementSibling?.classList.remove('hidden');
                                    }}
                                />
                            ) : (
                                <div className="text-xs text-hyper-muted-foreground">
                                    {settings.large_logo_enabled ? 'No logo' : 'Logo disabled'}
                                </div>
                            )}
                        </div>
                        <div className="text-xs text-hyper-muted-foreground">
                            Navigation Bar
                        </div>
                    </div>
                </div>
            </div>

            {/* Settings Summary */}
            <div className="bg-hyper-card backdrop-blur-lg border border-hyper-primary rounded-xl p-4">
                <h4 className="font-semibold text-hyper-primary mb-3">Current Settings</h4>
                <div className="space-y-2 text-xs">
                    <div className="flex justify-between">
                        <span className="text-hyper-muted-foreground">Large Logo:</span>
                        <span className={settings.large_logo_enabled ? 'text-green-400' : 'text-red-400'}>
                            {settings.large_logo_enabled ? 'Enabled' : 'Disabled'}
                        </span>
                    </div>
                    <div className="flex justify-between">
                        <span className="text-hyper-muted-foreground">Small Logo:</span>
                        <span className={settings.small_logo_enabled ? 'text-green-400' : 'text-red-400'}>
                            {settings.small_logo_enabled ? 'Enabled' : 'Disabled'}
                        </span>
                    </div>
                    <div className="flex justify-between">
                        <span className="text-hyper-muted-foreground">External Links:</span>
                        <span className="text-hyper-primary">
                            {[settings.discord_url, settings.knowledge_base_url, settings.support_url, settings.billing_url].filter(Boolean).length} configured
                        </span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PreviewPanel;