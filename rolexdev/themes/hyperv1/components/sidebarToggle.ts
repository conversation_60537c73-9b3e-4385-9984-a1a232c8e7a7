import { useEffect, useState } from 'react';

export const getSidebarCollapsed = (): boolean => {
    return localStorage.getItem('sidebar-collapsed') === 'true';
};

export const setSidebarCollapsed = (collapsed: boolean): void => {
    localStorage.setItem('sidebar-collapsed', String(collapsed));
    const event = new CustomEvent('sidebar-toggle', { detail: collapsed });
    window.dispatchEvent(event);
};

export const useSidebarCollapsed = (): [boolean, (collapsed: boolean) => void] => {
    const [collapsed, setCollapsedState] = useState(() => getSidebarCollapsed());

    useEffect(() => {
        const handler = (e: Event) => {
            const detail = (e as CustomEvent).detail;
            setCollapsedState(detail);
        };

        window.addEventListener('sidebar-toggle', handler);
        return () => window.removeEventListener('sidebar-toggle', handler);
    }, []);

    const updateCollapsed = (value: boolean) => {
        setSidebarCollapsed(value);
        setCollapsedState(value);
    };

    return [collapsed, updateCollapsed];
};
