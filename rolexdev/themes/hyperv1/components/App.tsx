import React, { lazy } from 'react';
import { hot } from 'react-hot-loader/root';
import { Route, Router, Switch } from 'react-router-dom';
import { StoreProvider } from 'easy-peasy';
import { store } from '@rolexdev/themes/hyperv1/state';
import { SiteSettings } from '@/state/settings';
import ProgressBar from '@/components/elements/ProgressBar';
import { NotFound } from '@/components/elements/ScreenBlock';
import tw from 'twin.macro';
import GlobalStylesheet from '@/assets/css/GlobalStylesheet';
import { history } from '@/components/history';
import { setupInterceptors } from '@/api/interceptors';
import AuthenticatedRoute from '@/components/elements/AuthenticatedRoute';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { ThemeProvider } from '../contexts/ThemeContext';
import '@/assets/tailwind.css';
import Spinner from '@/components/elements/Spinner';
import Effect  from '@rolexdev/themes/hyperv1/background/effect/Effect';
import StarsCanvas from '@rolexdev/themes/hyperv1/background/effect/StarsCanvas';
import Earth from '@rolexdev/themes/hyperv1/assets/globe/Globe';
import useMobileView from '@rolexdev/themes/hyperv1/hooks/useMobileView';

const DashboardRouter = lazy(() => import(/* webpackChunkName: "dashboard" */ '@rolexdev/themes/hyperv1/routers/DashboardRouter'));
const ServerRouter = lazy(() => import(/* webpackChunkName: "server" */ '@rolexdev/themes/hyperv1/routers/ServerRouter'));
const AuthenticationRouter = lazy(() => import(/* webpackChunkName: "auth" */ '@rolexdev/themes/hyperv1/routers/AuthenticationRouter'));


interface ExtendedWindow extends Window {
    SiteConfiguration?: SiteSettings;
    PterodactylUser?: {
        uuid: string;
        username: string;
        email: string;
        /* eslint-disable camelcase */
        root_admin: boolean;
        use_totp: boolean;
        language: string;
        updated_at: string;
        created_at: string;
        name_first: string;
        name_last: string;
        /* eslint-enable camelcase */
    };
}

setupInterceptors(history);

const App = () => {
    const isMobile = useMobileView(972);

    const { PterodactylUser, SiteConfiguration } = window as ExtendedWindow;
    if (PterodactylUser && !store.getState().user.data) {
        store.getActions().user.setUserData({
            uuid: PterodactylUser.uuid,
            username: PterodactylUser.username,
            email: PterodactylUser.email,
            language: PterodactylUser.language,
            rootAdmin: PterodactylUser.root_admin,
            useTotp: PterodactylUser.use_totp,
            createdAt: new Date(PterodactylUser.created_at),
            updatedAt: new Date(PterodactylUser.updated_at),
            first_name: PterodactylUser.name_first,
            last_name: PterodactylUser.name_last,
        });
    }

    if (!store.getState().settings.data) {
        store.getActions().settings.setSettings(SiteConfiguration!);
    }

    return (
        <>
            <div className='fixed w-full h-full bg-black opacity-60' css={'z-index: -1;'}/>
            <div className='fixed w-full h-full' css={'z-index: -2;'}>
                <Effect usePrimaryColor={true} />
            </div>
            {!isMobile && (
            <StarsCanvas stop={isMobile} className="fixed inset-0 flex items-center justify-center rounded-xl" css={'z-index: -1;'}/>
            )}
            <div className='fixed inset-x-0 bottom-0 flex justify-center' css={'z-index: -2;'}>
                <div style={{ transform: 'translateY(85%)' }}>
                    <Earth
                        usePrimaryColor={true}
                        width={isMobile ? '800px' : '2000px'}
                        height={isMobile ? '800px' : '2000px'}
                        maxWidth={isMobile ? '800px' : '2000px'}
                        maxHeight={isMobile ? '800px' : '2000px'}
                        stop={isMobile}
                    />
                </div>
            </div>
            <GlobalStylesheet />
            <StoreProvider store={store}>
                <ThemeProvider>
                    <ProgressBar />
                    <div css={tw`mx-auto w-auto`}>
                        <Router history={history}>
                            <Switch>
                                <Route path={'/auth'}>
                                    <Spinner.Suspense>
                                        <AuthenticationRouter />
                                    </Spinner.Suspense>
                                </Route>
                                <AuthenticatedRoute path={'/server/:id'}>
                                    <Spinner.Suspense>
                                        <ServerContext.Provider>
                                            <ServerRouter />
                                        </ServerContext.Provider>
                                    </Spinner.Suspense>
                                </AuthenticatedRoute>
                                <AuthenticatedRoute path={'/'}>
                                    <Spinner.Suspense>
                                        <DashboardRouter />
                                    </Spinner.Suspense>
                                </AuthenticatedRoute>
                                <Route path={'*'}>
                                    <NotFound />
                                </Route>
                            </Switch>
                        </Router>
                    </div>
                </ThemeProvider>
            </StoreProvider>
        </>
    );
};

export default hot(App);
