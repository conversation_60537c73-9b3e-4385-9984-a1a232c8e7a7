import styled from 'styled-components/macro';
import { breakpoint } from '@/theme';
import tw from 'twin.macro';

const ContentContainer = styled.div`
    ${tw`mx-4 my-10`};
    max-width: 100vw;
    
    ${breakpoint('md')`
        ${tw`mx-6`};
    `};
    
    ${breakpoint('lg')`
        ${tw`mx-8`};
    `};
    
    ${breakpoint('xl')`
        ${tw`mx-10`};
    `};
`;
ContentContainer.displayName = 'ContentContainer';

export default ContentContainer;
