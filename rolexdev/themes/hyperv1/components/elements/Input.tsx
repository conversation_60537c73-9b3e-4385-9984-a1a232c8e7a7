import React from 'react';

export interface Props extends React.InputHTMLAttributes<HTMLInputElement> {
    isLight?: boolean;
    hasError?: boolean;
}

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
    isLight?: boolean;
    hasError?: boolean;
}

// Helper function to generate common input/textarea styles
const getInputStyles = (isLight?: boolean, hasError?: boolean) => {
    const baseClasses = 'appearance-none outline-none w-full min-w-0 p-3 rounded-md text-sm transition-all duration-150 resize-none shadow-none h-10';
    
    const themeClasses = isLight 
        ? 'bg-hyper-background text-hyper-foreground border focus:border-2 border-hyper-primary hover:border-hyper-accent focus:outline-none disabled:bg-neutral-100 font-medium'
        : 'bg-neutral-600 border-2 border-neutral-500 hover:border-neutral-400 text-neutral-200 focus:shadow-md focus-within:border-primary-300 disabled:opacity-75';
    
    const errorClasses = hasError 
        ? (isLight ? 'border-red-400 text-red-800' : 'text-red-100 border-red-400 hover:border-red-300 focus:border-red-300')
        : '';
    
    return `${baseClasses} ${themeClasses} ${errorClasses}`;
};

const Input: React.FC<Props> = ({ isLight, hasError, className, type = 'text', ...props }) => {
    let classes = '';
    
    if (type === 'checkbox' || type === 'radio') {
        // Checkbox/Radio styles
        classes = 'bg-neutral-500 cursor-pointer appearance-none inline-block align-middle select-none flex-shrink-0 w-4 h-4 text-primary-400 border border-neutral-300 transition-all duration-75';
        
        if (type === 'radio') {
            classes += ' rounded-full';
        } else {
            classes += ' rounded-sm';
        }
        
        classes += ' focus:outline-none focus:border-primary-300 checked:border-transparent checked:bg-no-repeat checked:bg-center';
    } else {
        // Regular input styles - add hyper-input class for light theme
        classes = getInputStyles(isLight, hasError);
        if (isLight) {
            classes += ' hyper-input';
        }
    }
    
    const combinedClasses = `${classes} ${className || ''}`.trim();
    
    return <input type={type} className={combinedClasses} {...props} />;
};

const Textarea: React.FC<TextareaProps> = ({ isLight, hasError, className, ...props }) => {
    // Use the same helper function and add hyper-textarea class for light theme
    let classes = getInputStyles(isLight, hasError);
    if (isLight) {
        classes += ' hyper-textarea';
    }
    
    const combinedClasses = `${classes} ${className || ''}`.trim();
    
    return <textarea className={combinedClasses} {...props} />;
};

export { Textarea };
export default Input;
