import React, { useState, useRef } from 'react';
import { PaginationDataSet } from '@/api/http';
import classNames from 'classnames';
import { ChevronDoubleLeftIcon, ChevronDoubleRightIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/solid';

interface Props {
    className?: string;
    pagination: PaginationDataSet;
    onPageSelect: (page: number, perPage: number) => void;
}

const ROWS_OPTIONS = [5, 10, 25, 50, 100];

const PaginationFooter = ({ pagination, className, onPageSelect }: Props) => {
    const currentPage = pagination.currentPage;
    const rowsPerPage = pagination.perPage;
    const totalItems = pagination.total;
    const totalPages = Math.ceil(totalItems / rowsPerPage);
    const startItem = totalItems === 0 ? 0 : (currentPage - 1) * rowsPerPage + 1;
    const endItem = Math.min(currentPage * rowsPerPage, totalItems);

    const handlePageChange = (page: number) => {
        if (page >= 1 && page <= totalPages && page !== currentPage) {
            onPageSelect(page, rowsPerPage);
        }
    };

    const [dropdownOpen, setDropdownOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);

    React.useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setDropdownOpen(false);
            }
        }
        if (dropdownOpen) {
            document.addEventListener("mousedown", handleClickOutside);
        } else {
            document.removeEventListener("mousedown", handleClickOutside);
        }
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, [dropdownOpen]);

    const handleRowsPerPageChange = (newRowsPerPage: number) => {
        // Always go to page 1 when changing per page, or keep current page if still valid
        const newTotalPages = Math.ceil(totalItems / newRowsPerPage);
        const newCurrentPage = Math.min(currentPage, newTotalPages) || 1;
        onPageSelect(newCurrentPage, newRowsPerPage);
    };

    const getVisiblePages = () => {
        const delta = 2;
        const range = [];
        const rangeWithDots: (number | string)[] = [];

        for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
            range.push(i);
        }

        if (currentPage - delta > 2) {
            rangeWithDots.push(1, "...");
        } else if (totalPages > 0) {
            rangeWithDots.push(1);
        }

        rangeWithDots.push(...range);

        if (currentPage + delta < totalPages - 1) {
            rangeWithDots.push("...", totalPages);
        } else if (totalPages > 1) {
            rangeWithDots.push(totalPages);
        }

        return rangeWithDots;
    };

    if (totalItems === 0) return null;

    return (
        <div className={classNames(' rounded-xl flex flex-col sm:flex-row items-center justify-between gap-4 p-4 bg-hyper-sidebar backdrop-blur-lg text-gray-100 border border-hyper-primary', className)}>
            {/* Rows per page selector */}
            <div className="flex items-center gap-2 text-sm">
                <span className="text-hyper-primary">Rows per page</span>
                <div className="relative">
                    <button
                        type="button"
                        className="bg-hyper-glass border border-hyper-accent rounded px-2 py-1 text-hyper-primary focus:outline-none flex items-center gap-2"
                        onClick={() => setDropdownOpen((open) => !open)}
                    >
                        {rowsPerPage}
                        <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    {dropdownOpen && (
                        <div className="absolute left-0 right-0 bottom-full mb-2 min-w-full bg-hyper-sidebar backdrop-blur-lg border border-hyper-accent rounded shadow-lg z-50">
                            {ROWS_OPTIONS.map(opt => (
                                <button
                                    key={opt}
                                    className={`w-full text-left px-4 py-2 hover:bg-hyper-accent hover:text-hyper-primary transition-colors ${
                                        rowsPerPage === opt ? "bg-hyper-primary text-hyper-primary font-bold" : "text-hyper-primary"
                                    }`}
                                    onClick={() => {
                                        setDropdownOpen(false);
                                        handleRowsPerPageChange(opt);
                                    }}
                                >
                                    {opt}
                                </button>
                            ))}
                        </div>
                    )}
                </div>
            </div>

            {/* Page info */}
            <div className="text-sm text-hyper-accent font-medium">
                {startItem}-{endItem} <span className='text-hyper-primary text-xs font-medium mx-1'>OF</span> {totalItems}
            </div>

            {/* Navigation controls */}
            {totalPages > 1 && (
                <div className="flex items-center gap-1 text-hyper-accent">
                    {/* First page */}
                    <button
                        onClick={() => handlePageChange(1)}
                        disabled={currentPage === 1}
                        className="p-2 rounded hover:bg-hyper-text-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        aria-label="First page"
                    >
                        <ChevronDoubleLeftIcon className="w-4 h-4" />
                    </button>
                    {/* Previous page */}
                    <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="p-2 rounded hover:bg-hyper-text-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        aria-label="Previous page"
                    >
                        <ChevronLeftIcon className="w-4 h-4" />
                    </button>
                    {/* Page numbers */}
                    <div className="flex items-center gap-1 mx-2">
                        {getVisiblePages().map((page, idx) => (
                            <div key={idx}>
                                {page === "..." ? (
                                    <span className="px-3 py-1 text-hyper-primary">...</span>
                                ) : (
                                    <button
                                        onClick={() => handlePageChange(page as number)}
                                        className={`px-3 py-1 rounded text-sm transition-colors ${
                                            currentPage === page ? "bg-hyper-text-primary text-hyper-accent" : "hover:bg-hyper-text-primary text-hyper-accent"
                                        }`}
                                        disabled={currentPage === page}
                                    >
                                        {page}
                                    </button>
                                )}
                            </div>
                        ))}
                    </div>
                    {/* Next page */}
                    <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="p-2 rounded hover:bg-hyper-text-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        aria-label="Next page"
                    >
                        <ChevronRightIcon className="w-4 h-4" />
                    </button>
                    {/* Last page */}
                    <button
                        onClick={() => handlePageChange(totalPages)}
                        disabled={currentPage === totalPages}
                        className="p-2 rounded hover:bg-hyper-text-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        aria-label="Last page"
                    >
                        <ChevronDoubleRightIcon className="w-4 h-4" />
                    </button>
                </div>
            )}
        </div>
    );
};

export default PaginationFooter;