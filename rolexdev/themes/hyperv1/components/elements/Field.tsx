import React, { forwardRef } from 'react';
import { Field as FormikField, FieldProps } from 'formik';
import Input, { Textarea } from '@rolexdev/themes/hyperv1/components/elements/Input';
import Label from '@rolexdev/themes/hyperv1/components/elements/Label';

interface OwnProps {
    name: string;
    light?: boolean;
    label?: string;
    description?: string;
    validate?: (value: any) => undefined | string | Promise<any>;
    multiline?: boolean;
}

type Props = OwnProps & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'name'>;

const Field = forwardRef<HTMLInputElement, Props>(
    ({ id, name, light = false, label, description, validate, multiline, ...props }, ref) => (
        <FormikField innerRef={ref} name={name} validate={validate}>
            {({ field, form: { errors, touched } }: FieldProps) => (
                <div>
                    {label && (
                        <Label htmlFor={id} isLight={light}>
                            {label}
                        </Label>
                    )}
                    {multiline ? (
                        <Textarea
                            id={id}
                            name={name}
                            value={field.value}
                            onChange={field.onChange}
                            onBlur={field.onBlur}
                            isLight={light}
                            hasError={!!(touched[field.name] && errors[field.name])}
                            disabled={props.disabled}
                            {...(props as Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'name'>)}
                        />
                    ) : (
                        <Input
                            id={id}
                            {...field}
                            {...props}
                            isLight={light}
                            hasError={!!(touched[field.name] && errors[field.name])}
                        />
                    )}
                    {touched[field.name] && errors[field.name] ? (
                        <p className={'input-help error text-xs text-hyper-accent font-medium mt-2'}>
                            {(errors[field.name] as string).charAt(0).toUpperCase() +
                                (errors[field.name] as string).slice(1)}
                        </p>
                    ) : description ? (
                        <p className={'input-help text-xs text-hyper-accent font-medium mt-2'}>{description}</p>
                    ) : null}
                </div>
            )}
        </FormikField>
    )
);
Field.displayName = 'Field';

export default Field;
