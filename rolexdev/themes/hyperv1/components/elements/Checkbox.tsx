import React from 'react';
import { Field, FieldProps } from 'formik';

interface Props {
    name: string;
    value: string;
    className?: string;
    id?: string;
    disabled?: boolean;
}

interface StandaloneCheckboxProps {
    checked: boolean;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    className?: string;
    id?: string;
    disabled?: boolean;
}

type OmitFields = 'ref' | 'name' | 'value' | 'type' | 'checked' | 'onClick' | 'onChange';
type InputProps = Omit<JSX.IntrinsicElements['input'], OmitFields>;

// Standalone checkbox for non-Formik use
export const StandaloneCheckbox = ({ checked, onChange, className, id, disabled, ...props }: StandaloneCheckboxProps) => {
    const uniqueId = id || `checkbox-${Math.random().toString(36).substr(2, 9)}`;
    
    return (
        <div className="relative contents">
            <input
                type="checkbox"
                checked={checked}
                onChange={onChange}
                className="sr-only"
                id={uniqueId}
                disabled={disabled}
                {...props}
            />
            <label
                htmlFor={uniqueId}
                className={`
                    inline-flex items-center justify-center w-5 h-5 rounded border-2 
                    ${checked 
                        ? 'bg-hyper-accent border-hyper-accent' 
                        : 'bg-transparent border-hyper-accent'
                    }
                    ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                    transition-all duration-200
                    ${className || ''}
                `}
            >
                {checked && (
                    <svg 
                        className="w-3 h-3 text-white" 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                    >
                        <path 
                            strokeLinecap="round" 
                            strokeLinejoin="round" 
                            strokeWidth={2} 
                            d="M5 13l4 4L19 7" 
                        />
                    </svg>
                )}
            </label>
        </div>
    );
};

// Formik-based checkbox
const Checkbox = ({ name, value, className, id, disabled, ...props }: Props & InputProps) => (
    <Field name={name}>
        {({ field, form }: FieldProps) => {
            if (!Array.isArray(field.value)) {
                console.error('Attempting to mount a checkbox using a field value that is not an array.');
                return null;
            }

            const isChecked = (field.value || []).includes(value);
            const uniqueId = id || `checkbox-${name}-${value}-${Math.random().toString(36).substr(2, 9)}`;

            return (
                <div className="relative contents">
                    <input
                        {...props}
                        id={uniqueId}
                        type="checkbox"
                        disabled={disabled}
                        checked={isChecked}
                        className="sr-only"
                        onClick={() => form.setFieldTouched(field.name, true)}
                        onChange={(e) => {
                            const set = new Set(field.value);
                            set.has(value) ? set.delete(value) : set.add(value);

                            field.onChange(e);
                            form.setFieldValue(field.name, Array.from(set));
                        }}
                    />
                    <label
                        htmlFor={uniqueId}
                        className={`
                            inline-flex items-center justify-center w-5 h-5 rounded border-2 
                            ${isChecked 
                                ? 'bg-hyper-accent border-hyper-accent' 
                                : 'bg-transparent border-hyper-accent'
                            }
                            ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                            transition-all duration-200
                            ${className || ''}
                        `}
                    >
                        {isChecked && (
                            <svg 
                                className="w-3 h-3 text-white" 
                                fill="none" 
                                stroke="currentColor" 
                                viewBox="0 0 24 24"
                            >
                                <path 
                                    strokeLinecap="round" 
                                    strokeLinejoin="round" 
                                    strokeWidth={2} 
                                    d="M5 13l4 4L19 7" 
                                />
                            </svg>
                        )}
                    </label>
                </div>
            );
        }}
    </Field>
);

export default Checkbox;