'use client';
import React from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import { ShineBorder } from '@rolexdev/themes/hyperv1/components/elements/ShineBorder';

interface SidebarTooltipProps {
  text: string;
  rect: DOMRect;
  placement?: 'top' | 'bottom' | 'right' | 'left';
  onClose?: () => void;
}

export const SidebarTooltip = ({ text, rect, placement = 'right', onClose }: SidebarTooltipProps) => {
  const x = useMotionValue(0);
  const spring = { stiffness: 200, damping: 10 };
  const rotate = useSpring(useTransform(x, [-50, 50], [-15, 15]), spring);
  const translateX = useSpring(useTransform(x, [-50, 50], [-10, 10]), spring);

  // Calculate tooltip position based on placement
  let style: React.CSSProperties = {
    position: 'fixed',
    pointerEvents: 'none',
    whiteSpace: 'nowrap',
    zIndex: 9999,
  };

  switch (placement) {
    case 'top':
      style.left = rect.left + rect.width / 2 - 40;
      style.top = rect.top - 10;
      style.transform = 'translate(-50%, -100%)';
      break;
    case 'bottom':
      style.left = rect.left + rect.width / 2 - 40; // nudge left for perfect centering
      style.top = rect.bottom + 10;
      style.transform = 'translate(-50%, 0)';
      break;
    case 'left':
      style.left = rect.left - 10;
      style.top = rect.top + rect.height / 2 - 20;
      style.transform = 'translate(-100%, -50%)';
      break;
    case 'right':
    default:
      style.left = rect.right + 10;
      style.top = rect.top + rect.height / 2 - 20;
      style.transform = 'translate(0, -50%)';
      break;
  }

  const handleTouchEnd = () => {
    if (onClose) onClose();
  };
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, y: 10 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.8, y: 10 }}
      transition={{ type: 'spring', stiffness: 250, damping: 20 }}
      style={{ ...style, rotate, translateX }}
      onMouseMove={(e) => {
        const halfWidth = rect.width / 2;
        x.set(e.nativeEvent.offsetX - halfWidth);
      }}
      onTouchEnd={handleTouchEnd}
    >
      <div className="bg-hyper-card backdrop-blur-sm text-white text-sm font-medium px-4 py-2 rounded-md shadow-lg border border-hyper-primary">
        <ShineBorder shineColor={["#df3050"]} />
        {text}
      </div>
    </motion.div>
  );
};

