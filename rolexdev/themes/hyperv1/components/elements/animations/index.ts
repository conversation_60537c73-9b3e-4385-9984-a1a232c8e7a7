// Animation Components Export
export { default as PageTransition } from './PageTransition';
export { default as AnimatedList } from './AnimatedList';
export { default as AnimatedCard } from './AnimatedCard';
export { default as FadeTransition } from './FadeTransition';
export { default as <PERSON>Container } from './AnimatedContainer';
export { default as withAnimation } from './withAnimation';
export { default as SortableList } from './SortableList';

// Common animation variants
export const pageVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 }
};

export const listItemVariants = {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 20 }
};

export const cardVariants = {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.95 },
    hover: { scale: 1.02 },
    tap: { scale: 0.98 }
};

export const staggerContainer = {
    initial: {},
    animate: {
        transition: {
            staggerChildren: 0.1
        }
    }
};

// Common transition settings
export const springTransition = {
    type: 'spring' as const,
    stiffness: 400,
    damping: 30
};

export const easeTransition = {
    duration: 0.3,
    ease: [0.4, 0.0, 0.2, 1] as const
};