import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface SortableListProps<T> {
    items: T[];
    renderItem: (item: T, index: number) => React.ReactNode;
    className?: string;
    itemClassName?: string;
    getKey: (item: T, index: number) => string | number;
    sortKey?: string | number;
    searchTerm?: string;
    staggerDelay?: number;
}

/**
 * A wrapper component that provides smooth animations for sortable/searchable lists
 */
export default function SortableList<T>({
    items,
    renderItem,
    className = '',
    itemClassName = '',
    getKey,
    sortKey,
    searchTerm,
    staggerDelay = 0.05
}: SortableListProps<T>) {
    const variants = {
        container: {
            initial: {},
            animate: {
                transition: {
                    staggerChildren: staggerDelay
                }
            }
        },
        item: {
            initial: { opacity: 0, scale: 0.95, y: 20 },
            animate: { 
                opacity: 1, 
                scale: 1, 
                y: 0,
                transition: {
                    type: 'spring',
                    stiffness: 400,
                    damping: 30
                }
            },
            exit: { 
                opacity: 0, 
                scale: 0.95, 
                y: -20,
                transition: {
                    duration: 0.2
                }
            }
        }
    };

    return (
        <motion.div
            className={className}
            variants={variants.container}
            initial="initial"
            animate="animate"
            key={`${sortKey}-${searchTerm}`} // Re-animate when sort or search changes
        >
            <AnimatePresence>
                {items.map((item, index) => (
                    <motion.div
                        key={getKey(item, index)}
                        className={itemClassName}
                        variants={variants.item}
                        layout
                        initial="initial"
                        animate="animate"
                        exit="exit"
                    >
                        {renderItem(item, index)}
                    </motion.div>
                ))}
            </AnimatePresence>
        </motion.div>
    );
}