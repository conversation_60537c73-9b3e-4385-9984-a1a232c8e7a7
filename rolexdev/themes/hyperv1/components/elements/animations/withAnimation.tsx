import React from 'react';
import { PageTransition } from '../animations';

interface WithAnimationProps {
    [key: string]: any;
}

/**
 * Higher-order component that wraps a component with page transition animations
 * @param WrappedComponent The component to wrap with animations
 * @param options Animation options for the wrapper
 */
export function withAnimation<T extends WithAnimationProps>(
    WrappedComponent: React.ComponentType<T>,
    options?: {
        variant?: 'default' | 'slide' | 'fade' | 'scale';
        duration?: number;
        className?: string;
    }
) {
    const {
        variant = 'default',
        duration = 0.3,
        className = ''
    } = options || {};

    const WithAnimationComponent = React.forwardRef<any, T>((props, ref) => {
        return (
            <PageTransition 
                variant={variant} 
                duration={duration}
                className={className}
            >
                <WrappedComponent ref={ref} {...props} />
            </PageTransition>
        );
    });

    WithAnimationComponent.displayName = `withAnimation(${WrappedComponent.displayName || WrappedComponent.name})`;

    return WithAnimationComponent;
}

export default withAnimation;