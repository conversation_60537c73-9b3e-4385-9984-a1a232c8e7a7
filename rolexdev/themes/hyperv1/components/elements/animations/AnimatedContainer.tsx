import React from 'react';
import { motion } from 'framer-motion';

interface AnimatedContainerProps {
    children: React.ReactNode;
    className?: string;
    style?: React.CSSProperties;
    staggerChildren?: number;
    variant?: 'stagger' | 'slide' | 'fade';
}

const containerVariants = {
    stagger: {
        initial: {},
        animate: {
            transition: {
                staggerChildren: 0.1
            }
        }
    },
    slide: {
        initial: { opacity: 0, x: -50 },
        animate: { 
            opacity: 1, 
            x: 0,
            transition: {
                type: 'spring',
                stiffness: 400,
                damping: 30
            }
        }
    },
    fade: {
        initial: { opacity: 0 },
        animate: { 
            opacity: 1,
            transition: {
                duration: 0.5
            }
        }
    }
};

export default function AnimatedContainer({ 
    children, 
    className = '', 
    style,
    staggerChildren = 0.1,
    variant = 'stagger'
}: AnimatedContainerProps) {
    const variants = variant === 'stagger' 
        ? {
            ...containerVariants[variant],
            animate: {
                transition: {
                    staggerChildren
                }
            }
        }
        : containerVariants[variant];

    return (
        <motion.div
            className={className}
            style={style}
            variants={variants}
            initial="initial"
            animate="animate"
        >
            {children}
        </motion.div>
    );
}