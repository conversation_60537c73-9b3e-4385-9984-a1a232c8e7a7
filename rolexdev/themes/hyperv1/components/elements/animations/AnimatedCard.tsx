import React from 'react';
import { motion } from 'framer-motion';

interface AnimatedCardProps {
    children: React.ReactNode;
    className?: string;
    delay?: number;
    variant?: 'default' | 'hover' | 'tap';
    hoverScale?: number;
    tapScale?: number;
}

export default function AnimatedCard({ 
    children, 
    className = '', 
    delay = 0,
    variant = 'default',
    hoverScale = 1.02,
    tapScale = 0.98
}: AnimatedCardProps) {
    const baseVariants = {
        initial: { opacity: 0, y: 20, scale: 0.95 },
        animate: { 
            opacity: 1, 
            y: 0, 
            scale: 1,
            transition: {
                type: 'spring',
                stiffness: 400,
                damping: 30,
                delay: delay
            }
        }
    };

    const hoverVariants = variant === 'hover' ? {
        ...baseVariants,
        hover: { scale: hoverScale }
    } : baseVariants;

    const tapVariants = variant === 'tap' ? {
        ...hoverVariants,
        tap: { scale: tapScale }
    } : hoverVariants;

    return (
        <motion.div
            className={className}
            variants={tapVariants}
            initial="initial"
            animate="animate"
            whileHover={variant === 'hover' ? 'hover' : undefined}
            whileTap={variant === 'tap' ? 'tap' : undefined}
            layout
        >
            {children}
        </motion.div>
    );
}