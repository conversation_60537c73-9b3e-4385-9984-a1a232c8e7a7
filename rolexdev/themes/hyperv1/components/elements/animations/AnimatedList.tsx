import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface AnimatedListProps {
    children: React.ReactNode[] | React.ReactNode;
    className?: string;
    staggerDelay?: number;
    variant?: 'default' | 'fade' | 'slide' | 'scale';
}

const itemVariants = {
    default: {
        initial: { opacity: 0, y: 20 },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: -20 }
    },
    fade: {
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        exit: { opacity: 0 }
    },
    slide: {
        initial: { opacity: 0, x: -20 },
        animate: { opacity: 1, x: 0 },
        exit: { opacity: 0, x: 20 }
    },
    scale: {
        initial: { opacity: 0, scale: 0.8 },
        animate: { opacity: 1, scale: 1 },
        exit: { opacity: 0, scale: 0.8 }
    }
};

export default function AnimatedList({ 
    children, 
    className = '', 
    staggerDelay = 0.05,
    variant = 'default'
}: AnimatedListProps) {
    const selectedVariant = itemVariants[variant];
    const childrenArray = React.Children.toArray(children);
    
    return (
        <motion.div 
            className={className}
            initial="initial"
            animate="animate"
            variants={{
                initial: {},
                animate: {
                    transition: {
                        staggerChildren: staggerDelay
                    }
                }
            }}
        >
            <AnimatePresence>
                {childrenArray.map((child, index) => (
                    <motion.div
                        key={index}
                        variants={selectedVariant}
                        layout
                        transition={{
                            type: 'spring',
                            stiffness: 400,
                            damping: 30
                        }}
                    >
                        {child}
                    </motion.div>
                ))}
            </AnimatePresence>
        </motion.div>
    );
}