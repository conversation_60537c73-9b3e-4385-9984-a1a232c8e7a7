import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface PageTransitionProps {
    children: React.ReactNode;
    className?: string;
    variant?: 'default' | 'slide' | 'fade' | 'scale';
    duration?: number;
}

const variants = {
    default: {
        initial: { opacity: 0, y: 20 },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: -20 }
    },
    slide: {
        initial: { opacity: 0, x: 30 },
        animate: { opacity: 1, x: 0 },
        exit: { opacity: 0, x: -30 }
    },
    fade: {
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        exit: { opacity: 0 }
    },
    scale: {
        initial: { opacity: 0, scale: 0.95 },
        animate: { opacity: 1, scale: 1 },
        exit: { opacity: 0, scale: 0.95 }
    }
};

export default function PageTransition({ 
    children, 
    className = '', 
    variant = 'default',
    duration = 0.3
}: PageTransitionProps) {
    const selectedVariant = variants[variant];
    
    return (
        <motion.div
            className={className}
            initial={selectedVariant.initial}
            animate={selectedVariant.animate}
            exit={selectedVariant.exit}
            transition={{
                type: 'spring',
                stiffness: 400,
                damping: 30,
                duration: duration
            }}
        >
            {children}
        </motion.div>
    );
}