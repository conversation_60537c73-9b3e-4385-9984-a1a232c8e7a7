import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface FadeTransitionProps {
    show: boolean;
    children: React.ReactNode;
    className?: string;
    duration?: number;
}

export default function FadeTransition({ 
    show, 
    children, 
    className = '',
    duration = 0.3
}: FadeTransitionProps) {
    return (
        <AnimatePresence>
            {show && (
                <motion.div
                    className={className}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration }}
                >
                    {children}
                </motion.div>
            )}
        </AnimatePresence>
    );
}