import React, { useEffect } from 'react';
import ContentContainer from '@rolexdev/themes/hyperv1/components/elements/ContentContainer';
import { CSSTransition } from 'react-transition-group';
import tw from 'twin.macro';
import FlashMessageRender from '@/components/FlashMessageRender';

export interface PageContentBlockProps {
    title?: string;
    className?: string;
    showFlashKey?: string;
}

const PageContentBlock: React.FC<PageContentBlockProps> = ({ title, showFlashKey, className, children }) => {
    useEffect(() => {
        if (title) {
            document.title = title;
        }
    }, [title]);

    return (
        <CSSTransition timeout={150} classNames={'fade'} appear in>
            <>
                <ContentContainer className={className}>
                    {showFlashKey && <FlashMessageRender byKey={showFlashKey} css={tw`mb-4`} />}
                    {children}
                </ContentContainer>
                <ContentContainer css={tw`mb-4`} className='justify-center flex'>
                    <div className='w-fit h-full py-2 px-4 bg-hyper-glass rounded-2xl backdrop-blur-lg font-medium'>
                        <p css={tw`text-center text-xs`} className='w-fit text-hyper-primary'>
                            <a
                                rel={'noopener nofollow noreferrer'}
                                href={'https://pterodactyl.io'}
                                target={'_blank'}
                                css={tw`no-underline`}
                                className='text-hyper-accent'
                            >
                                Pterodactyl&reg;
                            </a>
                            &nbsp;&copy; 2015 - {new Date().getFullYear()}
                        </p>
                    </div>
                </ContentContainer>
            </>
        </CSSTransition>
    );
};

export default PageContentBlock;
