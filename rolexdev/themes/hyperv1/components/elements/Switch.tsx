import React, { useMemo, useState } from 'react';
import { v4 } from 'uuid';

export interface SwitchProps {
    name: string;
    label?: string;
    description?: string;
    checked?: boolean;
    defaultChecked?: boolean;
    readOnly?: boolean;
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
    children?: React.ReactNode;
}

const Switch = ({ name, label, description, checked, defaultChecked, readOnly, onChange, children }: SwitchProps) => {
    const uuid = useMemo(() => v4(), []);
    
    // Use controlled if checked is provided, otherwise uncontrolled with defaultChecked
    const isControlled = checked !== undefined;
    const checkboxProps = isControlled 
        ? { checked: checked }
        : { defaultChecked: defaultChecked };

    // For controlled component, use the checked prop; for uncontrolled, use a state
    const [internalChecked, setInternalChecked] = useState(defaultChecked || false);
    const isChecked = isControlled ? checked : internalChecked;

    return (
        <div className="flex items-start gap-3">
            <div className="relative select-none leading-normal flex-shrink-0">
                {children || (
                    <input
                        id={uuid}
                        name={name}
                        type="checkbox"
                        onChange={(e) => {
                            if (!isControlled) {
                                setInternalChecked(e.target.checked);
                            }
                            onChange && onChange(e);
                        }}
                        disabled={readOnly}
                        className="sr-only"
                        {...checkboxProps}
                    />
                )}
                <label
                    htmlFor={uuid}
                    className={`
                        relative flex h-6 w-11 cursor-pointer items-center rounded-full 
                        border transition-all duration-200 ease-in-out
                        ${isChecked 
                            ? 'bg-hyper-primary border-hyper-primary' 
                            : 'bg-hyper-sidebar border-hyper-accent'
                        }
                        ${readOnly ? 'cursor-not-allowed opacity-50' : 'hover:border-hyper-primary hover:shadow-lg'}
                    `}
                >
                    <span
                        className={`
                            inline-block h-4 w-4 transform rounded-full bg-white shadow-lg 
                            transition-transform duration-200 ease-in-out
                            ${isChecked ? 'translate-x-5 shadow-xl' : 'translate-x-1'}
                        `}
                    />
                </label>
            </div>
            {(label || description) && (
                <div className="flex-1 min-w-0">
                    {label && (
                        <label 
                            className={`cursor-pointer font-medium text-hyper-primary block ${description ? 'mb-1' : ''}`} 
                            htmlFor={uuid}
                        >
                            {label}
                        </label>
                    )}
                    {description && (
                        <p className="text-hyper-accent text-sm">{description}</p>
                    )}
                </div>
            )}
        </div>
    );
};

export default Switch;