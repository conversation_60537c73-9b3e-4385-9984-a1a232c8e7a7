import React from 'react';
import Formik<PERSON>ieldWrapper from '@/components/elements/FormikFieldWrapper';
import { Field, FieldProps, useField } from 'formik';
import Switch, { SwitchProps } from './Switch';

interface FormikSwitchProps {
    name: string;
    label?: string;
    description?: string;
    readOnly?: boolean;
}

const FormikSwitch = ({ name, label, description, readOnly }: FormikSwitchProps) => {
    const [field, meta, helpers] = useField<boolean>(name);

    return (
        <Switch
            name={name}
            label={label}
            description={description}
            checked={field.value}
            readOnly={readOnly}
            onChange={(e) => {
                helpers.setValue(e.target.checked);
                helpers.setTouched(true);
            }}
        />
    );
};

export default FormikSwitch;