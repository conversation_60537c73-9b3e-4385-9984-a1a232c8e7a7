import React from 'react';
import { Dialog } from './dialog';
import Button from './Button';

type Props = {
    title: string;
    buttonText: string;
    visible: boolean;
    showSpinnerOverlay?: boolean;
    onConfirmed: () => void;
    onModalDismissed: () => void;
    children: React.ReactNode;
};

const ConfirmationModal: React.FC<Props> = ({ 
    title, 
    children, 
    buttonText, 
    visible,
    showSpinnerOverlay = false,
    onConfirmed,
    onModalDismissed
}) => {
    return (
        <Dialog
            open={visible}
            onClose={onModalDismissed}
            title={title}
            preventExternalClose={showSpinnerOverlay}
        >
            <div className="p-6">
                <div className="text-hyper-secondary">
                    {children}
                </div>
            </div>
            
            <Dialog.Footer>
                <Button 
                    isSecondary 
                    onClick={onModalDismissed}
                    disabled={showSpinnerOverlay}
                    size="small"
                >
                    Cancel
                </Button>
                <Button 
                    color="red" 
                    onClick={onConfirmed}
                    disabled={showSpinnerOverlay}
                    size="small"
                >
                    {buttonText}
                </Button>
            </Dialog.Footer>
        </Dialog>
    );
};

ConfirmationModal.displayName = 'ConfirmationModal';

export default ConfirmationModal;