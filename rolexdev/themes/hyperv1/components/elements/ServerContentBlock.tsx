import PageContentBlock, { PageContentBlockProps } from '@rolexdev/themes/hyperv1/components/elements/PageContentBlock';
import React from 'react';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import ServerMiddleware from '@rolexdev/themes/hyperv1/components/dashboard/ServerMiddleware';

interface Props extends PageContentBlockProps {
    title: string;
}

const ServerContentBlock: React.FC<Props> = ({ title, children, ...props }) => {
    const server = ServerContext.useStoreState((state) => state.server.data);
    const name = server?.name;

    return (
        <PageContentBlock title={`${name} | ${title}`} {...props}>
            {server && (
                <div className="my-6 flex justify-start">
                    <ServerMiddleware server={server} layout="row" hideManageButton={true}/>
                </div>
            )}
            {children}
        </PageContentBlock>
    );
};

export default ServerContentBlock;
