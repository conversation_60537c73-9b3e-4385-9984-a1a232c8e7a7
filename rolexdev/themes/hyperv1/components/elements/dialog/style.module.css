.container {
    @apply flex min-h-full items-center justify-center p-4 text-center;
}

.panel {
    @apply relative rounded-xl max-w-2xl w-full mx-auto text-left;
}

.title {
    @apply font-header text-2xl font-bold mb-4 pr-8;
}

.close_icon {
    @apply w-5 h-5 group-hover:rotate-90 transition-transform duration-100;
}

.dialog_icon {
    @apply flex items-center justify-center w-10 h-10 rounded-full mr-4;

    &.danger {
        @apply bg-red-500 text-red-50;
    }

    &.warning {
        @apply bg-yellow-600 text-yellow-50;
    }

    &.success {
        @apply bg-green-600 text-green-50;
    }

    &.info {
        @apply bg-primary-500 text-primary-50;
    }
}
