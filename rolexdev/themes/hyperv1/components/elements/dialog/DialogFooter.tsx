import React, { useContext } from 'react';
import { DialogContext } from './';
import { useDeepCompareEffect } from '@/plugins/useDeepCompareEffect';

export default ({ children }: { children: React.ReactNode }) => {
    const { setFooter } = useContext(DialogContext);

    useDeepCompareEffect(() => {
        setFooter(
            <div className={'pt-4 border-t border-hyper-primary flex items-center justify-end gap-3'}>{children}</div>
        );
    }, [children]);

    return null;
};
