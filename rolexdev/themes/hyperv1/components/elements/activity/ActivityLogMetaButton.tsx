import React, { useState } from 'react';
import { ClipboardListIcon } from '@heroicons/react/outline';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';

export default ({ meta }: { meta: Record<string, unknown> }) => {
    const [open, setOpen] = useState(false);

    return (
        <div className={'self-center md:px-4'}>
            <Dialog open={open} onClose={() => setOpen(false)} hideCloseIcon>
                <div className='max-w-full w-full text-hyper-primary rounded-xl bg-hyper-card backdrop-blur-lg border border-hyper-primary p-5 max-h-fit'>
                    <div className='w-full h-10 mb-4'>
                        <h1 className='text-2xl font-bold text-hyper-primary'>Metadata</h1>
                    </div>
                    <pre
                        className={
                            'bg-hyper-sidebar border border-hyper-accent backdrop-blur-lg rounded p-2 font-mono text-sm leading-relaxed overflow-x-scroll whitespace-pre-wrap'
                        }
                    >
                        {JSON.stringify(meta, null, 2)}
                    </pre>
                    <div className='flex justify-end gap-2 mt-6'>
                        <Button onClick={() => setOpen(false)}>Close</Button>
                    </div>
                </div>
            </Dialog>
            <button
                aria-describedby={'View additional event metadata'}
                className={
                    'p-2 transition-colors duration-100 text-hyper-accent'
                }
                onClick={() => setOpen(true)}
            >
            
                <ClipboardListIcon className={'w-5 h-5'} />
            </button>
        </div>
    );
};
