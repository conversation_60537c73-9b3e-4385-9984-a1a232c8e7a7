import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import Tooltip from '@rolexdev/themes/hyperv1/components/elements/tooltip/Tooltip';
import Translate from '@/components/elements/Translate';
import { format, formatDistanceToNowStrict } from 'date-fns';
import { ActivityLog } from '@definitions/user';
import ActivityLogMetaButton from '@rolexdev/themes/hyperv1/components/elements/activity/ActivityLogMetaButton';
import { FolderOpenIcon, TerminalIcon } from '@heroicons/react/solid';
import useLocationHash from '@/plugins/useLocationHash';
import { getObjectKeys, isObject } from '@/lib/objects';
import { createAvatar } from '@dicebear/core';
import { identicon } from '@dicebear/collection';

interface Props {
    activity: ActivityLog;
    children?: React.ReactNode;
    isLast?: boolean;
}

function wrapProperties(value: unknown): any {
    if (value === null || typeof value === 'string' || typeof value === 'number') {
        return `<strong classname="text-hyper-accent">${String(value)}</strong>`;
    }

    if (isObject(value)) {
        return getObjectKeys(value).reduce((obj, key) => {
            if (key === 'count' || (typeof key === 'string' && key.endsWith('_count'))) {
                return { ...obj, [key]: value[key] };
            }
            return { ...obj, [key]: wrapProperties(value[key]) };
        }, {} as Record<string, unknown>);
    }

    if (Array.isArray(value)) {
        return value.map(wrapProperties);
    }

    return value;
}

export default ({ activity, children, isLast }: Props) => {
    const { pathTo } = useLocationHash();
    const actor = activity.relationships.actor;
    const properties = wrapProperties(activity.properties);
    const [showFullTime, setShowFullTime] = React.useState(false);

    // Use first and last name, fallback to "System"
    const username = actor?.username || 'System';

    // DiceBear avatar SVG as string
    const avatarSvg = createAvatar(identicon, { seed: username, size: 48 }).toString();

    return (
        <div className="flex gap-3 relative mb-6">
            {/* Timeline line */}
            {!isLast && (
                <div
                    className="absolute left-[1.2rem] top-[2rem] w-0.5 bg-hyper-accent z-0"
                    style={{
                        height: "calc(100% + 24px)",
                        transform: "translateX(-1px)",
                    }}
                />
            )}
            {/* Avatar */}
            <div className="flex-shrink-0 z-10">
                <div
                    className="w-10 h-10 rounded-lg bg-hyper-accent flex items-center justify-center text-hyper-primary font-semibold border-2 border-hyper-primary overflow-hidden"
                    dangerouslySetInnerHTML={{ __html: avatarSvg }}
                />
            </div>
            {/* Content */}
            <div className="flex-1 min-w-0">
                <div className="flex flex-wrap items-center gap-1 mb-2">
                    <span className="font-semibold text-white text-sm sm:text-base">
                        <Tooltip placement="top" content={actor?.email || 'System User'}>
                            <span>{username}</span>
                        </Tooltip>
                    </span>
                    <span className="text-hyper-accent text-xs font-medium bg-hyper-sidebar border border-hyper-primary backdrop-blur-lg p-1 px-2 rounded-xl">
                        <Link
                            to={`#${pathTo({ event: activity.event })}`}
                            className="transition-colors duration-75 active:text-cyan-400 hover:text-cyan-400"
                        >
                            {activity.event}
                        </Link>
                    </span>
                    <span className="flex gap-1 ml-2 text-gray-300">
                        {activity.isApi && (
                            <Tooltip placement="top" content="Using API Key">
                                <TerminalIcon className="w-4 h-4 text-hyper-accent" />
                            </Tooltip>
                        )}
                        {activity.event.startsWith('server:sftp.') && (
                            <Tooltip placement="top" content="Using SFTP">
                                <FolderOpenIcon className="w-4 h-4 text-hyper-accent" />
                            </Tooltip>
                        )}
                        {children}
                    </span>
                </div>
                <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-accent rounded-lg relative">
                    {activity.hasAdditionalMetadata && (
                        <div className="absolute top-1/2 right-4 -translate-y-1/2">
                            <ActivityLogMetaButton meta={activity.properties} />
                        </div>
                    )}
                    <div className="p-4">
                        <p className="text-hyper-primary text-xs sm:text-base leading-relaxed mb-3">
                            <Translate ns={'activity'} values={properties} i18nKey={activity.event.replace(':', '.')} />
                        </p>
                        <div className="flex items-center text-xs text-hyper-accent">
                            {activity.ip && (
                                <span>
                                    {activity.ip}
                                    <span className="text-gray-400">&nbsp;|&nbsp;</span>
                                </span>
                            )}
                            <span
                                onMouseEnter={() => setShowFullTime(true)}
                                onMouseLeave={() => setShowFullTime(false)}
                                className="cursor-pointer transition-colors"
                            >
                                {showFullTime
                                    ? format(activity.timestamp, 'MMM dd, yyyy HH:mm:ss')
                                    : formatDistanceToNowStrict(activity.timestamp, { addSuffix: true })}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};