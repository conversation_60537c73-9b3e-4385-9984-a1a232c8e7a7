import React, { memo } from 'react';
import { usePermissions } from '@rolexdev/themes/hyperv1/plugins/usePermissions';
import isEqual from 'react-fast-compare';

interface Props {
    action: string | string[];
    matchAny?: boolean;
    renderOnError?: React.ReactNode | null;
    children: React.ReactNode;
}

const Can = ({ action, matchAny = false, renderOnError, children }: Props) => {
    try {
        const can = usePermissions(action);

        // Additional safety check - if can is empty or all false, and no specific error component is provided,
        // we should still handle this gracefully
        if (!can || can.length === 0) {
            console.warn('No permissions returned from usePermissions hook');
            return <>{renderOnError}</>;
        }

        return (
            <>
                {(matchAny && can.filter((p: boolean) => p).length > 0) || (!matchAny && can.every((p: boolean) => p))
                    ? children
                    : renderOnError}
            </>
        );
    } catch (error) {
        console.error('Error in Can component:', error);
        // If there's an error, render the error fallback or nothing
        return <>{renderOnError}</>;
    }
};

export default memo(Can, isEqual);