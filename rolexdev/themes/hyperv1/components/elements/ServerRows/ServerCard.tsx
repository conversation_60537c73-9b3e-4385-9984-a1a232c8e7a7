'use client';
import React, { useRef, useEffect, useState, useMemo, useCallback } from 'react';
import Tooltip from '@rolexdev/themes/hyperv1/components/elements/tooltip/Tooltip';
import { cn } from '@rolexdev/themes/hyperv1/lib/utils';
import { Cpu, HardDrive, CircleX, Skull, MemoryStick, Users, Earth, Play, RotateCcw } from 'lucide-react';
import sendServerPowerState from '@rolexdev/themes/hyperv1/api/server/sendServerPowerState';
import { ShineBorder } from '@rolexdev/themes/hyperv1/components/elements/ShineBorder';

const IMAGE_EXTENSIONS = ['webp', 'png', 'jpg', 'jpeg', 'gif'];

const getImageCandidates = (
  egg_name: string,
  egg_id: number,
  nest_name: string,
  nest_id: number
): string[] => {
  const names = [egg_name, egg_id?.toString(), nest_name, nest_id?.toString()];
  return names
    .map(name => IMAGE_EXTENSIONS.map(ext => `/rolexdev/themes/hyperv1/server/card/${name}.${ext}`))
    .reduce((acc, val) => acc.concat(val), []);
};

export interface ServerCardProps {
  uuid?: string;
  name?: string;
  description?: string;
  status?: string;
  player?: string;
  ip?: string;
  cpu?: string;
  cpu_limit?: string;
  ram?: string;
  ram_limit?: string;
  disk?: string;
  disk_limit?: string;
  egg_name?: string;
  egg_id?: number;
  nest_name?: string;
  nest_id?: number;
  liveStats?: {
    cpuUsagePercent?: number;
    memoryUsageInBytes?: number;
    diskUsageInBytes?: number;
  };
}

const formatGB = (bytes?: number) => (!bytes ? '0' : (bytes / 1024 / 1024 / 1024).toFixed(2));

const getProgressColor = (value: number, max: number) => {
  const percent = (value / max) * 100;
  if (percent < 30) return 'bg-green-500';
  if (percent < 50) return 'bg-yellow-500';
  if (percent < 75) return 'bg-orange-500';
  return 'bg-red-500';
};

const getStatusDotColor = (status: string) => {
  switch (status) {
    case 'Offline': return 'bg-red-500';
    case 'Starting': return 'bg-yellow-400';
    case 'Running': return 'bg-green-500';
    case 'Stopping': return 'bg-orange-500';
    default: return 'bg-gray-400';
  }
};

export default function CardFlip({
  uuid = 'server-uuid',
  name = 'RolexDev SMP',
  description = 'This is Minecraft server',
  status = 'Running',
  player = '12 / 100',
  ip = 'mc.rolexdev.com',
  cpu = '85',
  cpu_limit = '100',
  ram = '4.5',
  ram_limit = '6',
  disk = '10',
  disk_limit = '50',
  egg_name = 'paper',
  egg_id = 3,
  nest_name = 'minecraft',
  nest_id = 1,
  liveStats,
}: ServerCardProps) {
  const [isFlipped, setIsFlipped] = useState(false);
  const [copied, setCopied] = useState(false);
  const ipRef = useRef<HTMLDivElement>(null);
  const [bannerSrc, setBannerSrc] = useState<string>('/rolexdev/themes/hyperv1/server/card/default.jpg');

  const cpuUsage = useMemo(
    () => liveStats?.cpuUsagePercent !== undefined ? liveStats.cpuUsagePercent.toFixed(2) : cpu,
    [liveStats, cpu]
  );
  const ramUsage = useMemo(
    () => liveStats?.memoryUsageInBytes !== undefined ? formatGB(liveStats.memoryUsageInBytes) : ram,
    [liveStats, ram]
  );
  const diskUsage = useMemo(
    () => liveStats?.diskUsageInBytes !== undefined ? formatGB(liveStats.diskUsageInBytes) : disk,
    [liveStats, disk]
  );
  const bannerCandidates = useMemo(
    () => getImageCandidates(egg_name.toLowerCase(), egg_id, nest_name.toLowerCase(), nest_id),
    [egg_name, egg_id, nest_name, nest_id]
  );

  useEffect(() => {
    let isMounted = true;
    setBannerSrc('/rolexdev/themes/hyperv1/server/card/default.jpg');
    const findBanner = async () => {
      for (const candidate of bannerCandidates) {
        try {
          const res = await fetch(candidate, { method: 'HEAD' });
          const contentType = res.headers.get('Content-Type') || '';
          if (res.status === 200 && contentType.startsWith('image/')) {
            if (isMounted) setBannerSrc(candidate);
            return;
          }
        } catch {
          // Ignore fetch errors
        }
      }
    };
    findBanner();
    return () => { isMounted = false; };
  }, [bannerCandidates]);

  const handleCopyIp = useCallback(() => {
    if (!ip) return;
    if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
      navigator.clipboard.writeText(ip);
    } else {
      const textarea = document.createElement('textarea');
      textarea.value = ip;
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
    }
    setCopied(true);
    setTimeout(() => setCopied(false), 1200);
  }, [ip]);

  const handlePowerAction = useCallback((signal: 'start' | 'stop' | 'restart' | 'kill') => {
    if (!uuid) return;
    sendServerPowerState(uuid, signal).catch(() => {});
  }, [uuid]);

  const truncatedName = useMemo(
    () => name.length > 12 ? name.slice(0, 12) + '…' : name,
    [name]
  );

  return (
    <div
      className="group relative h-[360px] min-h-[360px] min-w-[300px] w-[300px] [perspective:2000px]"
      onMouseEnter={() => setIsFlipped(true)}
      onMouseLeave={() => setIsFlipped(false)}
    >
      <div
        className={cn(
          'relative h-full w-full',
          '[transform-style:preserve-3d]',
          'transition-all duration-700',
          isFlipped
            ? '[transform:rotateY(180deg)]'
            : '[transform:rotateY(0deg)]',
        )}
      >
        {/* Front of card */}
        <div
          className={cn(
            'absolute inset-0 h-full w-full',
            '[transform:rotateY(0deg)] [backface-visibility:hidden]',
            'overflow-hidden rounded-2xl',
            'bg-hyper-primary-10',
            'border border-hyper-accent',
            'shadow-lg dark:shadow-xl',
            'transition-all duration-700',
            'group-hover:shadow-xl dark:group-hover:shadow-2xl',
            'group-hover:border-primary/20 dark:group-hover:border-primary/30',
            isFlipped ? 'opacity-0' : 'opacity-100',
          )}
        >
          <ShineBorder shineColor={["#df3050"]} borderWidth={2} className='z-[20]'/>
          <div className="absolute inset-0 backdrop-blur-[2px] z-[3]" />
          <div className="absolute inset-0 bg-black/70 z-[2]" />
          <img className={cn('object-cover absolute z-[1]')} src={bannerSrc} alt="Server Banner"/>
          <div className='absolute p-4 h-full w-full z-[4] rounded-2xl'>
            <div className='w-full h-[10%] flex justify-between'>
              <div className='px-2 h-[22px] text-hyper-primary bg-hyper-background-50 rounded-lg text-[10px] font-medium flex items-center justify-center'>
                <span className="uppercase">{egg_name}</span>
              </div>
              <div className='h-[22px] w-fit px-2 text-hyper-primary bg-hyper-background-50 rounded-lg text-[10px] font-medium flex items-center justify-center'>
                <span className="relative flex items-center">
                  <span
                    className={`absolute inline-flex h-[8px] w-[8px] rounded-full opacity-75 ${getStatusDotColor(status)} animate-ping`}
                  />
                  <span
                    className={`relative inline-flex h-[8px] w-[8px] rounded-full ${getStatusDotColor(status)}`}
                  />
                </span>
                <span className="ml-2 uppercase">{status}</span>
              </div>
            </div>
            <div className='w-full flex justify-center h-[70%]'>
              <div className='items-center justify-center flex flex-col'>
                <span className='font-bold text-2xl text-hyper-primary'>{truncatedName}</span>
                {description && (
                  <div className='w-[162px] h-[20px] bg-hyper-glass text-xs rounded-2xl text-hyper-primary font-normal flex items-center justify-center overflow-hidden'>
                    <span className='text-xs text-hyper-primary font-normal truncate w-full block px-2'>{description}</span>
                  </div>
                )}
              </div>
            </div>
            <div className='w-full h-[20%] flex justify-center'>
              {status === 'Running' && (
                <div className='w-[146px] h-[32px] bg-hyper-glass rounded-2xl font-semibold text-sm text-hyper-primary flex items-center justify-center'>
                  <Users height={16} className='text-hyper-accent mr-1'/>
                  <span className='mr-1'>{player}</span>
                  <span>Playing</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Back of card */}
        <div
          className={cn(
            'absolute inset-0 h-full w-full rounded-2xl',
            '[transform:rotateY(180deg)] [backface-visibility:hidden]',
            'bg-hyper-primary-30',
            'border border-hyper-accent',
            'shadow-lg dark:shadow-xl',
            'flex flex-col',
            'transition-all duration-700',
            'group-hover:shadow-xl dark:group-hover:shadow-2xl',
            'group-hover:border-primary/20 dark:group-hover:border-primary/30',
            !isFlipped ? 'opacity-0' : 'opacity-100',
          )}
        >
          <div className="absolute inset-0 bg-hyper-sidebar backdrop-blur-[2px] z-[3] rounded-2xl" />
          <div className="absolute inset-0 bg-black/70 z-[2] rounded-2xl" />
          <div className='absolute p-4 h-full w-full z-[4] rounded-2xl'>
            <div className='w-full h-[10%] flex justify-between text-hyper-primary'>
              <span className='font-bold text-base'>{truncatedName}</span>
              <div className='h-[22px] w-fit px-2 text-hyper-primary bg-hyper-primary-30 rounded-lg text-[10px] font-medium flex items-center justify-center'>
                <span className="relative flex items-center">
                  <span
                    className={`absolute inline-flex h-[8px] w-[8px] rounded-full opacity-75 ${getStatusDotColor(status)} animate-ping`}
                  />
                  <span
                    className={`relative inline-flex h-[8px] w-[8px] rounded-full ${getStatusDotColor(status)}`}
                  />
                </span>
                <span className="ml-2 uppercase">{status}</span>
              </div>
            </div>
            <div className='w-full h-[10%] flex justify-between text-hyper-primary'>
              <Tooltip content={copied ? "Copied!" : "Click To Copy"} placement='top' visible>
                <div
                  className='leading-loose button-primary h-[30px] w-[274px] text-hyper-primary bg-hyper-glass rounded-lg text-xs flex items-center px-3 cursor-pointer'
                  onClick={handleCopyIp}
                  onMouseEnter={() => setCopied(false)}
                  style={{ position: 'relative' }}
                  ref={ipRef}
                >
                  <Earth height={16} className='text-hyper-accent mr-1'/>
                  <span className='font-semibold mr-2'>IP Address :</span>
                  <span className='font-normal'>{ip}</span>
                </div>
              </Tooltip>
            </div>
            <div className='w-full flex justify-center h-[60%]'>
              <div className='items-center justify-center flex flex-col'>
                <div className='w-[274px] text-hyper-primary text-xs p-3'>
                  <div className='mb-4'>
                    <div className='mb-2 flex leading-normal'>
                      <Cpu height={14} className='text-hyper-accent mr-1'/>
                      <span className='font-semibold mr-1'>CPU :</span>
                      <span className='font-normal'>{cpuUsage}%</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='font-semibold mr-1 w-[35px]'>0%</span>
                      <div className="relative h-[5px] w-[180px] rounded-full bg-hyper-glass overflow-hidden">
                        <div
                          className={`${getProgressColor(Number(cpuUsage), Number(cpu_limit))} h-full rounded-full`}
                          style={{
                            width: `${(Number(cpuUsage) / Number(cpu_limit)) * 100}%`,
                            transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                          }}
                        />
                      </div>
                      <span className='font-semibold ml-1 w-[45px] flex justify-center'>{cpu_limit}%</span>
                    </div>
                  </div>
                  <div className='mb-4'>
                    <div className='mb-2 flex leading-normal'>
                      <MemoryStick height={16} className='text-hyper-accent mr-1'/>
                      <span className='font-semibold mr-1'>RAM :</span>
                      <span className='font-normal'>{ramUsage} GB</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='font-semibold mr-1 w-[35px]'>0GB</span>
                      <div className="relative h-[5px] w-[180px] rounded-full bg-hyper-glass overflow-hidden">
                        <div
                          className={`${getProgressColor(Number(ramUsage), Number(ram_limit))} h-full rounded-full`}
                          style={{
                            width: `${(Number(ramUsage) / Number(ram_limit)) * 100}%`,
                            transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                          }}
                        />
                      </div>
                      <span className='font-semibold ml-1 w-[45px] flex justify-center'>{ram_limit}GB</span>
                    </div>
                  </div>
                  <div className='mb-3'>
                    <div className='mb-2 flex leading-normal'>
                      <HardDrive height={16} className='text-hyper-accent mr-1'/>
                      <span className='font-semibold mr-1 '>DISK :</span>
                      <span className='font-normal'>{diskUsage} GB</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='font-semibold mr-1 w-[35px]'>0GB</span>
                      <div className="relative h-[5px] w-[180px] rounded-full bg-hyper-glass overflow-hidden">
                        <div
                          className={`${getProgressColor(Number(diskUsage), Number(disk_limit))} h-full rounded-full`}
                          style={{
                            width: `${(Number(diskUsage) / Number(disk_limit)) * 100}%`,
                            transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                          }}
                        />
                      </div>
                      <span className='font-semibold ml-1 w-[45px] flex justify-center'>{disk_limit}GB</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className='w-full h-[20%] inline-grid justify-between text-hyper-primary'>
              <div className='flex justify-between items-center'>
                {status === 'Offline' && (
                  <div className='bg-hyper-text-primary text-hyper-accent rounded-lg font-semibold text-xs w-[80px] h-[25px] flex justify-center items-center button-primary' onClick={() => handlePowerAction('start')}>
                    <Play height={16} className='text-hyper-accent mr-1 ml-[-8px]'/>
                    <span>START</span>
                  </div>
                )}
                {(status === 'Running' || status === 'Starting') && (
                  <div className='bg-hyper-text-primary text-hyper-accent rounded-lg font-semibold text-xs w-[80px] h-[25px] flex justify-center items-center button-primary' onClick={() => handlePowerAction('stop')}>
                    <CircleX height={16} className='text-hyper-accent mr-1 ml-[-8px]'/>
                    <span>STOP</span>
                  </div>
                )}
                {status === 'Running' && (
                  <div className='button-primary bg-hyper-glass rounded-lg font-semibold text-xs w-[80px] h-[25px] flex justify-center items-center'>
                    <Users height={16} className='text-hyper-accent mr-1 ml-[-8px]'/>
                    <span>{player}</span>
                  </div>
                )}
                {(status === 'Running' || status === 'Offline') && (
                  <div className='bg-hyper-text-primary text-hyper-accent rounded-lg font-semibold text-xs w-[80px] h-[25px] flex justify-center items-center button-primary' onClick={() => handlePowerAction('restart')}>
                    <RotateCcw height={16} className='text-hyper-accent mr-1 ml-[-8px]'/>
                    <span>RESET</span>
                  </div>
                )}
                {(status === 'Stopping' || status === 'Starting') && (
                  <div className='bg-hyper-text-primary text-hyper-accent rounded-lg font-semibold text-xs w-[80px] h-[25px] flex justify-center items-center button-primary' onClick={() => handlePowerAction('kill')}>
                    <Skull height={16} className='text-hyper-accent mr-1 ml-[-8px]'/>
                    <span>KILL</span>
                  </div>
                )}
              </div>
              <div className='mt-3 justify-center items-center flex'>
                <a
                  href={`/server/${uuid}`}
                  className="button-primary bg-hyper-primary text-hyper-primary w-[274px] h-[30px] rounded-lg justify-center items-center flex"
                  style={{ textDecoration: 'none' }}
                >
                  <span className="font-semibold text-xs">MANAGE SERVER</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <style>{`
        @keyframes slideIn {
          0% { transform: translateX(-100px); opacity: 0; }
          50% { transform: translateX(0); opacity: 0.8; }
          100% { transform: translateX(100px); opacity: 0; }
        }
      `}</style>
    </div>
  );
}