'use client';
import React, { useRef, useEffect, useState, useMemo, useCallback } from 'react';
import Tooltip from '@rolexdev/themes/hyperv1/components/elements/tooltip/Tooltip';
import { cn } from '@rolexdev/themes/hyperv1/lib/utils';
import { Cpu, HardDrive, CircleX, Skull, MemoryStick, Users, Earth, Play, RotateCcw } from 'lucide-react';
import sendServerPowerState from '@rolexdev/themes/hyperv1/api/server/sendServerPowerState';
import { ShineBorder } from '@rolexdev/themes/hyperv1/components/elements/ShineBorder';

const IMAGE_EXTENSIONS = ['webp', 'png', 'jpg', 'jpeg', 'gif'];

const getImageCandidates = (
  egg_name: string,
  egg_id: number,
  nest_name: string,
  nest_id: number
): string[] => {
  const names = [egg_name, egg_id?.toString(), nest_name, nest_id?.toString()];
  return names
    .map(name => IMAGE_EXTENSIONS.map(ext => `/rolexdev/themes/hyperv1/server/banner/${name}.${ext}`))
    .reduce((acc, val) => acc.concat(val), []);
};

export interface ServerRowProps {
  uuid?: string;
  name?: string;
  description?: string;
  status?: string;
  player?: string;
  ip?: string;
  cpu?: string;
  cpu_limit?: string;
  ram?: string;
  ram_limit?: string;
  disk?: string;
  disk_limit?: string;
  egg_name?: string;
  egg_id?: number;
  nest_name?: string;
  nest_id?: number;
  liveStats?: {
    cpuUsagePercent?: number;
    memoryUsageInBytes?: number;
    diskUsageInBytes?: number;
  };
  mobileView?: boolean;
  hideManageButton?: boolean;
}

const formatGB = (bytes?: number) => (!bytes ? '0' : (bytes / 1024 / 1024 / 1024).toFixed(2));

const getProgressColor = (value: number, max: number) => {
  const percent = (value / max) * 100;
  if (percent < 30) return 'bg-green-500';
  if (percent < 50) return 'bg-yellow-500';
  if (percent < 75) return 'bg-orange-500';
  return 'bg-red-500';
};

const getStatusDotColor = (status: string) => {
  switch (status) {
    case 'Offline': return 'bg-red-500';
    case 'Starting': return 'bg-yellow-400';
    case 'Running': return 'bg-green-500';
    case 'Stopping': return 'bg-orange-500';
    default: return 'bg-gray-400';
  }
};

export default function ServerRow({
  uuid = 'server-uuid',
  name = 'RolexDev SMP',
  description = 'This is Minecraft server',
  status = 'Running',
  player = '12 / 100',
  ip = 'mc.rolexdev.com',
  cpu = '85',
  cpu_limit = '100',
  ram = '4.5',
  ram_limit = '6',
  disk = '10',
  disk_limit = '50',
  egg_name = 'paper',
  egg_id = 3,
  nest_name = 'minecraft',
  nest_id = 1,
  liveStats,
  mobileView = false,
  hideManageButton = false,
}: ServerRowProps) {
  const [copied, setCopied] = useState(false);
  const ipRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [bannerSrc, setBannerSrc] = useState<string>('/rolexdev/themes/hyperv1/server/banner/default.png');
  const [containerWidth, setContainerWidth] = useState<number>(0);

  const cpuUsage = useMemo(
    () => liveStats?.cpuUsagePercent !== undefined ? liveStats.cpuUsagePercent.toFixed(2) : cpu,
    [liveStats, cpu]
  );
  const ramUsage = useMemo(
    () => liveStats?.memoryUsageInBytes !== undefined ? formatGB(liveStats.memoryUsageInBytes) : ram,
    [liveStats, ram]
  );
  const diskUsage = useMemo(
    () => liveStats?.diskUsageInBytes !== undefined ? formatGB(liveStats.diskUsageInBytes) : disk,
    [liveStats, disk]
  );
  const bannerCandidates = useMemo(
    () => getImageCandidates(egg_name.toLowerCase(), egg_id, nest_name.toLowerCase(), nest_id),
    [egg_name, egg_id, nest_name, nest_id]
  );

  useEffect(() => {
    let isMounted = true;
    setBannerSrc('/rolexdev/themes/hyperv1/server/banner/default.png');
    const findBanner = async () => {
      for (const candidate of bannerCandidates) {
        try {
          const res = await fetch(candidate, { method: 'HEAD' });
          const contentType = res.headers.get('Content-Type') || '';
          if (res.status === 200 && contentType.startsWith('image/')) {
            if (isMounted) setBannerSrc(candidate);
            return;
          }
        } catch {
          // Ignore fetch errors
        }
      }
    };
    findBanner();
    return () => { isMounted = false; };
  }, [bannerCandidates]);

  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
    };

    // Set initial container width on mount
    handleResize();
    
    // Also set initial width based on window if container measurement fails
    if (typeof window !== 'undefined' && containerWidth === 0) {
      setTimeout(() => {
        if (containerRef.current && containerWidth === 0) {
          setContainerWidth(containerRef.current.offsetWidth || window.innerWidth);
        }
      }, 0);
    }

    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [containerWidth]);

  const handleCopyIp = useCallback(() => {
    if (!ip) return;
    if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
      navigator.clipboard.writeText(ip);
    } else {
      const textarea = document.createElement('textarea');
      textarea.value = ip;
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
    }
    setCopied(true);
    setTimeout(() => setCopied(false), 1200);
  }, [ip]);

  const handlePowerAction = useCallback((signal: 'start' | 'stop' | 'restart' | 'kill') => {
    if (!uuid) return;
    sendServerPowerState(uuid, signal).catch(() => {});
  }, [uuid]);

  const truncatedName = useMemo(() => {
    const maxLen = mobileView ? 12 : 26;
    return name.length > maxLen ? name.slice(0, maxLen) + '…' : name;
  }, [name, mobileView]);

  const shouldHideIcon = useMemo(() => {
    // Use window width as fallback for initial load
    const width = containerWidth > 0 ? containerWidth : (typeof window !== 'undefined' ? window.innerWidth : 0);
    return width < 1045;
  }, [containerWidth]);

  const shouldUseCompactLayout = useMemo(() => {
    // Use window width as fallback for initial load
    const width = containerWidth > 0 ? containerWidth : (typeof window !== 'undefined' ? window.innerWidth : 0);
    return width < 918;
  }, [containerWidth]);

  const shouldUseStackedLayout = useMemo(() => {
    // Use window width as fallback for initial load
    const width = containerWidth > 0 ? containerWidth : (typeof window !== 'undefined' ? window.innerWidth : 0);
    return width < 572;
  }, [containerWidth]);

  // Usage bar color for each resource
  const getBarColor = (type: 'cpu' | 'ram' | 'disk') => {
    if (type === 'cpu') return 'bg-yellow-400';
    if (type === 'ram') return 'bg-green-400';
    if (type === 'disk') return 'bg-red-400';
    return '';
  };

  // --- Layout ---
  if (mobileView) {
    // MOBILE LAYOUT
    return (
      <div className="server-row flex flex-col bg-hyper-sidebar border border-hyper-accent rounded-2xl w-full max-w-[340px] min-w-[280px] h-auto min-h-[320px] p-3 relative mx-auto">
        {/* Top: Status badge */}
        <div className="flex justify-between items-center mb-3">
          <div className="flex items-center">
            <span className={`inline-flex h-[10px] w-[10px] rounded-full mr-2 ${getStatusDotColor(status)}`} />
            <span className="uppercase text-xs font-bold text-hyper-primary bg-hyper-background-50 px-3 py-1 rounded-lg">
              {status}
            </span>
          </div>
          <span className="uppercase text-[10px] font-medium text-hyper-primary bg-hyper-background-50 rounded-lg px-2 py-1">{egg_name}</span>
        </div>
        
        {/* Name, Desc */}
        <div className="flex flex-col items-center mb-3">
          <span className="font-bold text-lg text-hyper-primary text-center">{truncatedName}</span>
          <span className="text-xs text-hyper-primary font-normal mt-1 text-center line-clamp-2">{description}</span>
        </div>
        
        {/* IP */}
        <div className="flex justify-center mb-4">
          <Tooltip content={copied ? "Copied!" : "Click To Copy"} placement="top" visible>
            <div
              className="inline-flex items-center px-3 py-2 bg-hyper-glass rounded-lg text-xs text-hyper-primary border border-hyper-accent cursor-pointer w-full max-w-[280px] justify-center"
              onClick={handleCopyIp}
              onMouseEnter={() => setCopied(false)}
              ref={ipRef}
            >
              <Earth height={16} className="text-hyper-accent mr-2" />
              <span className="font-semibold mr-2">IP:</span>
              <span className="font-normal truncate">{ip}</span>
            </div>
          </Tooltip>
        </div>
        
        {/* Usage Bars */}
        <div className="flex flex-col gap-3 mb-4">
          {/* CPU */}
          <div className="flex items-center w-[280px]">
            <Cpu height={16} className="text-yellow-400 mr-3 flex-shrink-0" />
            <span className="w-[30px] text-xs text-hyper-primary">0%</span>
            <div className="w-[120px] mx-2 h-[8px] rounded-full bg-hyper-glass overflow-hidden relative">
              <div
                className="absolute left-0 top-0 h-full rounded-full bg-yellow-400"
                style={{
                  width: `${Math.min((Number(cpuUsage) / Number(cpu_limit)) * 100, 100)}%`,
                  transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                }}
              />
            </div>
            <span className="w-[35px] text-xs text-hyper-primary text-right">{cpu_limit}%</span>
            <span className="ml-2 text-xs text-hyper-primary font-semibold w-[40px] text-right">{cpuUsage}%</span>
          </div>
          {/* RAM */}
          <div className="flex items-center w-[280px]">
            <MemoryStick height={16} className="text-green-400 mr-3 flex-shrink-0" />
            <span className="w-[30px] text-xs text-hyper-primary">0GB</span>
            <div className="w-[120px] mx-2 h-[8px] rounded-full bg-hyper-glass overflow-hidden relative">
              <div
                className="absolute left-0 top-0 h-full rounded-full bg-green-400"
                style={{
                  width: `${Math.min((Number(ramUsage) / Number(ram_limit)) * 100, 100)}%`,
                  transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                }}
              />
            </div>
            <span className="w-[35px] text-xs text-hyper-primary text-right">{ram_limit}GB</span>
            <span className="ml-2 text-xs text-hyper-primary font-semibold w-[40px] text-right">{ramUsage}GB</span>
          </div>
          {/* DISK */}
          <div className="flex items-center w-[280px]">
            <HardDrive height={16} className="text-red-400 mr-3 flex-shrink-0" />
            <span className="w-[30px] text-xs text-hyper-primary">0GB</span>
            <div className="w-[120px] mx-2 h-[8px] rounded-full bg-hyper-glass overflow-hidden relative">
              <div
                className="absolute left-0 top-0 h-full rounded-full bg-red-400"
                style={{
                  width: `${Math.min((Number(diskUsage) / Number(disk_limit)) * 100, 100)}%`,
                  transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                }}
              />
            </div>
            <span className="w-[35px] text-xs text-hyper-primary text-right">{disk_limit}GB</span>
            <span className="ml-2 text-xs text-hyper-primary font-semibold w-[40px] text-right">{diskUsage}GB</span>
          </div>
        </div>
        
        {/* Actions */}
        <div className="flex flex-row gap-2 justify-center mb-3 w-[280px] mx-auto">
          <button
            className="button-primary flex items-center justify-center w-[80px] h-[32px] rounded-lg bg-hyper-glass text-hyper-accent font-semibold text-xs"
            onClick={() => handlePowerAction('restart')}
          >
            <RotateCcw height={14} className="text-hyper-accent mr-1" />
            RESET
          </button>
          <div className="button-primary flex items-center justify-center w-[80px] h-[32px] rounded-lg bg-hyper-glass text-hyper-accent font-semibold text-xs">
            <Users height={14} className="text-hyper-accent mr-1" />
            {player}
          </div>
          {status === 'Offline' ? (
            <button
              className="button-primary flex items-center justify-center w-[80px] h-[32px] rounded-lg bg-hyper-glass text-hyper-accent font-semibold text-xs"
              onClick={() => handlePowerAction('start')}
            >
              <Play height={14} className="text-hyper-accent mr-1" />
              START
            </button>
          ) : (
            <button
              className="button-primary flex items-center justify-center w-[80px] h-[32px] rounded-lg bg-hyper-glass text-hyper-accent font-semibold text-xs"
              onClick={() => handlePowerAction('stop')}
            >
              <CircleX height={14} className="text-hyper-accent mr-1" />
              STOP
            </button>
          )}
        </div>
        
        {/* Manage Server - Only show if not hidden */}
        {!hideManageButton && (
          <a
            href={`/server/${uuid}`}
            className="button-primary bg-hyper-primary text-hyper-primary w-full h-[32px] rounded-lg justify-center items-center flex"
            style={{ textDecoration: 'none' }}
          >
            <span className="font-semibold text-xs">MANAGE SERVER</span>
          </a>
        )}
      </div>
    );
  }

  // DESKTOP LAYOUT
  return (
    <div 
      ref={containerRef}
      className={cn(
        "server-row flex bg-hyper-sidebar border border-hyper-accent rounded-2xl w-full max-w-[1200px] p-4 relative",
        shouldUseStackedLayout
          ? "flex-col h-auto min-h-[300px] justify-start gap-3"
          : shouldUseCompactLayout 
            ? "flex-col h-auto min-h-[220px] justify-start gap-4" 
            : "flex-row items-center h-[160px] min-h-[160px] justify-between"
      )}
    >
      {shouldUseStackedLayout ? (
        <>
          {/* Stacked Layout - Width < 572px */}
          {/* Row 1: Server Details */}
          <div className="flex items-center w-full mt-[20px]">
            {/* Server Icon/Image - Hide when width < 1045px */}
            {!shouldHideIcon && (
              <div className="h-[80px] w-[80px] rounded-xl overflow-hidden bg-hyper-glass flex-shrink-0 flex items-center justify-center border border-hyper-accent mr-4">
                <img
                  src={bannerSrc}
                  alt="Server Icon"
                  className="object-cover w-full h-full"
                />
              </div>
            )}
            {/* Name, Desc, IP */}
            <div className="flex-1">
              <div className='mb-1'>
                <span className="font-bold text-xl text-hyper-primary truncate block">{truncatedName}</span>
                {description && (
                  <div className='w-full h-[18px] bg-hyper-glass text-xs rounded-2xl text-hyper-primary font-normal flex items-center justify-center overflow-hidden mt-1'>
                    <span className='text-xs text-hyper-primary font-normal truncate w-full block px-2'>
                      {description}
                    </span>
                  </div>
                )}
              </div>
              <div className='w-fit px-2 h-[20px] text-hyper-primary bg-hyper-primary-30 rounded-lg text-[10px] font-medium flex items-center justify-center mb-2'>
                <span className="uppercase">{egg_name}</span>
              </div>
              <Tooltip content={copied ? "Copied!" : "Click To Copy"} placement='top' visible>
                <div
                  className='leading-loose button-primary h-[28px] w-full text-hyper-primary bg-hyper-glass rounded-lg text-xs flex items-center px-3 cursor-pointer'
                  onClick={handleCopyIp}
                  onMouseEnter={() => setCopied(false)}
                  style={{ position: 'relative' }}
                  ref={ipRef}
                >
                  <Earth height={14} className='text-hyper-accent mr-1'/>
                  <span className='font-semibold mr-2'>IP:</span>
                  <span className='font-normal truncate'>{ip}</span>
                </div>
              </Tooltip>
            </div>
            {/* Status Badge - Positioned absolutely */}
            <div className='absolute top-3 right-3 h-[20px] w-fit px-2 text-hyper-primary bg-hyper-background-50 rounded-lg text-[10px] font-medium flex items-center justify-center z-10'>
              <span className="relative flex items-center">
                <span
                  className={`absolute inline-flex h-[6px] w-[6px] rounded-full opacity-75 ${getStatusDotColor(status)} animate-ping`}
                />
                <span
                  className={`relative inline-flex h-[6px] w-[6px] rounded-full ${getStatusDotColor(status)}`}
                />
              </span>
              <span className="ml-2 uppercase">{status}</span>
            </div>
          </div>

          {/* Row 2: CPU and RAM Status */}
          <div className="flex gap-4 w-full">
            <div className='flex-1 text-hyper-primary text-xs'>
              <div className='mb-1 flex leading-normal'>
                <Cpu height={14} className='text-hyper-accent mr-1'/>
                <span className='font-semibold mr-1'>CPU:</span>
                <span className='font-normal'>{cpuUsage}%</span>
              </div>
              <div className='flex items-center justify-between'>
                <span className='font-semibold mr-1 w-[30px]'>0%</span>
                <div className="relative h-[5px] flex-1 mx-2 rounded-full bg-hyper-glass overflow-hidden">
                  <div
                    className={`${getProgressColor(Number(cpuUsage), Number(cpu_limit))} h-full rounded-full`}
                    style={{
                      width: `${Math.min((Number(cpuUsage) / Number(cpu_limit)) * 100, 100)}%`,
                      transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                    }}
                  />
                </div>
                <span className='font-semibold ml-1 w-[35px] text-center'>{cpu_limit}%</span>
              </div>
            </div>
            <div className='flex-1 text-hyper-primary text-xs'>
              <div className='mb-1 flex leading-normal'>
                <MemoryStick height={14} className='text-hyper-accent mr-1'/>
                <span className='font-semibold mr-1'>RAM:</span>
                <span className='font-normal'>{ramUsage}GB</span>
              </div>
              <div className='flex items-center justify-between'>
                <span className='font-semibold mr-1 w-[30px]'>0GB</span>
                <div className="relative h-[5px] flex-1 mx-2 rounded-full bg-hyper-glass overflow-hidden">
                  <div
                    className={`${getProgressColor(Number(ramUsage), Number(ram_limit))} h-full rounded-full`}
                    style={{
                      width: `${Math.min((Number(ramUsage) / Number(ram_limit)) * 100, 100)}%`,
                      transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                    }}
                  />
                </div>
                <span className='font-semibold ml-1 w-[35px] text-center'>{ram_limit}GB</span>
              </div>
            </div>
          </div>

          {/* Row 3: Disk Status */}
          <div className='text-hyper-primary text-xs w-full'>
            <div className='mb-1 flex leading-normal'>
              <HardDrive height={14} className='text-hyper-accent mr-1'/>
              <span className='font-semibold mr-1'>DISK:</span>
              <span className='font-normal'>{diskUsage}GB</span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='font-semibold mr-1 w-[30px]'>0GB</span>
              <div className="relative h-[5px] flex-1 mx-2 rounded-full bg-hyper-glass overflow-hidden">
                <div
                  className={`${getProgressColor(Number(diskUsage), Number(disk_limit))} h-full rounded-full`}
                  style={{
                    width: `${Math.min((Number(diskUsage) / Number(disk_limit)) * 100, 100)}%`,
                    transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                  }}
                />
              </div>
              <span className='font-semibold ml-1 w-[40px] text-center'>{disk_limit}GB</span>
            </div>
          </div>

          {/* Row 4: Action Buttons */}
          <div className='flex justify-between items-center gap-2 w-full'>
            {status === 'Offline' && (
              <div className='bg-hyper-text-primary text-hyper-accent rounded-lg font-semibold text-xs w-[70px] h-[25px] flex justify-center items-center button-primary cursor-pointer' onClick={() => handlePowerAction('start')}>
                <Play height={14} className='text-hyper-accent mr-1 ml-[-8px]'/>
                <span>START</span>
              </div>
            )}
            {(status === 'Running' || status === 'Starting') && (
              <div className='bg-hyper-text-primary text-hyper-accent rounded-lg font-semibold text-xs w-[70px] h-[25px] flex justify-center items-center button-primary cursor-pointer' onClick={() => handlePowerAction('stop')}>
                <CircleX height={14} className='text-hyper-accent mr-1 ml-[-8px]'/>
                <span>STOP</span>
              </div>
            )}
            {status === 'Running' && (
              <div className='button-primary bg-hyper-glass rounded-lg font-semibold text-xs w-[70px] h-[25px] flex justify-center items-center'>
                <Users height={14} className='text-hyper-accent mr-1 ml-[-8px]'/>
                <span>{player}</span>
              </div>
            )}
            {(status === 'Running' || status === 'Offline') && (
              <div className='bg-hyper-text-primary text-hyper-accent rounded-lg font-semibold text-xs w-[70px] h-[25px] flex justify-center items-center button-primary cursor-pointer' onClick={() => handlePowerAction('restart')}>
                <RotateCcw height={14} className='text-hyper-accent mr-1 ml-[-8px]'/>
                <span>RESET</span>
              </div>
            )}
            {(status === 'Stopping' || status === 'Starting') && (
              <div className='bg-hyper-text-primary text-hyper-accent rounded-lg font-semibold text-xs w-[70px] h-[25px] flex justify-center items-center button-primary cursor-pointer' onClick={() => handlePowerAction('kill')}>
                <Skull height={14} className='text-hyper-accent mr-1 ml-[-8px]'/>
                <span>KILL</span>
              </div>
            )}
            {/* Manage Server - Only show if not hidden */}
            {!hideManageButton && (
              <a
                href={`/server/${uuid}`}
                className="button-primary bg-hyper-primary text-hyper-primary w-[100px] h-[25px] rounded-lg justify-center items-center flex"
                style={{ textDecoration: 'none' }}
              >
                <span className="font-semibold text-xs">MANAGE</span>
              </a>
            )}
          </div>
        </>
      ) : shouldUseCompactLayout ? (
        <>
          {/* Compact Layout - Width < 918px */}
          {/* Top Row: Server Details and CPU/RAM */}
          <div className="flex items-center justify-between w-full">
            {/* Left: Icon + Name/Desc + IP */}
            <div className="flex items-center w-max">
              {/* Server Icon/Image - Hide when width < 1045px */}
              {!shouldHideIcon && (
                <div className="h-[120px] w-[120px] rounded-xl overflow-hidden bg-hyper-glass flex-shrink-0 flex items-center justify-center border border-hyper-accent mr-6">
                  <img
                    src={bannerSrc}
                    alt="Server Icon"
                    className="object-cover w-full h-full"
                  />
                </div>
              )}
              {/* Name, Desc, IP */}
              <div className="inline-grid max-w-[245px] h-[120px] justify-between">
                <div className='inline-grid h-fit'>
                  <span className="font-bold text-2xl text-hyper-primary truncate h-[30px]">{truncatedName}</span>
                  {description && (
                    <div className='w-full h-[20px] bg-hyper-glass text-xs rounded-2xl text-hyper-primary font-normal flex items-center justify-center overflow-hidden mt-1'>
                      <span className='text-xs text-hyper-primary font-normal truncate w-full block px-2'>
                        {description}
                      </span>
                    </div>
                  )}
                </div>
                <div className='w-fit px-2 h-[22px] text-hyper-primary bg-hyper-primary-30 rounded-lg text-[10px] font-medium flex items-center justify-center z-10'>
                  <span className="uppercase">{egg_name}</span>
                </div>
                <div className="self-end">
                  <Tooltip content={copied ? "Copied!" : "Click To Copy"} placement='top' visible>
                    <div
                      className='leading-loose button-primary h-[30px] w-[full] text-hyper-primary bg-hyper-glass rounded-lg text-xs flex items-center px-3 cursor-pointer'
                      onClick={handleCopyIp}
                      onMouseEnter={() => setCopied(false)}
                      style={{ position: 'relative' }}
                      ref={ipRef}
                    >
                      <Earth height={16} className='text-hyper-accent mr-1 ml-[-5px]'/>
                      <span className='font-semibold mr-2 w-max'>IP Address :</span>
                      <span className='font-normal truncate'>{ip}</span>
                    </div>
                  </Tooltip>
                </div>
              </div>
            </div>

            {/* Right: CPU/RAM Usage Bars */}
            <div className="inline-grid justify-between items-center w-[268px] text-hyper-primary text-xs h-[120px]">
              <div className='self-start'>
                <div className='mb-2 flex leading-normal'>
                  <Cpu height={14} className='text-hyper-accent mr-1'/>
                  <span className='font-semibold mr-1'>CPU :</span>
                  <span className='font-normal'>{cpuUsage}%</span>
                </div>
                <div className='flex items-center justify-between'>
                  <span className='font-semibold mr-1 w-[35px]'>0%</span>
                  <div className="relative h-[5px] w-[180px] rounded-full bg-hyper-glass overflow-hidden">
                    <div
                      className={`${getProgressColor(Number(cpuUsage), Number(cpu_limit))} h-full rounded-full`}
                      style={{
                        width: `${Math.min((Number(cpuUsage) / Number(cpu_limit)) * 100, 100)}%`,
                        transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                      }}
                    />
                  </div>
                  <span className='font-semibold ml-1 w-[45px] flex justify-center'>{cpu_limit}%</span>
                </div>
              </div>
              <div className='self-end w-[268px]'>
                <div className='mb-2 flex leading-normal'>
                  <MemoryStick height={16} className='text-hyper-accent mr-1'/>
                  <span className='font-semibold mr-1'>RAM :</span>
                  <span className='font-normal'>{ramUsage} GB</span>
                </div>
                <div className='flex items-center justify-between'>
                  <span className='font-semibold mr-1 w-[35px]'>0GB</span>
                  <div className="relative h-[5px] w-[180px] rounded-full bg-hyper-glass overflow-hidden">
                    <div
                      className={`${getProgressColor(Number(ramUsage), Number(ram_limit))} h-full rounded-full`}
                      style={{
                        width: `${Math.min((Number(ramUsage) / Number(ram_limit)) * 100, 100)}%`,
                        transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                      }}
                    />
                  </div>
                  <span className='font-semibold ml-1 w-[45px] flex justify-center'>{ram_limit}GB</span>
                </div>
              </div>
              {/* Status Badge - Positioned absolutely */}
              <div className='absolute top-3 right-3 h-[22px] w-fit px-2 text-hyper-primary bg-hyper-background-50 rounded-lg text-[10px] font-medium flex items-center justify-center z-10'>
                <span className="relative flex items-center">
                  <span
                    className={`absolute inline-flex h-[8px] w-[8px] rounded-full opacity-75 ${getStatusDotColor(status)} animate-ping`}
                  />
                  <span
                    className={`relative inline-flex h-[8px] w-[8px] rounded-full ${getStatusDotColor(status)}`}
                  />
                </span>
                <span className="ml-2 uppercase">{status}</span>
              </div>
            </div>
          </div>

          {/* Bottom Row: Action Buttons and Disk Usage */}
          <div className="flex items-center justify-between w-full">
            {/* Left: Action Buttons */}
            <div className='flex justify-between items-center gap-2 w-[245px]'>
              {status === 'Offline' && (
                <div className='bg-hyper-text-primary text-hyper-accent rounded-lg font-semibold text-xs w-[80px] h-[25px] flex justify-center items-center button-primary cursor-pointer' onClick={() => handlePowerAction('start')}>
                  <Play height={16} className='text-hyper-accent mr-1 ml-[-8px]'/>
                  <span>START</span>
                </div>
              )}
              {(status === 'Running' || status === 'Starting') && (
                <div className='bg-hyper-text-primary text-hyper-accent rounded-lg font-semibold text-xs w-[80px] h-[25px] flex justify-center items-center button-primary cursor-pointer' onClick={() => handlePowerAction('stop')}>
                  <CircleX height={16} className='text-hyper-accent mr-1 ml-[-8px]'/>
                  <span>STOP</span>
                </div>
              )}
              {status === 'Running' && (
                <div className='button-primary bg-hyper-glass rounded-lg font-semibold text-xs w-[80px] h-[25px] flex justify-center items-center'>
                  <Users height={16} className='text-hyper-accent mr-1 ml-[-8px]'/>
                  <span>{player}</span>
                </div>
              )}
              {(status === 'Running' || status === 'Offline') && (
                <div className='bg-hyper-text-primary text-hyper-accent rounded-lg font-semibold text-xs w-[80px] h-[25px] flex justify-center items-center button-primary cursor-pointer' onClick={() => handlePowerAction('restart')}>
                  <RotateCcw height={16} className='text-hyper-accent mr-1 ml-[-8px]'/>
                  <span>RESET</span>
                </div>
              )}
              {(status === 'Stopping' || status === 'Starting') && (
                <div className='bg-hyper-text-primary text-hyper-accent rounded-lg font-semibold text-xs w-[80px] h-[25px] flex justify-center items-center button-primary cursor-pointer' onClick={() => handlePowerAction('kill')}>
                  <Skull height={16} className='text-hyper-accent mr-1 ml-[-8px]'/>
                  <span>KILL</span>
                </div>
              )}
              {/* Manage Server - Only show if not hidden */}
              {!hideManageButton && (
                <a
                  href={`/server/${uuid}`}
                  className="button-primary bg-hyper-primary text-hyper-primary w-[120px] h-[25px] rounded-lg justify-center items-center flex"
                  style={{ textDecoration: 'none' }}
                >
                  <span className="font-semibold text-xs">MANAGE SERVER</span>
                </a>
              )}
            </div>

            {/* Right: Disk Usage */}
            <div className='text-hyper-primary text-xs w-[268px]'>
              <div className='mb-2 flex leading-normal'>
                <HardDrive height={16} className='text-hyper-accent mr-1'/>
                <span className='font-semibold mr-1'>DISK :</span>
                <span className='font-normal'>{diskUsage} GB</span>
              </div>
              <div className='flex items-center justify-between'>
                <span className='font-semibold mr-1 w-[35px]'>0GB</span>
                <div className="relative h-[5px] w-[180px] rounded-full bg-hyper-glass overflow-hidden">
                  <div
                    className={`${getProgressColor(Number(diskUsage), Number(disk_limit))} h-full rounded-full`}
                    style={{
                      width: `${Math.min((Number(diskUsage) / Number(disk_limit)) * 100, 100)}%`,
                      transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                    }}
                  />
                </div>
                <span className='font-semibold ml-1 w-[45px] flex justify-center'>{disk_limit}GB</span>
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          {/* Normal Layout - Width >= 918px */}
          {/* Left Section: Icon + Name/Desc + IP */}
          <div className="flex items-center w-max">
            {/* Server Icon/Image - Hide when width < 1045px */}
            {!shouldHideIcon && (
              <div className="h-[120px] w-[120px] rounded-xl overflow-hidden bg-hyper-glass flex-shrink-0 flex items-center justify-center border border-hyper-accent mr-6">
                <img
                  src={bannerSrc}
                  alt="Server Icon"
                  className="object-cover w-full h-full"
                />
              </div>
            )}
            {/* Name, Desc, IP */}
            <div className="inline-grid max-w-[245px] h-[120px] justify-between">
              <div className='inline-grid h-fit'>
                <span className="font-bold text-2xl text-hyper-primary truncate h-[30px]">{truncatedName}</span>
                {description && (
                  <div className='w-full h-[20px] bg-hyper-glass text-xs rounded-2xl text-hyper-primary font-normal flex items-center justify-center overflow-hidden mt-1'>
                    <span className='text-xs text-hyper-primary font-normal truncate w-full block px-2'>
                      {description}
                    </span>
                  </div>
                )}
              </div>
              <div className='w-fit px-2 h-[22px] text-hyper-primary bg-hyper-primary-30 rounded-lg text-[10px] font-medium flex items-center justify-center z-10'>
                <span className="uppercase">{egg_name}</span>
              </div>
              <div className="self-end">
                <Tooltip content={copied ? "Copied!" : "Click To Copy"} placement='top' visible>
                  <div
                    className='leading-loose button-primary h-[30px] w-[full] text-hyper-primary bg-hyper-glass rounded-lg text-xs flex items-center px-3 cursor-pointer'
                    onClick={handleCopyIp}
                    onMouseEnter={() => setCopied(false)}
                    style={{ position: 'relative' }}
                    ref={ipRef}
                  >
                    <Earth height={16} className='text-hyper-accent mr-1 ml-[-5px]'/>
                    <span className='font-semibold mr-2 w-max'>IP Address :</span>
                    <span className='font-normal truncate'>{ip}</span>
                  </div>
                </Tooltip>
              </div>
            </div>
          </div>

          {/* Middle Section: Usage Bars */}
          <div className="inline-grid justify-between items-center w-[268px] text-hyper-primary text-xs h-full">
            <div className='self-start'>
              <div className='mb-2 flex leading-normal'>
                <Cpu height={14} className='text-hyper-accent mr-1'/>
                <span className='font-semibold mr-1'>CPU :</span>
                <span className='font-normal'>{cpuUsage}%</span>
              </div>
              <div className='flex items-center justify-between'>
                <span className='font-semibold mr-1 w-[35px]'>0%</span>
                <div className="relative h-[5px] w-[180px] rounded-full bg-hyper-glass overflow-hidden">
                  <div
                    className={`${getProgressColor(Number(cpuUsage), Number(cpu_limit))} h-full rounded-full`}
                    style={{
                      width: `${Math.min((Number(cpuUsage) / Number(cpu_limit)) * 100, 100)}%`,
                      transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                    }}
                  />
                </div>
                <span className='font-semibold ml-1 w-[45px] flex justify-center'>{cpu_limit}%</span>
              </div>
            </div>
            <div className='self-end w-[268px]'>
              <div className='mb-2 flex leading-normal'>
                <MemoryStick height={16} className='text-hyper-accent mr-1'/>
                <span className='font-semibold mr-1'>RAM :</span>
                <span className='font-normal'>{ramUsage} GB</span>
              </div>
              <div className='flex items-center justify-between'>
                <span className='font-semibold mr-1 w-[35px]'>0GB</span>
                <div className="relative h-[5px] w-[180px] rounded-full bg-hyper-glass overflow-hidden">
                  <div
                    className={`${getProgressColor(Number(ramUsage), Number(ram_limit))} h-full rounded-full`}
                    style={{
                      width: `${Math.min((Number(ramUsage) / Number(ram_limit)) * 100, 100)}%`,
                      transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                    }}
                  />
                </div>
                <span className='font-semibold ml-1 w-[45px] flex justify-center'>{ram_limit}GB</span>
              </div>
            </div>
          </div>

          {/* Right Section: Status + Actions */}
          <div className="flex flex-col items-end justify-between w-[320px] min-w-[320px] h-full">
            {/* Status Badge */}
            <div className='w-full h-fit'>
              <div className='text-hyper-primary text-xs w-[268px]'>
                <div className='mb-2 flex leading-normal'>
                  <HardDrive height={16} className='text-hyper-accent mr-1'/>
                  <span className='font-semibold mr-1 '>DISK :</span>
                  <span className='font-normal'>{diskUsage} GB</span>
                </div>
                <div className='flex items-center justify-between'>
                  <span className='font-semibold mr-1 w-[35px]'>0GB</span>
                  <div className="relative h-[5px] w-[180px] rounded-full bg-hyper-glass overflow-hidden">
                    <div
                      className={`${getProgressColor(Number(diskUsage), Number(disk_limit))} h-full rounded-full`}
                      style={{
                        width: `${Math.min((Number(diskUsage) / Number(disk_limit)) * 100, 100)}%`,
                        transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                      }}
                    />
                  </div>
                  <span className='font-semibold ml-1 w-[45px] flex justify-center'>{disk_limit}GB</span>
                </div>
              </div>
                <div className='absolute top-3 right-3 h-[22px] w-fit px-2 text-hyper-primary bg-hyper-background-50 rounded-lg text-[10px] font-medium flex items-center justify-center z-10'>
                  <span className="relative flex items-center">
                    <span
                      className={`absolute inline-flex h-[8px] w-[8px] rounded-full opacity-75 ${getStatusDotColor(status)} animate-ping`}
                    />
                    <span
                      className={`relative inline-flex h-[8px] w-[8px] rounded-full ${getStatusDotColor(status)}`}
                    />
                  </span>
                  <span className="ml-2 uppercase">{status}</span>
                </div>
            </div>
            {/* Action Buttons */}
            <div className='flex justify-between items-center w-full'>
              {status === 'Offline' && (
                <div className='bg-hyper-text-primary text-hyper-accent rounded-lg font-semibold text-xs w-[80px] h-[25px] flex justify-center items-center button-primary cursor-pointer' onClick={() => handlePowerAction('start')}>
                  <Play height={16} className='text-hyper-accent mr-1 ml-[-8px]'/>
                  <span>START</span>
                </div>
              )}
              {(status === 'Running' || status === 'Starting') && (
                <div className='bg-hyper-text-primary text-hyper-accent rounded-lg font-semibold text-xs w-[80px] h-[25px] flex justify-center items-center button-primary cursor-pointer' onClick={() => handlePowerAction('stop')}>
                  <CircleX height={16} className='text-hyper-accent mr-1 ml-[-8px]'/>
                  <span>STOP</span>
                </div>
              )}
              {status === 'Running' && (
                <div className='button-primary bg-hyper-glass rounded-lg font-semibold text-xs w-[80px] h-[25px] flex justify-center items-center'>
                  <Users height={16} className='text-hyper-accent mr-1 ml-[-8px]'/>
                  <span>{player}</span>
                </div>
              )}
              {(status === 'Running' || status === 'Offline') && (
                <div className='bg-hyper-text-primary text-hyper-accent rounded-lg font-semibold text-xs w-[80px] h-[25px] flex justify-center items-center button-primary cursor-pointer' onClick={() => handlePowerAction('restart')}>
                  <RotateCcw height={16} className='text-hyper-accent mr-1 ml-[-8px]'/>
                  <span>RESET</span>
                </div>
              )}
              {(status === 'Stopping' || status === 'Starting') && (
                <div className='bg-hyper-text-primary text-hyper-accent rounded-lg font-semibold text-xs w-[80px] h-[25px] flex justify-center items-center button-primary cursor-pointer' onClick={() => handlePowerAction('kill')}>
                  <Skull height={16} className='text-hyper-accent mr-1 ml-[-8px]'/>
                  <span>KILL</span>
                </div>
              )}
            </div>
            {/* Manage Server - Only show if not hidden */}
            {!hideManageButton && (
              <a
                href={`/server/${uuid}`}
                className="button-primary bg-hyper-primary text-hyper-primary w-full h-[30px] rounded-lg justify-center items-center flex"
                style={{ textDecoration: 'none' }}
              >
                <span className="font-semibold text-xs">MANAGE SERVER</span>
              </a>
            )}
          </div>
        </>
      )}
    </div>
  );
}