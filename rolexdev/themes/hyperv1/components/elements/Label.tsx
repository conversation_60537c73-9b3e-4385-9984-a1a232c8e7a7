import React from 'react';

interface LabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
    isLight?: boolean;
}

const Label: React.FC<LabelProps> = ({ isLight, className, ...props }) => {
    const baseClasses = 'block text-sm mb-1 sm:mb-2 font-medium';
    const colorClasses = isLight ? 'text-hyper-foreground' : 'text-hyper-muted-foreground';
    const combinedClasses = `${baseClasses} ${colorClasses} ${className || ''}`.trim();

    return <label className={combinedClasses} {...props} />;
};

export default Label;
