import React, { useState, useRef, useEffect } from 'react';
import { ArrowUpDown, ChevronDown, SortAsc, SortDesc } from 'lucide-react';
import { createPortal } from 'react-dom';

export type SortOption = 'name' | 'size' | 'modified' | 'type';
export type SortDirection = 'asc' | 'desc';

interface FileSorterProps {
    sortBy: SortOption;
    sortDirection: SortDirection;
    onSortChange: (sortBy: SortOption, direction: SortDirection) => void;
}

const sortOptions = [
    { value: 'name' as const, label: 'Name' },
    { value: 'size' as const, label: 'Size' },
    { value: 'modified' as const, label: 'Modified' },
    { value: 'type' as const, label: 'Type' },
];

const FileSorter: React.FC<FileSorterProps> = ({ sortBy, sortDirection, onSortChange }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [dropdownPosition, setDropdownPosition] = useState<{ top: number; left: number } | null>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const buttonRef = useRef<HTMLButtonElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (buttonRef.current && !buttonRef.current.contains(event.target as Node)) {
                // Check if click is outside the dropdown portal too
                const dropdownElement = document.querySelector('[data-file-sorter-dropdown="true"]');
                if (!dropdownElement || !dropdownElement.contains(event.target as Node)) {
                    setIsOpen(false);
                }
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
            return () => document.removeEventListener('mousedown', handleClickOutside);
        }
        
        return undefined;
    }, [isOpen]);

    // Update dropdown position when it opens
    useEffect(() => {
        if (!isOpen || !buttonRef.current) {
            setDropdownPosition(null);
            return;
        }

        const updatePosition = () => {
            if (!buttonRef.current) return;
            
            const rect = buttonRef.current.getBoundingClientRect();
            const dropdownWidth = 192; // w-48 = 12rem = 192px
            const dropdownHeight = 200; // estimated max height
            const padding = 8;
            
            // Calculate position - align to right edge of button
            let top = rect.bottom + 8; // mt-2 = 8px
            let left = rect.right - dropdownWidth;
            
            // Adjust horizontal position if it goes off screen
            if (left < padding) {
                left = rect.left;
            }
            if (left + dropdownWidth > window.innerWidth - padding) {
                left = window.innerWidth - dropdownWidth - padding;
            }
            
            // Adjust vertical position if it goes off screen
            if (top + dropdownHeight > window.innerHeight - padding) {
                top = rect.top - dropdownHeight - 8;
                if (top < padding) {
                    top = padding;
                }
            }
            
            setDropdownPosition({ top, left });
        };

        updatePosition();

        const handleScroll = () => {
            if (isOpen) {
                updatePosition();
            }
        };

        window.addEventListener('scroll', handleScroll, true);
        window.addEventListener('resize', handleScroll);

        return () => {
            window.removeEventListener('scroll', handleScroll, true);
            window.removeEventListener('resize', handleScroll);
        };
    }, [isOpen]);

    const handleSortOptionClick = (option: SortOption) => {
        // Always set ascending when selecting a new sort option
        onSortChange(option, 'asc');
        setIsOpen(false);
    };

    const handleDirectionChange = (direction: SortDirection) => {
        onSortChange(sortBy, direction);
    };

    const currentSortLabel = sortOptions.find(opt => opt.value === sortBy)?.label || 'Name';

    return (
        <div className="flex items-center gap-2 w-full justify-between">
            {/* Sort Option Dropdown */}
            <div className="relative" ref={dropdownRef}>
                <button
                    ref={buttonRef}
                    onClick={() => setIsOpen(!isOpen)}
                    className="flex items-center gap-2 px-3 py-2.5 bg-hyper-background border border-hyper-accent rounded-lg text-hyper-primary hover:bg-hyper-primary-10 transition-all duration-200 min-w-[120px]"
                >
                    <ArrowUpDown size={16} />
                    <span className="text-sm font-medium">{currentSortLabel}</span>
                    <ChevronDown size={14} className={`transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
                </button>

                {/* Render dropdown in portal */}
                {isOpen && dropdownPosition && createPortal(
                    <div 
                        data-file-sorter-dropdown="true"
                        className="fixed bg-hyper-sidebar backdrop-blur-lg border border-hyper-accent rounded-lg shadow-lg z-[99999] py-2 w-48"
                        style={{
                            top: `${dropdownPosition.top}px`,
                            left: `${dropdownPosition.left}px`,
                        }}
                    >
                        {sortOptions.map((option) => (
                            <button
                                key={option.value}
                                onClick={() => handleSortOptionClick(option.value)}
                                className={`w-full px-4 py-2.5 text-left text-sm hover:bg-hyper-primary-10 transition-colors duration-200 flex items-center justify-between ${
                                    sortBy === option.value 
                                        ? 'text-hyper-primary bg-hyper-primary-50' 
                                        : 'text-hyper-primary'
                                }`}
                            >
                                <span>{option.label}</span>
                                {sortBy === option.value && (
                                    <ArrowUpDown size={14} className="text-hyper-primary" />
                                )}
                            </button>
                        ))}
                    </div>,
                    document.body
                )}
            </div>

            {/* Direction Buttons */}
            <div className="flex items-center border border-hyper-primary rounded-lg overflow-hidden">
                <button
                    onClick={() => handleDirectionChange('asc')}
                    className={`p-2.5 transition-all duration-200 ${
                        sortDirection === 'asc'
                            ? 'bg-hyper-primary text-white'
                            : 'bg-hyper-background text-hyper-primary hover:bg-hyper-primary-10'
                    }`}
                    title="Sort Ascending"
                >
                    <SortAsc size={16} />
                </button>
                <button
                    onClick={() => handleDirectionChange('desc')}
                    className={`p-2.5 border-l border-hyper-primary transition-all duration-200 ${
                        sortDirection === 'desc'
                            ? 'bg-hyper-primary text-white'
                            : 'bg-hyper-background text-hyper-primary hover:bg-hyper-primary-10'
                    }`}
                    title="Sort Descending"
                >
                    <SortDesc size={16} />
                </button>
            </div>
        </div>
    );
};

export default FileSorter;