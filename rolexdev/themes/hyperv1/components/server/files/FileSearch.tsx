import React from 'react';
import { Search, X } from 'lucide-react';

interface FileSearchProps {
    searchTerm: string;
    onSearchChange: (value: string) => void;
    placeholder?: string;
}

const FileSearch: React.FC<FileSearchProps> = ({ 
    searchTerm, 
    onSearchChange, 
    placeholder = "Search files and folders..." 
}) => {
    const handleClear = () => {
        onSearchChange('');
    };

    return (
        <div className="relative flex-1 min-w-max md:max-w-md w-full">
            <div className="relative">
                <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-hyper-muted-foreground" />
                <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => onSearchChange(e.target.value)}
                    placeholder={placeholder}
                    className="w-full pl-10 pr-10 py-2.5 bg-hyper-background border border-hyper-primary rounded-lg text-hyper-primary placeholder-hyper-muted-foreground hyper-input transition-all duration-200"
                />
                {searchTerm && (
                    <button
                        onClick={handleClear}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-hyper-muted-foreground hover:text-hyper-accent transition-colors duration-200"
                    >
                        <X size={16} />
                    </button>
                )}
            </div>
        </div>
    );
};

export default FileSearch;