import React, { useEffect, useState } from 'react';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { NavLink, useLocation } from 'react-router-dom';
import { encodePathSegments, hashToPath } from '@/helpers';
import { ChevronRight, Home, Folder } from 'lucide-react';

interface Props {
    renderLeft?: JSX.Element;
    withinFileEditor?: boolean;
    isNewFile?: boolean;
}

export default ({ renderLeft, withinFileEditor, isNewFile }: Props) => {
    const [file, setFile] = useState<string | null>(null);
    const id = ServerContext.useStoreState((state) => state.server.data!.id);
    const directory = ServerContext.useStoreState((state) => state.files.directory);
    const { hash } = useLocation();

    useEffect(() => {
        const path = hashToPath(hash);

        if (withinFileEditor && !isNewFile) {
            const name = path.split('/').pop() || null;
            setFile(name);
        }
    }, [withinFileEditor, isNewFile, hash]);

    const breadcrumbs = (): { name: string; path?: string }[] =>
        directory
            .split('/')
            .filter((directory) => !!directory)
            .map((directory, index, dirs) => {
                if (!withinFileEditor && index === dirs.length - 1) {
                    return { name: directory };
                }

                return { name: directory, path: `/${dirs.slice(0, index + 1).join('/')}` };
            });

    return (
        <div className="flex items-center gap-2 text-sm bg-hyper-background border border-hyper-primary rounded-lg px-3 py-2 flex-1 min-w-0">
            {/* Container Root */}
            <NavLink 
                to={`/server/${id}/files`} 
                className="flex items-center gap-2 text-hyper-accent flex-shrink-0"
            >
                <Home size={16} />
                <span className="text-hyper-secondary">home</span>
            </NavLink>
            
            {/* Breadcrumb Trail */}
            {breadcrumbs().map((crumb, index) => (
                <React.Fragment key={index}>
                    <ChevronRight size={14} className="text-hyper-accent flex-shrink-0" />
                    {crumb.path ? (
                        <NavLink
                            to={`/server/${id}/files#${encodePathSegments(crumb.path)}`}
                            className="text-hyper-primary hover:text-hyper-accent transition-colors no-underline flex items-center gap-1 truncate"
                        >
                            <Folder size={14} />
                            <span className="truncate">{crumb.name}</span>
                        </NavLink>
                    ) : (
                        <div className="flex items-center gap-1 text-hyper-secondary truncate">
                            <Folder size={14} />
                            <span className="truncate">{crumb.name}</span>
                        </div>
                    )}
                </React.Fragment>
            ))}
            
            {/* Current File */}
            {file && (
                <>
                    <ChevronRight size={14} className="text-hyper-accent flex-shrink-0" />
                    <div className="flex items-center gap-1 text-hyper-secondary truncate">
                        <span className="truncate">{file}</span>
                    </div>
                </>
            )}
        </div>
    );
};
