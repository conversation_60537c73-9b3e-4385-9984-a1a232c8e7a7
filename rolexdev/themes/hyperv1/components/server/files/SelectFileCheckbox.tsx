import React from 'react';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { StandaloneCheckbox } from '@rolexdev/themes/hyperv1/components/elements/Checkbox';

export const FileActionCheckbox = ({ type, checked, onChange, className }: {
    type: string;
    checked: boolean;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    className?: string;
}) => {
    return (
        <StandaloneCheckbox
            checked={checked}
            onChange={onChange}
            className={className}
        />
    );
};

export default ({ name }: { name: string }) => {
    const isChecked = ServerContext.useStoreState((state) => state.files.selectedFiles.indexOf(name) >= 0);
    const appendSelectedFile = ServerContext.useStoreActions((actions) => actions.files.appendSelectedFile);
    const removeSelectedFile = ServerContext.useStoreActions((actions) => actions.files.removeSelectedFile);

    return (
        <div className="flex-shrink-0 flex items-center justify-center">
            <StandaloneCheckbox
                checked={isChecked}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    if (e.currentTarget.checked) {
                        appendSelectedFile(name);
                    } else {
                        removeSelectedFile(name);
                    }
                }}
            />
        </div>
    );
};
