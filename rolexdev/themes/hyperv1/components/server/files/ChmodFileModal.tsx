import { fileBitsToString } from '@/helpers';
import useFileManagerSwr from '@rolexdev/themes/hyperv1/plugins/useFileManagerSwr';
import React from 'react';
import { Form, Formik, FormikHelpers } from 'formik';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import chmodFiles from '@/api/server/files/chmodFiles';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import useFlash from '@/plugins/useFlash';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import FlashMessageRender from '@/components/FlashMessageRender';
import { Key, File, Lock, Unlock } from 'lucide-react';
import { RequiredModalProps } from '@/components/elements/Modal';

interface FormikValues {
    mode: string;
}

interface File {
    file: string;
    mode: string;
}

type OwnProps = RequiredModalProps & { files: File[] };

const ChmodFileModal = ({ files, visible, onDismissed }: OwnProps) => {
    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const { mutate } = useFileManagerSwr();
    const { clearFlashes, clearAndAddHttpError } = useFlash();
    const directory = ServerContext.useStoreState((state) => state.files.directory);
    const setSelectedFiles = ServerContext.useStoreActions((actions) => actions.files.setSelectedFiles);

    const submit = ({ mode }: FormikValues, { setSubmitting }: FormikHelpers<FormikValues>) => {
        clearFlashes('files');

        mutate(
            (data) =>
                data.map((f) =>
                    f.name === files[0].file ? { ...f, mode: fileBitsToString(mode, !f.isFile), modeBits: mode } : f
                ),
            false
        );

        const data = files.map((f) => ({ file: f.file, mode: mode }));

        chmodFiles(uuid, directory, data)
            .then((): Promise<any> => (files.length > 0 ? mutate() : Promise.resolve()))
            .then(() => setSelectedFiles([]))
            .catch((error) => {
                mutate();
                setSubmitting(false);
                clearAndAddHttpError({ key: 'files', error });
            })
            .then(() => onDismissed());
    };

    const getTitle = () => {
        return files.length === 1 ? `Permissions for "${files[0].file}"` : `Change Permissions for ${files.length} Files`;
    };

    const getPermissionText = (mode: string) => {
        if (!mode || mode.length !== 4) return 'Invalid permissions';
        
        const owner = parseInt(mode[1]);
        const group = parseInt(mode[2]);
        const other = parseInt(mode[3]);
        
        const convertToText = (perm: number) => {
            let text = '';
            text += (perm & 4) ? 'r' : '-';
            text += (perm & 2) ? 'w' : '-';
            text += (perm & 1) ? 'x' : '-';
            return text;
        };
        
        return `${convertToText(owner)}${convertToText(group)}${convertToText(other)}`;
    };

    return (
        <Dialog
            open={visible}
            onClose={onDismissed}
            title={getTitle()}
            description="Change file or directory permissions"
        >
            <Formik onSubmit={submit} initialValues={{ mode: files.length > 1 ? '' : files[0].mode || '' }}>
                {({ isSubmitting, values }) => (
                    <Form className="space-y-6">
                        <FlashMessageRender byKey={'files'} className="mb-4" />
                        
                        {/* Files List */}
                        {files.length > 1 ? (
                            <div className="bg-hyper-background border border-hyper-primary rounded-lg p-3">
                                <h4 className="text-sm font-medium text-hyper-foreground mb-2">Selected Files</h4>
                                <div className="max-h-32 overflow-y-auto space-y-1">
                                    {files.map((file) => (
                                        <div key={file.file} className="flex items-center gap-2 text-sm text-hyper-accent">
                                            <File size={14} />
                                            <span className="font-mono break-all">{file.file}</span>
                                            <span className="text-xs text-hyper-secondary ml-auto">({file.mode})</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ) : (
                            <div className="bg-hyper-background border border-hyper-primary rounded-lg p-4">
                                <div className="flex items-center gap-3">
                                    <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                                        <File size={20} className="text-hyper-accent" />
                                    </div>
                                    <div className="flex-1">
                                        <h4 className="text-lg font-semibold text-hyper-primary">{files[0].file}</h4>
                                        <p className="text-sm text-hyper-accent">Current permissions: {files[0].mode}</p>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Permission Input */}
                        <div className="space-y-4">
                            <Field
                                type={'string'}
                                id={'file_mode'}
                                name={'mode'}
                                label={'File Mode'}
                                placeholder="0755"
                                description="Enter the numeric permissions (e.g., 0755 for rwxr-xr-x)"
                                autoFocus
                            />
                            
                            {/* Permission Preview */}
                            {values.mode && (
                                <div className="bg-hyper-background border border-hyper-primary rounded-lg p-3">
                                    <div className="flex items-center gap-2 mb-2">
                                        <Key size={14} className="text-hyper-accent" />
                                        <span className="text-sm font-medium text-hyper-foreground">Permission Preview</span>
                                    </div>
                                    <div className="font-mono text-sm text-hyper-accent">
                                        {getPermissionText(values.mode)}
                                    </div>
                                    <div className="text-xs text-hyper-secondary mt-1">
                                        Owner: {getPermissionText(values.mode).slice(0, 3)} | 
                                        Group: {getPermissionText(values.mode).slice(3, 6)} | 
                                        Others: {getPermissionText(values.mode).slice(6, 9)}
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Info Box */}
                        <div className="bg-hyper-glass border border-hyper-accent rounded-lg p-4">
                            <div className="flex items-start gap-3">
                                <Lock size={16} className="text-hyper-accent flex-shrink-0 mt-0.5" />
                                <div className="text-sm text-hyper-accent">
                                    <p className="font-medium mb-1">Permission Format</p>
                                    <ul className="space-y-1 text-xs">
                                        <li>• 4-digit octal format (e.g., 0755, 0644)</li>
                                        <li>• First digit: special permissions</li>
                                        <li>• Second digit: owner permissions (rwx)</li>
                                        <li>• Third digit: group permissions (rwx)</li>
                                        <li>• Fourth digit: other permissions (rwx)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <Dialog.Footer>
                            <Button 
                                size="small" 
                                isSecondary 
                                onClick={onDismissed}
                                disabled={isSubmitting}
                            >
                                Cancel
                            </Button>
                            <Button 
                                size="small"
                                type="submit"
                                disabled={isSubmitting}
                                isLoading={isSubmitting}
                                className="flex items-center gap-2"
                            >
                                <Unlock size={16} />
                                Update Permissions
                            </Button>
                        </Dialog.Footer>
                    </Form>
                )}
            </Formik>
        </Dialog>
    );
};

export default ChmodFileModal;
