import React, { useContext, useEffect, useState } from 'react';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { Form, Formik, FormikHelpers } from 'formik';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import { join } from 'path';
import { object, string } from 'yup';
import createDirectory from '@/api/server/files/createDirectory';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { FileObject } from '@/api/server/files/loadDirectory';
import { useFlashKey } from '@/plugins/useFlash';
import useFileManagerSwr from '@rolexdev/themes/hyperv1/plugins/useFileManagerSwr';
import { WithClassname } from '@/components/types';
import FlashMessageRender from '@/components/FlashMessageRender';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import { <PERSON>old<PERSON><PERSON><PERSON>, Folder } from 'lucide-react';
import Code from '@rolexdev/themes/hyperv1/components/elements/Code';

interface Values {
    directoryName: string;
}

const schema = object().shape({
    directoryName: string().required('A valid directory name must be provided.'),
});

const generateDirectoryData = (name: string): FileObject => ({
    key: `dir_${name.split('/', 1)[0] ?? name}`,
    name: name.replace(/^(\/*)/, '').split('/', 1)[0] ?? name,
    mode: 'drwxr-xr-x',
    modeBits: '0755',
    size: 0,
    isFile: false,
    isSymlink: false,
    mimetype: '',
    createdAt: new Date(),
    modifiedAt: new Date(),
    isArchiveType: () => false,
    isEditable: () => false,
});

const NewDirectoryDialog = ({ open, onClose }: { open: boolean; onClose: () => void }) => {
    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const directory = ServerContext.useStoreState((state) => state.files.directory);

    const { mutate } = useFileManagerSwr();
    const { clearAndAddHttpError } = useFlashKey('files:directory-modal');

    useEffect(() => {
        return () => {
            clearAndAddHttpError();
        };
    }, []);

    const submit = ({ directoryName }: Values, { setSubmitting }: FormikHelpers<Values>) => {
        createDirectory(uuid, directory, directoryName)
            .then(() => mutate((data) => [...data, generateDirectoryData(directoryName)], false))
            .then(() => onClose())
            .catch((error) => {
                setSubmitting(false);
                clearAndAddHttpError(error);
            });
    };

    return (
        <Dialog
            open={open}
            onClose={onClose}
            title="Create Directory"
            description="Create a new directory in the current location"
        >
            <Formik onSubmit={submit} validationSchema={schema} initialValues={{ directoryName: '' }}>
                {({ submitForm, values, isSubmitting }) => (
                    <Form className="space-y-6">
                        <FlashMessageRender byKey={'files:directory-modal'} className="mb-4" />
                        
                        <div className="space-y-4">
                            <Field 
                                autoFocus 
                                id={'directoryName'} 
                                name={'directoryName'} 
                                label={'Directory Name'} 
                                placeholder="Enter directory name..."
                                className="appearance-none outline-none w-full min-w-0 p-3 rounded-md text-sm transition-all duration-150 resize-none shadow-none h-10 bg-hyper-background text-hyper-foreground border focus:border-2 border-hyper-primary hover:border-hyper-accent focus:outline-none disabled:bg-neutral-100 font-medium hyper-input font-mono"
                            />
                            
                            <div className="bg-hyper-background border border-hyper-primary rounded-lg p-3">
                                <p className="text-sm text-hyper-accent">
                                    <span className="text-hyper-secondary">This directory will be created as </span>
                                    <Code className="text-hyper-accent">
                                        /home/<USER>/
                                        <span className="text-hyper-primary">
                                            {join(directory, values.directoryName).replace(/^(\.\.\/|\/)+/, '')}
                                        </span>
                                    </Code>
                                </p>
                            </div>
                        </div>

                        <Dialog.Footer>
                            <Button 
                                isSecondary
                                size="small"
                                onClick={onClose}
                                disabled={isSubmitting}
                            >
                                Cancel
                            </Button>
                            <Button 
                                size="small"
                                onClick={submitForm}
                                disabled={isSubmitting}
                                isLoading={isSubmitting}
                                className="flex items-center gap-2"
                            >
                                <FolderPlus size={16} />
                                Create Directory
                            </Button>
                        </Dialog.Footer>
                    </Form>
                )}
            </Formik>
        </Dialog>
    );
};

export default ({ className }: WithClassname) => {
    const [open, setOpen] = useState(false);

    return (
        <>
            <NewDirectoryDialog open={open} onClose={() => setOpen(false)} />
            <Button 
                isSecondary
                size="small"
                onClick={() => setOpen(true)} 
                className={`flex items-center gap-2 w-full ${className || ''}`}
                style={{ minWidth: 'max-content' }}
            >
                <FolderPlus size={16} />
                New Folder
            </Button>
        </>
    );
};
