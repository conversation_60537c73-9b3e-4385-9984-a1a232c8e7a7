import React, { useEffect, useState } from 'react';
import getFileContents from '@/api/server/files/getFileContents';
import { httpErrorToHuman } from '@/api/http';
import SpinnerOverlay from '@/components/elements/SpinnerOverlay';
import saveFileContents from '@/api/server/files/saveFileContents';
import FileManagerBreadcrumbs from '@rolexdev/themes/hyperv1/components/server/files/FileManagerBreadcrumbs';
import { useHistory, useLocation, useParams } from 'react-router';
import FileNameModal from '@rolexdev/themes/hyperv1/components/server/files/FileNameModal';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import FlashMessageRender from '@/components/FlashMessageRender';
import PageContentBlock from '@rolexdev/themes/hyperv1/components/elements/PageContentBlock';
import { ServerError } from '@/components/elements/ScreenBlock';
import tw from 'twin.macro';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import modes from '@/modes';
import useFlash from '@/plugins/useFlash';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import ErrorBoundary from '@/components/elements/ErrorBoundary';
import { encodePathSegments, hashToPath } from '@/helpers';
import { dirname } from 'path';
import CodemirrorEditor from '@/components/elements/CodemirrorEditor';
import { FileText, Settings, Save, Plus, ChevronDown } from 'lucide-react';

// Import hyperv1 CodeMirror theme
import '@rolexdev/themes/hyperv1/assets/css/codemirror-hyperv1.css';

export default () => {
    const [error, setError] = useState('');
    const { action } = useParams<{ action: 'new' | string }>();
    const [loading, setLoading] = useState(action === 'edit');
    const [content, setContent] = useState('');
    const [modalVisible, setModalVisible] = useState(false);
    const [mode, setMode] = useState('text/plain');
    const [languageSelectorOpen, setLanguageSelectorOpen] = useState(false);

    const history = useHistory();
    const { hash } = useLocation();

    const id = ServerContext.useStoreState((state) => state.server.data!.id);
    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const setDirectory = ServerContext.useStoreActions((actions) => actions.files.setDirectory);
    const { addError, clearFlashes } = useFlash();

    let fetchFileContent: null | (() => Promise<string>) = null;

    useEffect(() => {
        if (action === 'new') return;

        setError('');
        setLoading(true);
        const path = hashToPath(hash);
        setDirectory(dirname(path));
        getFileContents(uuid, path)
            .then(setContent)
            .catch((error) => {
                console.error(error);
                setError(httpErrorToHuman(error));
            })
            .then(() => setLoading(false));
    }, [action, uuid, hash]);

    const save = (name?: string) => {
        if (!fetchFileContent) {
            return;
        }

        setLoading(true);
        clearFlashes('files:view');
        fetchFileContent()
            .then((content) => saveFileContents(uuid, name || hashToPath(hash), content))
            .then(() => {
                if (name) {
                    history.push(`/server/${id}/files/edit#/${encodePathSegments(name)}`);
                    return;
                }

                return Promise.resolve();
            })
            .catch((error) => {
                console.error(error);
                addError({ message: httpErrorToHuman(error), key: 'files:view' });
            })
            .then(() => setLoading(false));
    };

    if (error) {
        return <ServerError message={error} onBack={() => history.goBack()} />;
    }

    return (
        <PageContentBlock>
            <FlashMessageRender byKey={'files:view'} css={tw`mb-4`} />
            <ErrorBoundary>
                <div css={tw`mb-4`}>
                    <FileManagerBreadcrumbs withinFileEditor isNewFile={action !== 'edit'} />
                </div>
            </ErrorBoundary>
            {hash.replace(/^#/, '').endsWith('.pteroignore') && (
                <div className="mb-4 p-4 border-l-4 bg-hyper-sidebar backdrop-blur-lg rounded-lg border-yellow-400">
                    <div className="flex items-start gap-3">
                        <FileText size={20} className="text-yellow-400 flex-shrink-0 mt-0.5" />
                        <div>
                            <p className="text-yellow-400 font-medium mb-2">Editing .pteroignore file</p>
                            <p className="text-hyper-secondary text-sm leading-relaxed">
                                Any files or directories listed in this file will be excluded from backups. 
                                You can use wildcards with asterisk (<code className="font-mono bg-hyper-background rounded px-1 py-0.5 text-hyper-accent">*</code>) 
                                and negate rules with exclamation point (<code className="font-mono bg-hyper-background rounded px-1 py-0.5 text-hyper-accent">!</code>).
                            </p>
                        </div>
                    </div>
                </div>
            )}
            <FileNameModal
                visible={modalVisible}
                onDismissed={() => setModalVisible(false)}
                onFileNamed={(name) => {
                    setModalVisible(false);
                    save(name);
                }}
            />
            
            {/* Editor Container */}
            <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-xl overflow-hidden hyperv1-editor-container">
                <div css={tw`relative`}>
                    <SpinnerOverlay visible={loading} />
                    <CodemirrorEditor
                        mode={mode}
                        filename={hash.replace(/^#/, '')}
                        onModeChanged={setMode}
                        initialContent={content}
                        fetchContent={(value) => {
                            fetchFileContent = value;
                        }}
                        onContentSaved={() => {
                            if (action !== 'edit') {
                                setModalVisible(true);
                            } else {
                                save();
                            }
                        }}
                    />
                </div>
            </div>
            
            {/* Editor Controls */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mt-4">
                {/* Language Selector */}
                <div className="flex items-center gap-3">
                    <Settings size={18} className="text-hyper-accent" />
                    <div className="relative" tabIndex={0} onBlur={() => setTimeout(() => setLanguageSelectorOpen(false), 100)}>
                        <button
                            className="flex items-center justify-between px-3 py-1 rounded-lg bg-hyper-background border border-hyper-accent text-hyper-primary w-[180px] hover:bg-hyper-primary-10 transition"
                            onClick={() => setLanguageSelectorOpen(v => !v)}
                            aria-haspopup="listbox"
                            aria-expanded={languageSelectorOpen}
                            type="button"
                        >
                            <span className="truncate mr-2 text-sm font-medium">
                                {modes.find(m => m.mime === mode)?.name || 'Plain Text'}
                            </span>
                            <ChevronDown 
                                size={16} 
                                className={`text-hyper-accent transition-transform duration-200 ${
                                    languageSelectorOpen ? 'rotate-180' : ''
                                }`} 
                            />
                        </button>
                        {languageSelectorOpen && (
                            <div className="z-[9999] absolute left-0 bottom-full mb-2 w-[180px] bg-hyper-sidebar backdrop-blur-lg border border-hyper-accent rounded-lg shadow-lg max-h-60 overflow-y-auto">
                                {modes.map((modeOption) => (
                                    <div
                                        key={`${modeOption.name}_${modeOption.mime}`}
                                        className={`px-3 py-2 cursor-pointer bg-hyper-sidebar backdrop-blur-lg hover:bg-hyper-primary-10 truncate text-hyper-primary text-sm ${
                                            mode === modeOption.mime ? 'bg-hyper-primary-30 font-bold' : ''
                                        }`}
                                        onMouseDown={e => {
                                            e.preventDefault();
                                            setMode(modeOption.mime);
                                            setLanguageSelectorOpen(false);
                                        }}
                                    >
                                        <span className="font-medium">{modeOption.name}</span>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3">
                    {action === 'edit' ? (
                        <Can action={'file.update'}>
                            <Button 
                                size="small"
                                onClick={() => save()}
                                className="flex items-center gap-2"
                            >
                                <Save size={16} />
                                Save Content
                            </Button>
                        </Can>
                    ) : (
                        <Can action={'file.create'}>
                            <Button 
                                size="small"
                                onClick={() => setModalVisible(true)}
                                className="flex items-center gap-2"
                            >
                                <Plus size={16} />
                                Create File
                            </Button>
                        </Can>
                    )}
                </div>
            </div>
        </PageContentBlock>
    );
};
