import React from 'react';
import { Form, Formik, FormikHelpers } from 'formik';
import { object, string } from 'yup';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { join } from 'path';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import { FilePlus, File } from 'lucide-react';
import { RequiredModalProps } from '@/components/elements/Modal';

type Props = RequiredModalProps & {
    onFileNamed: (name: string) => void;
};

interface Values {
    fileName: string;
}

export default ({ onFileNamed, onDismissed, visible }: Props) => {
    const directory = ServerContext.useStoreState((state) => state.files.directory);

    const submit = (values: Values, { setSubmitting }: FormikHelpers<Values>) => {
        onFileNamed(join(directory, values.fileName));
        setSubmitting(false);
        onDismissed();
    };

    return (
        <Dialog
            open={visible}
            onClose={onDismissed}
            title="Create New File"
            description="Enter the name for your new file"
        >
            <Formik
                onSubmit={submit}
                initialValues={{ fileName: '' }}
                validationSchema={object().shape({
                    fileName: string().required().min(1),
                })}
            >
                {({ isSubmitting, values }) => (
                    <Form className="space-y-6">
                        <div className="space-y-4">
                            <Field
                                id={'fileName'}
                                name={'fileName'}
                                label={'File Name'}
                                placeholder={'example.txt'}
                                description={'Enter the name that this file should be saved as.'}
                                autoFocus
                            />
                            
                            {/* File Preview */}
                            {values.fileName && (
                                <div className="bg-hyper-background border border-hyper-primary rounded-lg p-3">
                                    <div className="flex items-center gap-2 mb-2">
                                        <File size={14} className="text-hyper-accent" />
                                        <span className="text-sm font-medium text-hyper-foreground">File Path Preview</span>
                                    </div>
                                    <code className="text-xs text-hyper-accent break-all">
                                        /home/<USER>/{join(directory, values.fileName).replace(/^(\.\.\/|\/)+/, '')}
                                    </code>
                                </div>
                            )}
                        </div>

                        <Dialog.Footer>
                            <Button 
                                size="small" 
                                isSecondary 
                                onClick={onDismissed}
                                disabled={isSubmitting}
                            >
                                Cancel
                            </Button>
                            <Button 
                                size="small"
                                type="submit"
                                disabled={isSubmitting}
                                isLoading={isSubmitting}
                                className="flex items-center gap-2"
                            >
                                <FilePlus size={16} />
                                Create File
                            </Button>
                        </Dialog.Footer>
                    </Form>
                )}
            </Formik>
        </Dialog>
    );
};
