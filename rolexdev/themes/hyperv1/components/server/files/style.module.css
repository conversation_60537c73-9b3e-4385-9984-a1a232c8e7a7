/* File Manager Styles - Updated for HyperV1 Theme */

.manager_actions {
    @apply flex items-center gap-3 flex-wrap;
    
    /* Responsive stacking on mobile */
    @screen sm {
        @apply flex-nowrap;
    }
}

/* Legacy styles - kept for backward compatibility but unused in redesigned components */
.file_row {
    @apply bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-4 transition-all duration-200;
    @apply hover:border-hyper-accent;

    & > .details {
        @apply flex flex-1 items-center text-hyper-primary no-underline overflow-hidden;
        @apply hover:text-hyper-accent transition-colors;

        &:not(a) {
            @apply cursor-default;
        }
    }
}

/* File manager specific animations */
.fade-enter {
    @apply opacity-0;
}

.fade-enter-active {
    @apply opacity-100 transition-opacity duration-150;
}

.fade-exit {
    @apply opacity-100;
}

.fade-exit-active {
    @apply opacity-0 transition-opacity duration-150;
}

/* File type indicators */
.file-type-file {
    @apply text-blue-400;
}

.file-type-directory {
    @apply text-yellow-400;
}

.file-type-archive {
    @apply text-green-400;
}

.file-type-symlink {
    @apply text-purple-400;
}

/* Upload progress indicators */
.upload-progress {
    @apply bg-hyper-background border border-hyper-primary rounded-lg;
}

.upload-progress-bar {
    @apply bg-hyper-accent rounded-full transition-all duration-300;
}

/* Selection states */
.file-selected {
    @apply border-hyper-accent bg-hyper-primary-10;
}

/* Hover states for interactive elements */
.file-action-button {
    @apply w-8 h-8 flex items-center justify-center rounded-lg transition-colors;
    @apply text-hyper-accent hover:bg-hyper-primary-10 hover:text-hyper-primary;
}

/* Dropdown menu styling */
.dropdown-menu {
    @apply bg-hyper-sidebar border border-hyper-primary rounded-lg shadow-lg backdrop-blur-lg;
    @apply min-w-[180px] z-50;
}

.dropdown-item {
    @apply px-3 py-2 cursor-pointer transition-colors flex items-center;
    @apply text-hyper-primary hover:bg-hyper-primary-10;
}

.dropdown-item-danger {
    @apply text-red-400 hover:bg-red-400/10 hover:text-red-400;
}

/* Breadcrumb styling */
.breadcrumb {
    @apply bg-hyper-background border border-hyper-primary rounded-lg px-3 py-2;
    @apply flex items-center gap-2 text-sm flex-1 min-w-0;
}

.breadcrumb-item {
    @apply text-hyper-primary hover:text-hyper-accent transition-colors no-underline;
    @apply flex items-center gap-1 truncate;
}

.breadcrumb-separator {
    @apply text-hyper-accent flex-shrink-0;
}

/* Mass action bar */
.mass-actions {
    @apply bg-hyper-sidebar border border-hyper-primary rounded-lg p-4 backdrop-blur-lg shadow-lg;
    @apply flex items-center gap-3;
}

/* Modal styling overrides */
.file-modal {
    @apply bg-hyper-sidebar border border-hyper-primary rounded-lg backdrop-blur-lg;
}

.file-modal-header {
    @apply border-b border-hyper-primary;
}

.file-modal-footer {
    @apply border-t border-hyper-primary;
}

/* Empty state styling */
.empty-directory {
    @apply bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-8 text-center;
    @apply w-full max-w-md mx-auto;
}

.empty-directory-icon {
    @apply w-16 h-16 rounded-lg bg-hyper-primary-10 flex items-center justify-center mx-auto mb-4;
}

/* File list spacing */
.file-list {
    @apply space-y-2;
}

/* Status indicators */
.status-uploading {
    @apply text-blue-400 bg-blue-400/10 border-blue-400/20;
}

.status-success {
    @apply text-green-400 bg-green-400/10 border-green-400/20;
}

.status-error {
    @apply text-red-400 bg-red-400/10 border-red-400/20;
}

.status-warning {
    @apply text-yellow-400 bg-yellow-400/10 border-yellow-400/20;
}
