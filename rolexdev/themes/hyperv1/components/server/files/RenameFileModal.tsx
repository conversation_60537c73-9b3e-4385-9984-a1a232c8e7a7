import React from 'react';
import { Form, Formik, FormikHelpers } from 'formik';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import { join } from 'path';
import renameFiles from '@/api/server/files/renameFiles';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import useFileManagerSwr from '@rolexdev/themes/hyperv1/plugins/useFileManagerSwr';
import useFlash from '@/plugins/useFlash';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import FlashMessageRender from '@/components/FlashMessageRender';
import { Edit, Move, File, Folder } from 'lucide-react';
import { RequiredModalProps } from '@/components/elements/Modal';
import Code from '@rolexdev/themes/hyperv1/components/elements/Code';

interface FormikValues {
    name: string;
}

type OwnProps = RequiredModalProps & { files: string[]; useMoveTerminology?: boolean };

const RenameFileModal = ({ files, useMoveTerminology, visible, onDismissed }: OwnProps) => {
    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const { mutate } = useFileManagerSwr();
    const { clearFlashes, clearAndAddHttpError } = useFlash();
    const directory = ServerContext.useStoreState((state) => state.files.directory);
    const setSelectedFiles = ServerContext.useStoreActions((actions) => actions.files.setSelectedFiles);

    const submit = ({ name }: FormikValues, { setSubmitting }: FormikHelpers<FormikValues>) => {
        clearFlashes('files');

        const len = name.split('/').length;
        if (files.length === 1) {
            if (!useMoveTerminology && len === 1) {
                // Rename the file within this directory.
                mutate((data) => data.map((f) => (f.name === files[0] ? { ...f, name } : f)), false);
            } else if (useMoveTerminology || len > 1) {
                // Remove the file from this directory since they moved it elsewhere.
                mutate((data) => data.filter((f) => f.name !== files[0]), false);
            }
        }

        let data;
        if (useMoveTerminology && files.length > 1) {
            data = files.map((f) => ({ from: f, to: join(name, f) }));
        } else {
            data = files.map((f) => ({ from: f, to: name }));
        }

        renameFiles(uuid, directory, data)
            .then((): Promise<any> => (files.length > 0 ? mutate() : Promise.resolve()))
            .then(() => setSelectedFiles([]))
            .catch((error) => {
                mutate();
                setSubmitting(false);
                clearAndAddHttpError({ key: 'files', error });
            })
            .then(() => onDismissed());
    };

    const getTitle = () => {
        if (useMoveTerminology) {
            return files.length === 1 ? `Move "${files[0]}"` : `Move ${files.length} Files`;
        }
        return files.length === 1 ? `Rename "${files[0]}"` : `Rename ${files.length} Files`;
    };

    const getDescription = () => {
        if (useMoveTerminology) {
            return files.length === 1 
                ? "Change the location and name of this file or folder"
                : "Move multiple files to a new location";
        }
        return files.length === 1 
            ? "Change the name of this file or folder"
            : "Rename multiple files";
    };

    return (
        <Dialog
            open={visible}
            onClose={onDismissed}
            title={getTitle()}
            description={getDescription()}
        >
            <Formik onSubmit={submit} initialValues={{ name: files.length > 1 ? '' : files[0] || '' }}>
                {({ isSubmitting, values }) => (
                    <Form className="space-y-6">
                        <FlashMessageRender byKey={'files'} className="mb-4" />
                        
                        {/* Files List */}
                        {files.length > 1 && (
                            <div className="bg-hyper-background border border-hyper-primary rounded-lg p-3">
                                <h4 className="text-sm font-medium text-hyper-foreground mb-2">Selected Files</h4>
                                <div className="max-h-32 overflow-y-auto space-y-1">
                                    {files.map((file) => (
                                        <div key={file} className="flex items-center gap-2 text-sm text-hyper-accent">
                                            <File size={14} />
                                            <span className="font-mono break-all">{file}</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Name Field */}
                        <div className="space-y-4">
                            <Field
                                id={'file_name'}
                                name={'name'}
                                label={useMoveTerminology ? 'New Location' : 'New Name'}
                                placeholder={useMoveTerminology ? 'destination/path' : 'Enter new name...'}
                                description={
                                    useMoveTerminology
                                        ? 'Enter the new name and directory of this file or folder, relative to the current directory.'
                                        : 'Enter the new name for this file or folder.'
                                }
                                className="appearance-none outline-none w-full min-w-0 p-3 rounded-md text-sm transition-all duration-150 resize-none shadow-none h-10 bg-hyper-background text-hyper-foreground border focus:border-2 border-hyper-primary hover:border-hyper-accent focus:outline-none disabled:bg-neutral-100 font-medium hyper-input font-mono"
                                autoFocus
                            />
                            
                            {/* Preview Path */}
                            {useMoveTerminology && values.name && (
                                <div className="bg-hyper-background border border-hyper-primary rounded-lg p-3">
                                    <p className="text-sm text-hyper-accent">
                                        <span className="text-hyper-secondary">This will be moved to </span>
                                        <Code className="text-hyper-accent">
                                            /home/<USER>/
                                            <span className="text-hyper-primary">
                                                {join(directory, values.name).replace(/^(\.\.\/|\/)+/, '')}
                                            </span>
                                        </Code>
                                    </p>
                                </div>
                            )}
                        </div>

                        <Dialog.Footer>
                            <Button 
                                size="small" 
                                isSecondary 
                                onClick={onDismissed}
                                disabled={isSubmitting}
                            >
                                Cancel
                            </Button>
                            <Button 
                                size="small"
                                type="submit"
                                disabled={isSubmitting}
                                isLoading={isSubmitting}
                                className="flex items-center gap-2"
                            >
                                {useMoveTerminology ? <Move size={16} /> : <Edit size={16} />}
                                {useMoveTerminology ? 'Move' : 'Rename'}
                            </Button>
                        </Dialog.Footer>
                    </Form>
                )}
            </Formik>
        </Dialog>
    );
};

export default RenameFileModal;
