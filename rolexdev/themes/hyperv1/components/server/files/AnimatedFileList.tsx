import React from 'react';
import { motion, AnimatePresence, LayoutGroup } from 'framer-motion';
import { FileObject } from '@/api/server/files/loadDirectory';
import FileObjectRow from './FileObjectRow';

interface AnimatedFileListProps {
    files: FileObject[];
    className?: string;
    enableAnimations?: boolean;
}

const fileItemVariants = {
    initial: { 
        opacity: 0, 
        y: 20,
        scale: 0.98
    },
    animate: { 
        opacity: 1, 
        y: 0,
        scale: 1
    },
    exit: { 
        opacity: 0, 
        y: -20,
        scale: 0.98,
        transition: {
            duration: 0.2,
            ease: "easeInOut"
        }
    }
};

export default function AnimatedFileList({ 
    files, 
    className = '',
    enableAnimations = true
}: AnimatedFileListProps) {

    return (
        <LayoutGroup>
            <motion.div 
                className={className}
                initial="initial"
                animate="animate"
                variants={{
                    initial: {},
                    animate: {
                        transition: {
                            staggerChildren: 0.02,
                            delayChildren: 0.1
                        }
                    }
                }}
                style={{
                    position: 'relative'
                }}
            >
                <AnimatePresence initial={false}>
                    {files.map((file) => (
                        <motion.div
                            key={file.key}
                            variants={enableAnimations ? fileItemVariants : undefined}
                            layout={enableAnimations}
                            initial={enableAnimations ? "initial" : undefined}
                            animate={enableAnimations ? "animate" : undefined}
                            exit={enableAnimations ? "exit" : undefined}
                            transition={enableAnimations ? {
                                type: 'spring',
                                stiffness: 400,
                                damping: 25,
                                mass: 0.8
                            } : undefined}
                            style={{
                                position: 'relative',
                                zIndex: 1
                            }}
                            whileHover={enableAnimations ? {
                                zIndex: 2,
                                transition: { duration: 0.1 }
                            } : undefined}
                        >
                            <FileObjectRow file={file} />
                        </motion.div>
                    ))}
                </AnimatePresence>
            </motion.div>
        </LayoutGroup>
    );
}