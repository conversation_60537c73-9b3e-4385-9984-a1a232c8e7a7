import React, { useEffect, useState, useMemo } from 'react';
import { httpErrorToHuman } from '@/api/http';
import { CSSTransition } from 'react-transition-group';
import Spinner from '@/components/elements/Spinner';
import RecycleBinItem from '@rolexdev/themes/hyperv1/components/server/files/RecycleBinItem';
import RecycleBinSearch from '@rolexdev/themes/hyperv1/components/server/files/RecycleBinSearch';
import RecycleBinSorter, { RecycleBinSortOption, SortDirection } from '@rolexdev/themes/hyperv1/components/server/files/RecycleBinSorter';
import { RecycledFile, RecycleBinFilterOptions } from '@rolexdev/themes/hyperv1/types/recycleBin';
import { NavLink, useHistory } from 'react-router-dom';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import { ServerError } from '@/components/elements/ScreenBlock';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import ServerContentBlock from '@rolexdev/themes/hyperv1/components/elements/ServerContentBlock';
import { useStoreActions } from '@/state/hooks';
import ErrorBoundary from '@/components/elements/ErrorBoundary';
import FlashMessageRender from '@/components/FlashMessageRender';
import { 
    Trash2, 
    RotateCcw, 
    FolderOpen, 
    Search, 
    Filter, 
    ArrowLeft,
    RefreshCw,
    AlertTriangle,
    Calendar,
    HardDrive,
    Clock
} from 'lucide-react';
import { format, formatDistanceToNow, isAfter, addDays } from 'date-fns';
import { 
    getRecycleBinFiles, 
    getRecycleBinStats, 
    restoreMultipleFiles, 
    permanentlyDeleteFiles, 
    emptyRecycleBin 
} from '@rolexdev/themes/hyperv1/api/server/files/recycleBin';
import ConfirmationModal from '@rolexdev/themes/hyperv1/components/elements/ConfirmationModal';
import { FileActionCheckbox } from '@rolexdev/themes/hyperv1/components/server/files/SelectFileCheckbox';
import style from './style.module.css';

export default () => {
    const history = useHistory();
    const id = ServerContext.useStoreState((state) => state.server.data!.id);
    const clearFlashes = useStoreActions((actions) => actions.flashes.clearFlashes);
    
    // Recycle bin state
    const recycledFiles = ServerContext.useStoreState((state) => state.recycleBin.recycledFiles);
    const selectedFiles = ServerContext.useStoreState((state) => state.recycleBin.selectedRecycledFiles);
    const stats = ServerContext.useStoreState((state) => state.recycleBin.stats);
    const filters = ServerContext.useStoreState((state) => state.recycleBin.filters);
    const loading = ServerContext.useStoreState((state) => state.recycleBin.loading);
    const openDropdownFileId = ServerContext.useStoreState((state) => state.recycleBin.openDropdownFileId);
    
    // Recycle bin actions
    const setRecycledFiles = ServerContext.useStoreActions((actions) => actions.recycleBin.setRecycledFiles);
    const setSelectedRecycledFiles = ServerContext.useStoreActions((actions) => actions.recycleBin.setSelectedRecycledFiles);
    const setStats = ServerContext.useStoreActions((actions) => actions.recycleBin.setStats);
    const setFilters = ServerContext.useStoreActions((actions) => actions.recycleBin.setFilters);
    const setLoading = ServerContext.useStoreActions((actions) => actions.recycleBin.setLoading);
    const removeRecycledFile = ServerContext.useStoreActions((actions) => actions.recycleBin.removeRecycledFile);
    const setOpenDropdownFileId = ServerContext.useStoreActions((actions) => actions.recycleBin.setOpenDropdownFileId);

    // Local state
    const [searchTerm, setSearchTerm] = useState(filters.searchTerm || '');
    const [sortBy, setSortBy] = useState<RecycleBinSortOption>(filters.sortBy || 'deletedAt');
    const [sortDirection, setSortDirection] = useState<SortDirection>(filters.sortDirection || 'desc');
    const [showConfirmRestore, setShowConfirmRestore] = useState(false);
    const [showConfirmDelete, setShowConfirmDelete] = useState(false);
    const [showConfirmEmpty, setShowConfirmEmpty] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Filtered and sorted files
    const filteredAndSortedFiles = useMemo(() => {
        let filtered = recycledFiles;

        // Apply search filter
        if (searchTerm) {
            filtered = filtered.filter(file => 
                file.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                file.originalPath.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Apply file type filter
        if (filters.fileType && filters.fileType !== 'all') {
            filtered = filtered.filter(file => 
                filters.fileType === 'file' ? file.isFile : !file.isFile
            );
        }

        // Apply expiring soon filter
        if (filters.showExpiringSoon) {
            const tomorrow = addDays(new Date(), 1);
            filtered = filtered.filter(file => 
                isAfter(tomorrow, new Date(file.expiresAt))
            );
        }

        // Sort files
        filtered = filtered.sort((a, b) => {
            let comparison = 0;
            
            switch (sortBy) {
                case 'name':
                    comparison = a.name.localeCompare(b.name);
                    break;
                case 'deletedAt':
                    comparison = new Date(a.deletedAt).getTime() - new Date(b.deletedAt).getTime();
                    break;
                case 'expiresAt':
                    comparison = new Date(a.expiresAt).getTime() - new Date(b.expiresAt).getTime();
                    break;
                case 'size':
                    comparison = (a.size || 0) - (b.size || 0);
                    break;
                case 'originalPath':
                    comparison = a.originalPath.localeCompare(b.originalPath);
                    break;
                default:
                    comparison = new Date(a.deletedAt).getTime() - new Date(b.deletedAt).getTime();
            }
            
            return sortDirection === 'desc' ? -comparison : comparison;
        });

        return filtered;
    }, [recycledFiles, searchTerm, sortBy, sortDirection, filters]);

    // Load recycle bin data
    const loadRecycleBinData = async () => {
        setLoading({ type: 'files', loading: true });
        setError(null);
        
        try {
            const [filesResponse, statsResponse] = await Promise.all([
                getRecycleBinFiles(id, filters),
                getRecycleBinStats(id)
            ]);
            
            setRecycledFiles(filesResponse.data);
            setStats(statsResponse.data);
        } catch (err: any) {
            setError(httpErrorToHuman(err));
        } finally {
            setLoading({ type: 'files', loading: false });
        }
    };

    // Handle bulk restore
    const handleBulkRestore = async () => {
        if (selectedFiles.length === 0) return;
        
        setLoading({ type: 'restore', loading: true });
        setShowConfirmRestore(false);
        
        try {
            const result = await restoreMultipleFiles(id, { 
                fileIds: selectedFiles, 
                overwrite: false 
            });
            
            if (result.success) {
                // Remove restored files from state
                selectedFiles.forEach(fileId => removeRecycledFile(fileId));
                setSelectedRecycledFiles([]);
                
                // Refresh data
                await loadRecycleBinData();
                
                clearFlashes('files');
                // Add success flash message here if needed
            }
        } catch (err: any) {
            setError(httpErrorToHuman(err));
        } finally {
            setLoading({ type: 'restore', loading: false });
        }
    };

    // Handle bulk permanent delete
    const handleBulkDelete = async () => {
        if (selectedFiles.length === 0) return;
        
        setLoading({ type: 'delete', loading: true });
        setShowConfirmDelete(false);
        
        try {
            await permanentlyDeleteFiles(id, { fileIds: selectedFiles });
            
            // Remove deleted files from state
            selectedFiles.forEach(fileId => removeRecycledFile(fileId));
            setSelectedRecycledFiles([]);
            
            // Refresh data
            await loadRecycleBinData();
        } catch (err: any) {
            setError(httpErrorToHuman(err));
        } finally {
            setLoading({ type: 'delete', loading: false });
        }
    };

    // Handle empty recycle bin
    const handleEmptyRecycleBin = async () => {
        setLoading({ type: 'delete', loading: true });
        setShowConfirmEmpty(false);
        
        try {
            await emptyRecycleBin(id, { confirmed: true });
            
            // Clear all files from state
            setRecycledFiles([]);
            setSelectedRecycledFiles([]);
            
            // Refresh data
            await loadRecycleBinData();
        } catch (err: any) {
            setError(httpErrorToHuman(err));
        } finally {
            setLoading({ type: 'delete', loading: false });
        }
    };

    // Handle search
    const handleSearchChange = (value: string) => {
        setSearchTerm(value);
    };

    // Handle sort change
    const handleSortChange = (newSortBy: RecycleBinSortOption, newDirection: SortDirection) => {
        setSortBy(newSortBy);
        setSortDirection(newDirection);
    };

    // Handle filter changes
    const handleFilterChange = (newFilters: Partial<RecycleBinFilterOptions>) => {
        setFilters(newFilters);
    };

    // Handle select all
    const onSelectAllClick = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSelectedRecycledFiles(
            e.currentTarget.checked 
                ? filteredAndSortedFiles.map(file => file.id) 
                : []
        );
    };

    // Load data on component mount
    useEffect(() => {
        clearFlashes('files');
        loadRecycleBinData();
    }, []);

    // Close dropdowns on scroll
    useEffect(() => {
        const handleScroll = () => {
            setOpenDropdownFileId(null);
        };

        window.addEventListener('scroll', handleScroll, true);
        return () => window.removeEventListener('scroll', handleScroll, true);
    }, [setOpenDropdownFileId]);

    if (error) {
        return <ServerError message={error} onRetry={loadRecycleBinData} />;
    }

    return (
        <ServerContentBlock title={'Recycle Bin'} showFlashKey={'files'}>
            {/* Header Section */}
            <div className="mb-6">
                <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                        <Trash2 size={20} className="text-hyper-accent" />
                    </div>
                    <div>
                        <h2 className="text-lg font-bold text-hyper-primary">Recycle Bin</h2>
                        <p className="text-sm text-hyper-muted-foreground">
                            Manage and restore deleted files
                        </p>
                    </div>
                </div>
                
                <FlashMessageRender byKey={'files'} className="mb-4" />

                {/* Stats Bar */}
                {stats && (
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-4">
                            <div className="flex items-center gap-3">
                                <FolderOpen size={20} className="text-hyper-accent" />
                                <div>
                                    <p className="text-sm text-hyper-muted-foreground">Total Items</p>
                                    <p className="text-lg font-semibold text-hyper-primary">{stats.totalItems}</p>
                                </div>
                            </div>
                        </div>
                        <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-4">
                            <div className="flex items-center gap-3">
                                <HardDrive size={20} className="text-blue-500" />
                                <div>
                                    <p className="text-sm text-hyper-muted-foreground">Total Size</p>
                                    <p className="text-lg font-semibold text-hyper-primary">
                                        {stats?.totalSize ? (stats.totalSize / (1024 * 1024)).toFixed(1) : '0.0'} MB
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-4">
                            <div className="flex items-center gap-3">
                                <Clock size={20} className="text-orange-500" />
                                <div>
                                    <p className="text-sm text-hyper-muted-foreground">Expiring This Week</p>
                                    <p className="text-lg font-semibold text-hyper-primary">{stats.expiringThisWeek}</p>
                                </div>
                            </div>
                        </div>
                        <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-4">
                            <div className="flex items-center gap-3">
                                <Clock size={20} className="text-blue-500" />
                                <div>
                                    <p className="text-sm text-hyper-muted-foreground">Expiring Today</p>
                                    <p className="text-lg font-semibold text-hyper-primary">{stats.expiringToday}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            <div className='rounded-lg bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary p-6 mb-6 gap-6 flex flex-col'>
                <ErrorBoundary>
                    {/* Search and Sort Bar */}
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 justify-between">
                        <RecycleBinSearch 
                            searchTerm={searchTerm}
                            onSearchChange={handleSearchChange}
                        />
                        <div className='w-full md:w-auto'>
                            <RecycleBinSorter 
                                sortBy={sortBy}
                                sortDirection={sortDirection}
                                onSortChange={handleSortChange}
                            />
                        </div>
                    </div>

                    <Can action={'file.create'}>
                        {/* Desktop Layout: Left-aligned action buttons, right-aligned refresh */}
                        <div className="hidden lg:flex items-center justify-between gap-4">
                            <div className='flex items-center gap-3'>
                                {selectedFiles.length > 0 && (
                                    <>
                                        <Button
                                            size="small"
                                            onClick={() => setShowConfirmRestore(true)}
                                            disabled={loading.restore}
                                            className="flex items-center gap-2"
                                        >
                                            <RotateCcw size={14} />
                                            Restore ({selectedFiles.length})
                                        </Button>
                                        <Button
                                            size="small"
                                            color="red"
                                            onClick={() => setShowConfirmDelete(true)}
                                            disabled={loading.delete}
                                            className="flex items-center gap-2"
                                        >
                                            <Trash2 size={14} />
                                            Delete Forever ({selectedFiles.length})
                                        </Button>
                                    </>
                                )}
                                {recycledFiles.length > 0 && (
                                    <Button
                                        size="small"
                                        color="red"
                                        onClick={() => setShowConfirmEmpty(true)}
                                        disabled={loading.delete}
                                        className="flex items-center gap-2"
                                    >
                                        <Trash2 size={14} />
                                        Empty Recycle Bin
                                    </Button>
                                )}
                            </div>
                            <div className='flex items-center gap-3'>
                                <Button
                                    size="small"
                                    onClick={loadRecycleBinData}
                                    disabled={loading.files}
                                    className="flex items-center gap-2"
                                >
                                    <RefreshCw size={14} className={loading.files ? 'animate-spin' : ''} />
                                    Refresh
                                </Button>
                                <Button
                                    size="small"
                                    isSecondary
                                    onClick={() => history.push(`/server/${id}/files`)}
                                    className="flex items-center gap-1"
                                >
                                    <ArrowLeft size={14} />
                                    Back to Files
                                </Button>
                            </div>
                        </div>

                        {/* Mobile/Tablet Layout: Adaptive grid based on available space */}
                        <div className="lg:hidden">
                            <div className="grid gap-3" style={{ gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))' }}>
                                {selectedFiles.length > 0 && (
                                    <>
                                        <Button
                                            size="small"
                                            onClick={() => setShowConfirmRestore(true)}
                                            disabled={loading.restore}
                                            className="flex items-center gap-2"
                                        >
                                            <RotateCcw size={14} />
                                            Restore
                                        </Button>
                                        <Button
                                            size="small"
                                            color="red"
                                            onClick={() => setShowConfirmDelete(true)}
                                            disabled={loading.delete}
                                            className="flex items-center gap-2"
                                        >
                                            <Trash2 size={14} />
                                            Delete
                                        </Button>
                                    </>
                                )}
                                {recycledFiles.length > 0 && (
                                    <Button
                                        size="small"
                                        color="red"
                                        onClick={() => setShowConfirmEmpty(true)}
                                        disabled={loading.delete}
                                        className="flex items-center gap-2"
                                    >
                                        <Trash2 size={14} />
                                        Empty
                                    </Button>
                                )}
                                <Button
                                    size="small"
                                    onClick={loadRecycleBinData}
                                    disabled={loading.files}
                                    className="flex items-center gap-2"
                                >
                                    <RefreshCw size={14} className={loading.files ? 'animate-spin' : ''} />
                                    Refresh
                                </Button>
                                <Button
                                    size="small"
                                    isSecondary
                                    onClick={() => history.push(`/server/${id}/files`)}
                                    className="flex items-center gap-2"
                                >
                                    <ArrowLeft size={14} />
                                    Back
                                </Button>
                            </div>
                        </div>
                    </Can>

                    {/* Selection Bar */}
                    <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
                        <div className="flex items-center gap-4 flex-1 min-w-0 w-full">
                            <FileActionCheckbox
                                type={'checkbox'}
                                checked={selectedFiles.length === filteredAndSortedFiles.length && selectedFiles.length > 0}
                                onChange={onSelectAllClick}
                            />
                            <span className="text-sm text-hyper-primary">
                                {selectedFiles.length > 0 
                                    ? `${selectedFiles.length} of ${filteredAndSortedFiles.length} selected` 
                                    : `${filteredAndSortedFiles.length} items`
                                }
                            </span>
                        </div>
                    </div>
                </ErrorBoundary>
            </div>

            {/* File Content */}
            {loading.files ? (
                <Spinner size={'large'} centered />
            ) : (
                <>
                    {!filteredAndSortedFiles.length ? (
                        <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-8 text-center w-full max-w-md mx-auto">
                            <div className="w-16 h-16 rounded-lg bg-hyper-primary-10 flex items-center justify-center mx-auto mb-4">
                                <Trash2 size={32} className="text-hyper-accent" />
                            </div>
                            {searchTerm ? (
                                <>
                                    <h3 className="text-lg font-semibold text-hyper-primary mb-2">No Results Found</h3>
                                    <p className="text-sm text-hyper-accent mb-6">
                                        No deleted files match your search "{searchTerm}".
                                    </p>
                                    <button
                                        onClick={() => handleSearchChange('')}
                                        className="px-4 py-2 bg-hyper-accent text-white rounded-lg hover:bg-opacity-80 transition-all duration-200"
                                    >
                                        Clear Search
                                    </button>
                                </>
                            ) : (
                                <>
                                    <h3 className="text-lg font-semibold text-hyper-primary mb-2">Recycle Bin is Empty</h3>
                                    <p className="text-sm text-hyper-accent mb-6">
                                        Deleted files will appear here and be automatically removed after 7 days.
                                    </p>
                                    <Button
                                        onClick={() => history.push(`/server/${id}/files`)}
                                        className="flex items-center gap-2"
                                    >
                                        <FolderOpen size={16} />
                                        Go to File Manager
                                    </Button>
                                </>
                            )}
                        </div>
                    ) : (
                        <CSSTransition classNames={'fade'} timeout={150} appear in>
                            <div>
                                {/* Search Results Info */}
                                {searchTerm && (
                                    <div className="bg-hyper-primary-10 border border-hyper-primary rounded-lg p-3 mb-4">
                                        <p className="text-sm text-hyper-primary">
                                            Showing {filteredAndSortedFiles.length} results for "{searchTerm}"
                                        </p>
                                    </div>
                                )}
                                
                                {/* File List */}
                                <div className="space-y-2">
                                    {filteredAndSortedFiles.map((file, index) => (
                                        <RecycleBinItem 
                                            key={`${file.id}-${index}`} 
                                            file={file} 
                                            onFileAction={loadRecycleBinData}
                                        />
                                    ))}
                                </div>
                            </div>
                        </CSSTransition>
                    )}
                </>
            )}

            {/* Confirmation Modals */}
            <ConfirmationModal
                visible={showConfirmRestore}
                title="Restore Files"
                buttonText="Restore"
                onConfirmed={handleBulkRestore}
                onModalDismissed={() => setShowConfirmRestore(false)}
            >
                <p className="text-sm text-hyper-muted-foreground">
                    Are you sure you want to restore {selectedFiles.length} selected files to their original locations?
                </p>
            </ConfirmationModal>

            <ConfirmationModal
                visible={showConfirmDelete}
                title="Permanently Delete Files"
                buttonText="Delete Forever"
                onConfirmed={handleBulkDelete}
                onModalDismissed={() => setShowConfirmDelete(false)}
            >
                <div className="flex items-start gap-3">
                    <AlertTriangle size={20} className="text-red-500 flex-shrink-0 mt-1" />
                    <div>
                        <p className="text-sm text-hyper-muted-foreground mb-2">
                            Are you sure you want to permanently delete {selectedFiles.length} selected files?
                        </p>
                        <p className="text-sm text-red-500 font-medium">
                            This action cannot be undone!
                        </p>
                    </div>
                </div>
            </ConfirmationModal>

            <ConfirmationModal
                visible={showConfirmEmpty}
                title="Empty Recycle Bin"
                buttonText="Empty Recycle Bin"
                onConfirmed={handleEmptyRecycleBin}
                onModalDismissed={() => setShowConfirmEmpty(false)}
            >
                <div className="flex items-start gap-3">
                    <AlertTriangle size={20} className="text-red-500 flex-shrink-0 mt-1" />
                    <div>
                        <p className="text-sm text-hyper-muted-foreground mb-2">
                            Are you sure you want to permanently delete all {recycledFiles.length} files in the recycle bin?
                        </p>
                        <p className="text-sm text-red-500 font-medium">
                            This action cannot be undone!
                        </p>
                    </div>
                </div>
            </ConfirmationModal>
        </ServerContentBlock>
    );
};