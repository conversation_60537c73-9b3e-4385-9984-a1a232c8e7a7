import React, { useState, useRef, useEffect } from 'react';
import { format, formatDistanceToNow, isValid, parseISO, isAfter, subDays } from 'date-fns';

// Helper function to safely parse dates
const safeDate = (dateString: string | null | undefined): Date | null => {
    if (!dateString) return null;
    try {
        const date = parseISO(dateString);
        return isValid(date) ? date : null;
    } catch {
        return null;
    }
};
import { 
    File, 
    Folder, 
    RotateCcw, 
    Trash2, 
    MapPin, 
    Clock, 
    AlertTriangle,
    Download,
    Eye,
    HardDrive,
    Calendar
} from 'lucide-react';
import { RecycledFile } from '@rolexdev/themes/hyperv1/types/recycleBin';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { 
    restoreFile, 
    permanentlyDeleteFiles, 
    downloadRecycledFile, 
    previewRecycledFile 
} from '@rolexdev/themes/hyperv1/api/server/files/recycleBin';
import { httpErrorTo<PERSON>uman } from '@/api/http';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import ConfirmationModal from '@rolexdev/themes/hyperv1/components/elements/ConfirmationModal';
import { StandaloneCheckbox } from '@rolexdev/themes/hyperv1/components/elements/Checkbox';
//import DropdownMenu from '@rolexdev/themes/hyperv1/components/elements/DropdownMenu';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import { useStoreActions } from '@/state/hooks';

interface Props {
    file: RecycledFile;
    onFileAction: () => void;
}

export default ({ file, onFileAction }: Props) => {
    const uuid = ServerContext.useStoreState((state: any) => state.server.data!.id);
    const selectedFiles = ServerContext.useStoreState((state: any) => state.recycleBin.selectedRecycledFiles);
    const openDropdownFileId = ServerContext.useStoreState((state: any) => state.recycleBin.openDropdownFileId);
    
    const setSelectedRecycledFiles = ServerContext.useStoreActions((actions: any) => actions.recycleBin.setSelectedRecycledFiles);
    const appendSelectedRecycledFile = ServerContext.useStoreActions((actions: any) => actions.recycleBin.appendSelectedRecycledFile);
    const removeSelectedRecycledFile = ServerContext.useStoreActions((actions: any) => actions.recycleBin.removeSelectedRecycledFile);
    const removeRecycledFile = ServerContext.useStoreActions((actions: any) => actions.recycleBin.removeRecycledFile);
    const setOpenDropdownFileId = ServerContext.useStoreActions((actions: any) => actions.recycleBin.setOpenDropdownFileId);
    const clearFlashes = useStoreActions((actions: any) => actions.flashes.clearFlashes);
    const addError = useStoreActions((actions: any) => actions.flashes.addFlash);

    const [showConfirmRestore, setShowConfirmRestore] = useState(false);
    const [showConfirmDelete, setShowConfirmDelete] = useState(false);
    const [showPreview, setShowPreview] = useState(false);
    const [previewContent, setPreviewContent] = useState<string>('');
    const [loading, setLoading] = useState(false);

    // Ref to track if component is mounted
    const mountedRef = useRef(true);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            mountedRef.current = false;
        };
    }, []);

    // Safe setState function that checks if component is mounted
    const safeSetLoading = (value: boolean) => {
        if (mountedRef.current) {
            setLoading(value);
        }
    };

    const isSelected = selectedFiles.includes(file.id);
    const expiresAtDate = safeDate(file.expiresAt);
    const isExpiringSoon = expiresAtDate ? isAfter(new Date(), subDays(expiresAtDate, 1)) : false;
    const isExpired = expiresAtDate ? isAfter(new Date(), expiresAtDate) : false;

    // Handle file selection
    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.checked) {
            appendSelectedRecycledFile(file.id);
        } else {
            removeSelectedRecycledFile(file.id);
        }
    };

    // Handle file restore
    const handleRestore = async () => {
        safeSetLoading(true);
        setShowConfirmRestore(false);
        
        // Validate file ID before making request
        if (!file.id) {
            addError({
                key: `files-restore-invalid-${Date.now()}`,
                type: 'error',
                message: 'Error: File ID is missing. Cannot restore file.',
            });
            safeSetLoading(false);
            return;
        }
        
        try {
            await restoreFile(uuid, { 
                fileId: file.id, 
                overwrite: false 
            });
            
            removeRecycledFile(file.id);
            onFileAction();
            
            clearFlashes('files');
            // Add success message
        } catch (error: any) {
            addError({
                key: `files-restore-${file.id || 'unknown'}-${Date.now()}`,
                type: 'error',
                message: httpErrorToHuman(error),
            });
        } finally {
            if (mountedRef.current) {
                safeSetLoading(false);
            }
        }
    };

    // Handle permanent delete
    const handlePermanentDelete = async () => {
        safeSetLoading(true);
        setShowConfirmDelete(false);
        
        try {
            await permanentlyDeleteFiles(uuid, { fileIds: [file.id] });
            
            removeRecycledFile(file.id);
            onFileAction();
            
            clearFlashes('files');
            // Add success message
        } catch (error: any) {
            addError({
                key: `files-delete-${file.id || 'unknown'}-${Date.now()}`,
                type: 'error',
                message: httpErrorToHuman(error),
            });
        } finally {
            if (mountedRef.current) {
                safeSetLoading(false);
            }
        }
    };

    // Handle file preview
    const handlePreview = async () => {
        if (!file.isFile) return;
        
        safeSetLoading(true);
        try {
            const content = await previewRecycledFile(uuid, file.id);
            if (mountedRef.current) {
                setPreviewContent(content);
                setShowPreview(true);
            }
        } catch (error: any) {
            addError({
                key: `files-preview-${file.id || 'unknown'}-${Date.now()}`,
                type: 'error',
                message: 'Failed to load file preview: ' + httpErrorToHuman(error),
            });
        } finally {
            if (mountedRef.current) {
                safeSetLoading(false);
            }
        }
    };

    // Handle file download
    const handleDownload = async () => {
        if (!file.isFile) return;
        
        safeSetLoading(true);
        try {
            const downloadUrl = await downloadRecycledFile(uuid, file.id);
            window.open(downloadUrl, '_blank');
        } catch (error: any) {
            addError({
                key: `files-download-${file.id || 'unknown'}-${Date.now()}`,
                type: 'error',
                message: 'Failed to download file: ' + httpErrorToHuman(error),
            });
        } finally {
            if (mountedRef.current) {
                safeSetLoading(false);
            }
        }
    };

    // Get file icon
    const getFileIcon = () => {
        if (!file.isFile) {
            return <Folder size={18} className="text-hyper-accent" />;
        }
        return <File size={18} className="text-hyper-accent" />;
    };

    // Format file size
    const formatFileSize = (bytes?: number) => {
        if (!bytes || bytes === 0) return '0 B';
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    };

    return (
        <>
            <div className={`
                group flex items-center gap-4 p-4 
                bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg 
                hover:border-hyper-accent transition-all duration-200 relative
                ${isExpired ? 'opacity-50' : isExpiringSoon ? 'border-orange-500/50' : ''}
            `}>
                <div className="flex items-center gap-4 w-full">
                    {/* Selection Checkbox */}
                    <div className="flex-shrink-0 flex items-center justify-center">
                        <StandaloneCheckbox
                            checked={isSelected}
                            onChange={handleFileSelect}
                        />
                    </div>

                    {/* File Icon */}
                    <div className="flex-shrink-0">
                        {getFileIcon()}
                    </div>

                    {/* File Details */}
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between w-full gap-2">
                        {/* File Name and Metadata */}
                        <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                                <h4 className="text-lg font-semibold text-hyper-primary truncate">{file.name}</h4>
                                {isExpired && (
                                    <span className="px-2 py-0.5 text-xs bg-red-500/20 text-red-500 rounded-full">
                                        Expired
                                    </span>
                                )}
                                {isExpiringSoon && !isExpired && (
                                    <span className="px-2 py-0.5 text-xs bg-orange-500/20 text-orange-500 rounded-full">
                                        Expiring Soon
                                    </span>
                                )}
                            </div>
                            <div className="flex items-center gap-4 text-sm text-hyper-accent mt-1">
                                {/* File Size */}
                                {file.isFile && file.size && (
                                    <div className="flex items-center gap-1">
                                        <HardDrive size={14} />
                                        <span>{formatFileSize(file.size)}</span>
                                    </div>
                                )}
                                
                                {/* Deleted Date */}
                                <div className="flex items-center gap-1">
                                    <Clock size={14} />
                                    <span>Deleted {(() => {
                                        const deletedDate = safeDate(file.deletedAt);
                                        return deletedDate ? formatDistanceToNow(deletedDate, { addSuffix: true }) : 'Unknown';
                                    })()}</span>
                                </div>
                                
                                {/* Expiry Date */}
                                <div className="flex items-center gap-1">
                                    <Calendar size={14} />
                                    <span>Expires {(() => {
                                        return expiresAtDate ? formatDistanceToNow(expiresAtDate, { addSuffix: true }) : 'Unknown';
                                    })()}</span>
                                </div>
                                
                                {/* Original Path */}
                                <div className="flex items-center gap-1 min-w-0">
                                    <MapPin size={14} className="flex-shrink-0" />
                                    <span className="truncate">{file.originalPath}</span>
                                </div>
                            </div>
                        </div>
                        
                        {/* Status Badge */}
                        <div className="flex-shrink-0">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                file.isFile 
                                    ? 'bg-blue-400/10 text-blue-400 border border-blue-400/20' 
                                    : 'bg-yellow-400/10 text-yellow-400 border border-yellow-400/20'
                            }`}>
                                {file.isFile ? 'File' : 'Directory'}
                            </span>
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        {/* Preview button for text files */}
                        {file.isFile && (file.mimetype?.startsWith('text/') || file.extension?.match(/\.(txt|md|json|xml|yml|yaml|ini|conf|log)$/i)) && (
                            <button
                                onClick={handlePreview}
                                disabled={loading}
                                className="p-2 text-hyper-muted-foreground hover:text-hyper-accent hover:bg-hyper-primary-10 rounded-lg transition-all duration-200"
                                title="Preview File"
                            >
                                <Eye size={16} />
                            </button>
                        )}
                        
                        {/* Download button for files */}
                        {file.isFile && (
                            <button
                                onClick={handleDownload}
                                disabled={loading}
                                className="p-2 text-hyper-muted-foreground hover:text-hyper-accent hover:bg-hyper-primary-10 rounded-lg transition-all duration-200"
                                title="Download File"
                            >
                                <Download size={16} />
                            </button>
                        )}
                        
                        <Can action={'file.create'}>
                            <button
                                onClick={() => setShowConfirmRestore(true)}
                                disabled={loading}
                                className="p-2 text-green-500 hover:text-green-400 hover:bg-green-500/10 rounded-lg transition-all duration-200"
                                title="Restore File"
                            >
                                <RotateCcw size={16} />
                            </button>
                        </Can>
                        
                        <Can action={'file.delete'}>
                            <button
                                onClick={() => setShowConfirmDelete(true)}
                                disabled={loading}
                                className="p-2 text-red-500 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200"
                                title="Delete Permanently"
                            >
                                <Trash2 size={16} />
                            </button>
                        </Can>
                    </div>
                </div>
            </div>

            {/* File Preview Modal */}
            {showPreview && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                    <div className="bg-hyper-sidebar border border-hyper-primary rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-auto">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-semibold text-hyper-primary">Preview: {file.name}</h3>
                            <Button
                                size="small"
                                isSecondary
                                onClick={() => setShowPreview(false)}
                            >
                                ×
                            </Button>
                        </div>
                        <pre className="bg-hyper-primary-10 p-4 rounded text-sm text-hyper-primary whitespace-pre-wrap max-h-96 overflow-auto">
                            {previewContent}
                        </pre>
                    </div>
                </div>
            )}

            {/* Confirmation Modals */}
            <ConfirmationModal
                visible={showConfirmRestore}
                title="Restore File"
                buttonText="Restore"
                onConfirmed={handleRestore}
                onModalDismissed={() => setShowConfirmRestore(false)}
            >
                <p className="text-sm text-hyper-muted-foreground">
                    Are you sure you want to restore <strong>{file.name}</strong> to <strong>{file.originalPath}</strong>?
                </p>
            </ConfirmationModal>

            <ConfirmationModal
                visible={showConfirmDelete}
                title="Permanently Delete File"
                buttonText="Delete Forever"
                onConfirmed={handlePermanentDelete}
                onModalDismissed={() => setShowConfirmDelete(false)}
            >
                <div className="flex items-start gap-3">
                    <AlertTriangle size={20} className="text-red-500 flex-shrink-0 mt-1" />
                    <div>
                        <p className="text-sm text-hyper-muted-foreground mb-2">
                            Are you sure you want to permanently delete <strong>{file.name}</strong>?
                        </p>
                        <p className="text-sm text-red-500 font-medium">
                            This action cannot be undone!
                        </p>
                    </div>
                </div>
            </ConfirmationModal>
        </>
    );
};