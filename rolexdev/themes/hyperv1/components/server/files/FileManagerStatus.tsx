import React, { useEffect } from 'react';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { CloudUpload, X } from 'lucide-react';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import Tooltip from '@rolexdev/themes/hyperv1/components/elements/tooltip/Tooltip';
import Code from '@/components/elements/Code';
import { useSignal } from '@preact/signals-react';

const svgProps = {
    cx: 16,
    cy: 16,
    r: 14,
    strokeWidth: 3,
    fill: 'none',
    stroke: 'currentColor',
};

const Spinner = ({ progress, className }: { progress: number; className?: string }) => (
    <svg viewBox={'0 0 32 32'} className={className}>
        <circle {...svgProps} className={'opacity-25'} />
        <circle
            {...svgProps}
            stroke={'currentColor'}
            strokeDasharray={28 * Math.PI}
            className={'rotate-[-90deg] origin-[50%_50%] transition-[stroke-dashoffset] duration-300'}
            style={{ strokeDashoffset: ((100 - progress) / 100) * 28 * Math.PI }}
        />
    </svg>
);

const FileUploadList = ({ onClose }: { onClose: () => void }) => {
    const cancelFileUpload = ServerContext.useStoreActions((actions) => actions.files.cancelFileUpload);
    const clearFileUploads = ServerContext.useStoreActions((actions) => actions.files.clearFileUploads);
    const uploads = ServerContext.useStoreState((state) =>
        Object.entries(state.files.uploads).sort(([a], [b]) => a.localeCompare(b))
    );

    return (
        <div className="space-y-4">
            {uploads.map(([name, file]) => (
                <div key={name} className="bg-hyper-background border border-hyper-primary rounded-lg p-4">
                    <div className="flex items-center gap-3">
                        <Tooltip content={`${Math.floor((file.loaded / file.total) * 100)}%`} placement={'left'}>
                            <div className="flex-shrink-0">
                                <Spinner progress={(file.loaded / file.total) * 100} className="w-6 h-6 text-hyper-accent" />
                            </div>
                        </Tooltip>
                        <div className="flex-1 min-w-0">
                            <Code className="text-sm text-hyper-primary truncate block">{name}</Code>
                            <div className="text-xs text-hyper-accent mt-1">
                                {Math.floor((file.loaded / file.total) * 100)}% uploaded
                            </div>
                        </div>
                        <button
                            onClick={() => cancelFileUpload(name)}
                            className="w-8 h-8 flex items-center justify-center rounded-lg text-red-400 hover:bg-red-400/10 transition-colors"
                        >
                            <X size={16} />
                        </button>
                    </div>
                </div>
            ))}
        </div>
    );
};

export default () => {
    const open = useSignal(false);

    const count = ServerContext.useStoreState((state) => Object.keys(state.files.uploads).length);
    const progress = ServerContext.useStoreState((state) => ({
        uploaded: Object.values(state.files.uploads).reduce((count, file) => count + file.loaded, 0),
        total: Object.values(state.files.uploads).reduce((count, file) => count + file.total, 0),
    }));
    
    const clearFileUploads = ServerContext.useStoreActions((actions) => actions.files.clearFileUploads);

    useEffect(() => {
        if (count === 0) {
            open.value = false;
        }
    }, [count]);

    return (
        <>
            {count > 0 && (
                <>
                    <Tooltip content={`${count} files are uploading, click to view`}>
                        <button
                            className="relative flex items-center justify-center w-10 h-10 rounded-lg bg-hyper-primary-10 text-hyper-accent hover:bg-hyper-primary-20 transition-colors"
                            onClick={() => (open.value = true)}
                        >
                            <Spinner progress={(progress.uploaded / progress.total) * 100} className="w-6 h-6" />
                            <CloudUpload className="h-3 w-3 absolute animate-pulse" />
                        </button>
                    </Tooltip>
                    
                    <Dialog
                        open={open.value}
                        onClose={() => (open.value = false)}
                        title="File Uploads"
                        description="The following files are being uploaded to your server."
                    >
                        <FileUploadList onClose={() => (open.value = false)} />
                        
                        <Dialog.Footer>
                            <Button 
                                size="small"
                                color="red"
                                onClick={() => {
                                    clearFileUploads();
                                    open.value = false;
                                }}
                            >
                                Cancel All Uploads
                            </Button>
                            <Button 
                                size="small" 
                                isSecondary 
                                onClick={() => (open.value = false)}
                            >
                                Close
                            </Button>
                        </Dialog.Footer>
                    </Dialog>
                </>
            )}
        </>
    );
};
