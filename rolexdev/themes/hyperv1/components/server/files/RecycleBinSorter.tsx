import React, { useState, useRef, useEffect } from 'react';
import { ArrowUpDown, ChevronDown, SortAsc, SortDesc } from 'lucide-react';

export type RecycleBinSortOption = 'name' | 'deletedAt' | 'expiresAt' | 'size' | 'originalPath';
export type SortDirection = 'asc' | 'desc';

interface RecycleBinSorterProps {
    sortBy: RecycleBinSortOption;
    sortDirection: SortDirection;
    onSortChange: (sortBy: RecycleBinSortOption, direction: SortDirection) => void;
}

const sortOptions = [
    { value: 'name' as const, label: 'Name' },
    { value: 'deletedAt' as const, label: 'Deleted Date' },
    { value: 'expiresAt' as const, label: 'Expires' },
    { value: 'size' as const, label: 'Size' },
    { value: 'originalPath' as const, label: 'Original Path' },
];

const RecycleBinSorter: React.FC<RecycleBinSorterProps> = ({ sortBy, sortDirection, onSortChange }) => {
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleSortOptionClick = (option: RecycleBinSortOption) => {
        // Always set ascending when selecting a new sort option
        onSortChange(option, 'asc');
        setIsOpen(false);
    };

    const handleDirectionChange = (direction: SortDirection) => {
        onSortChange(sortBy, direction);
    };

    const currentSortLabel = sortOptions.find(opt => opt.value === sortBy)?.label || 'Name';

    return (
        <div className="flex items-center gap-2 w-full justify-between">
            {/* Sort Option Dropdown */}
            <div className="relative" ref={dropdownRef}>
                <button
                    onClick={() => setIsOpen(!isOpen)}
                    className="flex items-center gap-2 px-3 py-2.5 bg-hyper-background border border-hyper-accent rounded-lg text-hyper-primary hover:bg-hyper-primary-10 transition-all duration-200 min-w-[140px]"
                >
                    <ArrowUpDown size={16} />
                    <span className="text-sm font-medium">{currentSortLabel}</span>
                    <ChevronDown size={14} className={`transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
                </button>

                {isOpen && (
                    <div className="absolute top-full right-0 mt-2 w-48 bg-hyper-sidebar border border-hyper-primary rounded-lg shadow-lg z-50 py-2">
                        {sortOptions.map((option) => (
                            <button
                                key={option.value}
                                onClick={() => handleSortOptionClick(option.value)}
                                className={`w-full px-4 py-2.5 text-left text-sm hover:bg-hyper-primary-10 transition-colors duration-200 flex items-center justify-between ${
                                    sortBy === option.value 
                                        ? 'text-hyper-accent bg-hyper-primary-10' 
                                        : 'text-hyper-primary'
                                }`}
                            >
                                <span>{option.label}</span>
                                {sortBy === option.value && (
                                    <ArrowUpDown size={14} className="text-hyper-accent" />
                                )}
                            </button>
                        ))}
                    </div>
                )}
            </div>

            {/* Direction Buttons */}
            <div className="flex items-center border border-hyper-primary rounded-lg overflow-hidden">
                <button
                    onClick={() => handleDirectionChange('asc')}
                    className={`p-2.5 transition-all duration-200 ${
                        sortDirection === 'asc'
                            ? 'bg-hyper-primary text-white'
                            : 'bg-hyper-background text-hyper-primary hover:bg-hyper-primary-10'
                    }`}
                    title="Sort Ascending"
                >
                    <SortAsc size={16} />
                </button>
                <button
                    onClick={() => handleDirectionChange('desc')}
                    className={`p-2.5 border-l border-hyper-primary transition-all duration-200 ${
                        sortDirection === 'desc'
                            ? 'bg-hyper-primary text-white'
                            : 'bg-hyper-background text-hyper-primary hover:bg-hyper-primary-10'
                    }`}
                    title="Sort Descending"
                >
                    <SortDesc size={16} />
                </button>
            </div>
        </div>
    );
};

export default RecycleBinSorter;