import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFileAlt, faFileArchive, faFileImport, faFolder } from '@fortawesome/free-solid-svg-icons';
import { encodePathSegments } from '@/helpers';
import { differenceInHours, format, formatDistanceToNow } from 'date-fns';
import React, { memo } from 'react';
import { FileObject } from '@/api/server/files/loadDirectory';
import FileDropdownMenu from '@rolexdev/themes/hyperv1/components/server/files/FileDropdownMenu';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { NavLink, useRouteMatch } from 'react-router-dom';
import tw from 'twin.macro';
import isEqual from 'react-fast-compare';
import SelectFileCheckbox from '@rolexdev/themes/hyperv1/components/server/files/SelectFileCheckbox';
import { usePermissions } from '@rolexdev/themes/hyperv1/plugins/usePermissions';
import { join } from 'path';
import { bytesToString } from '@/lib/formatters';
import { Folder, File, Link, Archive, FileText, Clock, HardDrive } from 'lucide-react';
import { motion } from 'framer-motion';

const Clickable: React.FC<{ file: FileObject }> = memo(({ file, children }) => {
    const [canRead] = usePermissions(['file.read']);
    const [canReadContents] = usePermissions(['file.read-content']);
    const directory = ServerContext.useStoreState((state) => state.files.directory);

    const match = useRouteMatch();

    return (file.isFile && (!file.isEditable() || !canReadContents)) || (!file.isFile && !canRead) ? (
        <div className="flex-1 min-w-0">{children}</div>
    ) : (
        <NavLink
            className="flex-1 min-w-0 text-hyper-primary hover:text-hyper-accent transition-colors no-underline"
            to={`${match.url}${file.isFile ? '/edit' : ''}#${encodePathSegments(join(directory, file.name))}`}
        >
            {children}
        </NavLink>
    );
}, isEqual);

const FileObjectRow = ({ file }: { file: FileObject }) => {
    const getFileIcon = () => {
        if (file.isFile) {
            if (file.isSymlink) return <Link size={18} className="text-hyper-accent" />;
            if (file.isArchiveType()) return <Archive size={18} className="text-hyper-accent" />;
            return <FileText size={18} className="text-hyper-accent" />;
        }
        return <Folder size={18} className="text-hyper-accent" />;
    };

    const formatDate = (date: Date) => {
        return Math.abs(differenceInHours(date, new Date())) > 48
            ? format(date, 'MMM do, yyyy h:mma')
            : formatDistanceToNow(date, { addSuffix: true });
    };

    return (
        <motion.div
            className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-4 mb-2 transition-colors duration-200 hover:border-hyper-accent hover:shadow-lg hover:shadow-hyper-accent/10 relative group"
            key={file.name}
            initial={{ opacity: 1, scale: 1 }}
            whileHover={{ 
                scale: 1.02,
                transition: { duration: 0.2, ease: "easeOut" }
            }}
            whileTap={{ scale: 0.98 }}
            onContextMenu={(e) => {
                e.preventDefault();
                window.dispatchEvent(new CustomEvent(`pterodactyl:files:ctx:${file.key}`, { detail: e.clientX }));
            }}
        >
            <div className="flex items-center gap-4">
                {/* Checkbox */}
                <SelectFileCheckbox name={file.name} />
                
                {/* File Icon */}
                <div className="flex-shrink-0">
                    {getFileIcon()}
                </div>
                
                {/* File Details */}
                <Clickable file={file}>
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between w-full gap-2">
                        {/* File Name */}
                        <div className="flex-1 min-w-0">
                            <h4 className="text-lg font-semibold text-hyper-primary truncate">{file.name}</h4>
                            <div className="flex items-center gap-4 text-sm text-hyper-accent mt-1">
                                {/* File Size */}
                                {file.isFile && (
                                    <div className="flex items-center gap-1">
                                        <HardDrive size={14} />
                                        <span>{bytesToString(file.size)}</span>
                                    </div>
                                )}
                                
                                {/* Modified Date */}
                                <div className="flex items-center gap-1">
                                    <Clock size={14} />
                                    <span title={file.modifiedAt.toString()}>
                                        {formatDate(file.modifiedAt)}
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        {/* File Type Badge */}
                        <div className="flex-shrink-0">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                file.isFile 
                                    ? 'bg-blue-400/10 text-blue-400 border border-blue-400/20' 
                                    : 'bg-yellow-400/10 text-yellow-400 border border-yellow-400/20'
                            }`}>
                                {file.isFile ? 'File' : 'Directory'}
                            </span>
                        </div>
                    </div>
                </Clickable>
                
                {/* Actions Menu */}
                <div className="flex-shrink-0">
                    <FileDropdownMenu file={file} />
                </div>
            </div>
        </motion.div>
    );
};

export default memo(FileObjectRow, (prevProps, nextProps) => {
    /* eslint-disable @typescript-eslint/no-unused-vars */
    const { isArchiveType, isEditable, ...prevFile } = prevProps.file;
    const { isArchiveType: nextIsArchiveType, isEditable: nextIsEditable, ...nextFile } = nextProps.file;
    /* eslint-enable @typescript-eslint/no-unused-vars */

    return isEqual(prevFile, nextFile);
});
