import React, { useEffect, useState, useMemo } from 'react';
import { httpErrorToHuman } from '@/api/http';
import { CSSTransition } from 'react-transition-group';
import Spinner from '@/components/elements/Spinner';
import AnimatedFileList from '@rolexdev/themes/hyperv1/components/server/files/AnimatedFileList';
import FileManagerBreadcrumbs from '@rolexdev/themes/hyperv1/components/server/files/FileManagerBreadcrumbs';
import NewDirectoryButton from '@rolexdev/themes/hyperv1/components/server/files/NewDirectoryButton';
import FileSearch from '@rolexdev/themes/hyperv1/components/server/files/FileSearch';
import FileSorter, { SortOption, SortDirection } from '@rolexdev/themes/hyperv1/components/server/files/FileSorter';
import { NavLink, useLocation } from 'react-router-dom';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import { ServerError } from '@/components/elements/ScreenBlock';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import useFileManagerSwr from '@rolexdev/themes/hyperv1/plugins/useFileManagerSwr';
import FileManagerStatus from '@rolexdev/themes/hyperv1/components/server/files/FileManagerStatus';
import MassActionsBar from '@rolexdev/themes/hyperv1/components/server/files/MassActionsBar';
import UploadButton from '@rolexdev/themes/hyperv1/components/server/files/UploadButton';
import ServerContentBlock from '@rolexdev/themes/hyperv1/components/elements/ServerContentBlock';
import { useStoreActions } from '@/state/hooks';
import ErrorBoundary from '@/components/elements/ErrorBoundary';
import { FileActionCheckbox } from '@rolexdev/themes/hyperv1/components/server/files/SelectFileCheckbox';
import { hashToPath } from '@/helpers';
import FlashMessageRender from '@/components/FlashMessageRender';
import { Folder, Trash2, FilePlus, AlertCircle } from 'lucide-react';
import { PageTransition, AnimatedContainer, AnimatedCard, AnimatedList, FadeTransition } from '@rolexdev/themes/hyperv1/components/elements/animations';

export default () => {
    const id = ServerContext.useStoreState((state) => state.server.data!.id);
    const { hash } = useLocation();
    const { data: files, error, mutate } = useFileManagerSwr();
    const directory = ServerContext.useStoreState((state) => state.files.directory);
    const clearFlashes = useStoreActions((actions) => actions.flashes.clearFlashes);
    const setDirectory = ServerContext.useStoreActions((actions) => actions.files.setDirectory);

    const setSelectedFiles = ServerContext.useStoreActions((actions) => actions.files.setSelectedFiles);
    const selectedFilesLength = ServerContext.useStoreState((state) => state.files.selectedFiles.length);
    const setOpenDropdownFileKey = ServerContext.useStoreActions((actions) => actions.files.setOpenDropdownFileKey);

    // Search and Sort state
    const [searchTerm, setSearchTerm] = useState('');
    const [sortBy, setSortBy] = useState<SortOption>('name');
    const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

    // Filtered and sorted files
    const filteredAndSortedFiles = useMemo(() => {
        if (!files) return [];
        
        // Filter files based on search term and exclude recycle-bin folder
        let filtered = files.filter(file => 
            file.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
            file.name !== 'recycle-bin'
        );

        // Sort files
        filtered = filtered.sort((a, b) => {
            let comparison = 0;
            
            switch (sortBy) {
                case 'name':
                    comparison = a.name.localeCompare(b.name);
                    break;
                case 'size':
                    comparison = (a.size || 0) - (b.size || 0);
                    break;
                case 'modified':
                    comparison = new Date(a.modifiedAt || 0).getTime() - new Date(b.modifiedAt || 0).getTime();
                    break;
                case 'type':
                    const aType = a.isFile ? 'file' : 'folder';
                    const bType = b.isFile ? 'file' : 'folder';
                    comparison = aType.localeCompare(bType);
                    if (comparison === 0) {
                        // If same type, sort by name
                        comparison = a.name.localeCompare(b.name);
                    }
                    break;
                default:
                    comparison = a.name.localeCompare(b.name);
            }
            
            // Apply sort direction
            return sortDirection === 'desc' ? -comparison : comparison;
        });

        // Always show folders first unless sorting by type
        if (sortBy !== 'type') {
            filtered = filtered.sort((a, b) => {
                if (a.isFile === b.isFile) return 0;
                return a.isFile ? 1 : -1;
            });
        }

        return filtered;
    }, [files, searchTerm, sortBy, sortDirection]);

    // Handle sort change
    const handleSortChange = (newSortBy: SortOption, newDirection: SortDirection) => {
        setSortBy(newSortBy);
        setSortDirection(newDirection);
    };

    useEffect(() => {
        clearFlashes('files');
        setSelectedFiles([]);
        setOpenDropdownFileKey(null); // Close any open dropdowns when directory changes
        setSearchTerm(''); // Clear search when directory changes
        setDirectory(hashToPath(hash));
    }, [hash]);

    useEffect(() => {
        mutate();
    }, [directory]);

    // Close dropdowns on scroll to prevent positioning issues
    useEffect(() => {
        const handleScroll = () => {
            setOpenDropdownFileKey(null);
        };

        window.addEventListener('scroll', handleScroll, true);
        return () => window.removeEventListener('scroll', handleScroll, true);
    }, [setOpenDropdownFileKey]);

    const onSelectAllClick = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSelectedFiles(e.currentTarget.checked ? filteredAndSortedFiles?.map((file) => file.name) || [] : []);
    };

    if (error) {
        return <ServerError message={httpErrorToHuman(error)} onRetry={() => mutate()} />;
    }

    return (
        <ServerContentBlock title={'File Manager'} showFlashKey={'files'}>
            <PageTransition>
                {/* Header Section */}
                <AnimatedCard delay={0.1} variant="default" className="mb-6">
                    <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                            <Folder size={20} className="text-hyper-accent" />
                        </div>
                        <div>
                            <h2 className="text-lg font-bold text-hyper-primary">File Manager</h2>
                            <p className="text-sm text-hyper-muted-foreground">
                                Browse, edit, and manage your server files
                            </p>
                        </div>
                    </div>
                    
                    <FlashMessageRender byKey={'files'} className="mb-4" />
                </AnimatedCard>

                <AnimatedCard delay={0.2} variant="hover" className='rounded-lg bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary p-6 mb-6 gap-6 flex flex-col'>
                    <ErrorBoundary>
                        {/* Search and Sort Bar */}
                        <AnimatedContainer variant="stagger" staggerChildren={0.1} className="flex flex-col sm:flex-row items-start sm:items-center gap-4 justify-between">
                            <FileSearch 
                                searchTerm={searchTerm}
                                onSearchChange={setSearchTerm}
                            />
                            <div className='w-full md:w-auto'>
                            <FileSorter 
                                sortBy={sortBy}
                                sortDirection={sortDirection}
                                onSortChange={handleSortChange}
                            />
                            </div>
                        </AnimatedContainer>
                        <Can action={'file.create'}>
                            {/* Desktop Layout: Left-aligned action buttons, right-aligned recycle bin */}
                            <AnimatedContainer variant="stagger" staggerChildren={0.05} className="hidden lg:flex items-center justify-between gap-4">
                                <div className='flex items-center gap-3'>
                                    <FileManagerStatus />
                                    <UploadButton />
                                    <NewDirectoryButton />
                                    <NavLink to={`/server/${id}/files/new${window.location.hash}`}>
                                        <Button size="small" className="flex items-center gap-2 w-max">
                                            <FilePlus size={16} />
                                            New File
                                        </Button>
                                    </NavLink>
                                </div>
                                <div>
                                    <NavLink to={`/server/${id}/files/recycle-bin`}>
                                        <Button size="small" color='red' className="flex items-center gap-2 w-max">
                                            <Trash2 size={16} />
                                            Recycle Bin
                                        </Button>
                                    </NavLink>
                                </div>
                            </AnimatedContainer>

                            {/* Mobile/Tablet Layout: Adaptive grid based on available space */}
                            <AnimatedContainer variant="stagger" staggerChildren={0.05} className="lg:hidden">
                                <div className="grid gap-3" style={{ gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))' }}>
                                    <FileManagerStatus />
                                    <UploadButton />
                                    <div className="w-full" style={{ minWidth: 'max-content' }}>
                                        <NewDirectoryButton className="w-full" />
                                    </div>
                                    <NavLink to={`/server/${id}/files/new${window.location.hash}`}>
                                        <Button size="small" className="flex items-center gap-2 w-full" style={{ minWidth: 'max-content' }}>
                                            <FilePlus size={16} />
                                            <span>New File</span>
                                        </Button>
                                    </NavLink>
                                    <NavLink to={`/server/${id}/files/recycle-bin`}>
                                        <Button size="small" color='red' className="flex items-center gap-2 w-full" style={{ minWidth: 'max-content' }}>
                                            <Trash2 size={16} />
                                            <span>Recycle Bin</span>
                                        </Button>
                                    </NavLink>
                                </div>
                            </AnimatedContainer>
                        </Can>
                        {/* Breadcrumbs*/}
                        <AnimatedCard delay={0.15} variant="default" className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
                            <div className="flex items-center gap-4 flex-1 min-w-0 w-full">
                                <FileActionCheckbox
                                    type={'checkbox'}
                                    checked={selectedFilesLength === (filteredAndSortedFiles?.length === 0 ? -1 : filteredAndSortedFiles?.length)}
                                    onChange={onSelectAllClick}
                                />
                                <FileManagerBreadcrumbs />
                            </div>
                        </AnimatedCard>
                    </ErrorBoundary>
                </AnimatedCard>

                {/* File Content */}
                {!files ? (
                    <Spinner size={'large'} centered />
                ) : (
                    <>
                        {!filteredAndSortedFiles.length ? (
                            <AnimatedCard delay={0.3} variant="default" className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-8 text-center w-full max-w-md mx-auto">
                                <div className="w-16 h-16 rounded-lg bg-hyper-primary-10 flex items-center justify-center mx-auto mb-4">
                                    <Folder size={32} className="text-hyper-accent" />
                                </div>
                                {searchTerm ? (
                                    <>
                                        <h3 className="text-lg font-semibold text-hyper-primary mb-2">No Results Found</h3>
                                        <p className="text-sm text-hyper-accent mb-6">
                                            No files or folders match your search "{searchTerm}".
                                        </p>
                                        <button
                                            onClick={() => setSearchTerm('')}
                                            className="px-4 py-2 bg-hyper-accent text-white rounded-lg hover:bg-opacity-80 transition-all duration-200"
                                        >
                                            Clear Search
                                        </button>
                                    </>
                                ) : (
                                    <>
                                        <h3 className="text-lg font-semibold text-hyper-primary mb-2">Directory is Empty</h3>
                                        <p className="text-sm text-hyper-accent mb-6">
                                            This directory doesn't contain any files or folders yet.
                                        </p>
                                        <Can action={'file.create'}>
                                            <div className="flex flex-col sm:flex-row gap-3 justify-center">
                                                <UploadButton />
                                                <NewDirectoryButton />
                                            </div>
                                        </Can>
                                    </>
                                )}
                            </AnimatedCard>
                        ) : (
                            <FadeTransition show={true}>
                                <div>
                                    {files.length > 250 && (
                                        <AnimatedCard delay={0.3} variant="default" className="bg-hyper-glass border border-hyper-accent rounded-lg p-4 mb-4">
                                            <div className="flex items-start gap-3">
                                                <AlertCircle size={20} className="text-hyper-accent flex-shrink-0 mt-0.5" />
                                                <div className="text-sm text-hyper-accent">
                                                    <p className="font-medium mb-1">Large Directory</p>
                                                    <p>This directory contains more than 250 files. Only the first 250 items are displayed for performance reasons.</p>
                                                </div>
                                            </div>
                                        </AnimatedCard>
                                    )}
                                    
                                    {/* Search Results Info */}
                                    {searchTerm && (
                                        <AnimatedCard delay={0.35} variant="default" className="bg-hyper-primary-10 border border-hyper-primary rounded-lg p-3 mb-4">
                                            <p className="text-sm text-hyper-primary">
                                                Showing {filteredAndSortedFiles.length} of {files.length} items matching "{searchTerm}"
                                            </p>
                                        </AnimatedCard>
                                    )}
                                    
                                    {/* File List */}
                                    <AnimatedCard delay={0.4} variant="default">
                                        <AnimatedFileList 
                                            files={filteredAndSortedFiles.slice(0, 250)}
                                            enableAnimations={filteredAndSortedFiles.length <= 100}
                                        />
                                        
                                        <MassActionsBar />
                                    </AnimatedCard>
                                </div>
                            </FadeTransition>
                        )}
                    </>
                )}
            </PageTransition>
        </ServerContentBlock>
    );
};
