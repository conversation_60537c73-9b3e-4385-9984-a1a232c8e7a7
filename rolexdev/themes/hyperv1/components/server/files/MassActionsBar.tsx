import React, { useEffect, useState } from 'react';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import Fade from '@/components/elements/Fade';
import SpinnerOverlay from '@/components/elements/SpinnerOverlay';
import useFileManagerSwr from '@rolexdev/themes/hyperv1/plugins/useFileManagerSwr';
import useFlash from '@/plugins/useFlash';
import compressFiles from '@/api/server/files/compressFiles';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import deleteFiles from '@rolexdev/themes/hyperv1/api/server/files/deleteFiles';
import { moveToRecycleBin } from '@rolexdev/themes/hyperv1/api/server/files/recycleBin';
import RenameFileModal from '@rolexdev/themes/hyperv1/components/server/files/RenameFileModal';
import Portal from '@/components/elements/Portal';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import { Move, Archive, Trash2, X, RotateCcw } from 'lucide-react';

const MassActionsBar = () => {
    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);

    const { mutate } = useFileManagerSwr();
    const { clearFlashes, clearAndAddHttpError } = useFlash();
    const [loading, setLoading] = useState(false);
    const [loadingMessage, setLoadingMessage] = useState('');
    const [showConfirm, setShowConfirm] = useState(false);
    const [showPermanentDelete, setShowPermanentDelete] = useState(false);
    const [showMove, setShowMove] = useState(false);
    const directory = ServerContext.useStoreState((state) => state.files.directory);

    const selectedFiles = ServerContext.useStoreState((state) => state.files.selectedFiles);
    const setSelectedFiles = ServerContext.useStoreActions((actions) => actions.files.setSelectedFiles);

    useEffect(() => {
        if (!loading) setLoadingMessage('');
    }, [loading]);

    const onClickCompress = () => {
        setLoading(true);
        clearFlashes('files');
        setLoadingMessage('Archiving files...');

        compressFiles(uuid, directory, selectedFiles)
            .then(() => mutate())
            .then(() => setSelectedFiles([]))
            .catch((error) => clearAndAddHttpError({ key: 'files', error }))
            .then(() => setLoading(false));
    };

    const onClickConfirmDeletion = () => {
        setLoading(true);
        setShowConfirm(false);
        clearFlashes('files');
        setLoadingMessage('Moving files to recycle bin...');

        moveToRecycleBin(uuid, directory, selectedFiles)
            .then(() => {
                // Delay to allow exit animation to play
                setTimeout(() => {
                    mutate((files) => files.filter((f) => selectedFiles.indexOf(f.name) < 0), false);
                }, 100);
                setSelectedFiles([]);
            })
            .catch((error) => {
                mutate();
                clearAndAddHttpError({ key: 'files', error });
            })
            .then(() => setLoading(false));
    };

    const onClickPermanentDeletion = () => {
        setLoading(true);
        setShowPermanentDelete(false);
        clearFlashes('files');
        setLoadingMessage('Permanently deleting files...');

        deleteFiles(uuid, directory, selectedFiles, true) // Pass true for permanent deletion
            .then(() => {
                // Delay to allow exit animation to play
                setTimeout(() => {
                    mutate((files) => files.filter((f) => selectedFiles.indexOf(f.name) < 0), false);
                }, 100);
                setSelectedFiles([]);
            })
            .catch((error) => {
                mutate();
                clearAndAddHttpError({ key: 'files', error });
            })
            .then(() => setLoading(false));
    };

    return (
        <>
            <SpinnerOverlay visible={loading} size={'large'} fixed>
                {loadingMessage}
            </SpinnerOverlay>
            
            {/* Delete to Recycle Bin Confirmation Dialog */}
            <Dialog
                open={showConfirm}
                onClose={() => setShowConfirm(false)}
                title="Move to Recycle Bin"
            >
                <div className="space-y-4">
                    <div className="bg-hyper-glass border border-orange-400/20 rounded-lg p-4">
                        <div className="flex items-start gap-3">
                            <Trash2 size={16} className="text-orange-400 flex-shrink-0 mt-0.5" />
                            <div className="text-sm">
                                <p className="text-orange-400 font-medium mb-1">Move to Recycle Bin</p>
                                <p className="text-hyper-accent mb-3">
                                    Are you sure you want to move{' '}
                                    <span className="font-semibold text-hyper-primary">{selectedFiles.length} files</span>{' '}
                                    to the recycle bin? Files will be automatically deleted after 7 days.
                                </p>
                                
                                {/* File List */}
                                <div className="bg-hyper-background border border-hyper-primary rounded p-3 max-h-32 overflow-y-auto">
                                    <ul className="text-xs text-hyper-accent space-y-1">
                                        {selectedFiles.slice(0, 15).map((file) => (
                                            <li key={file} className="font-mono break-all">{file}</li>
                                        ))}
                                        {selectedFiles.length > 15 && (
                                            <li className="text-hyper-secondary italic">
                                                and {selectedFiles.length - 15} more files...
                                            </li>
                                        )}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <Dialog.Footer>
                    <Button 
                        size="small" 
                        isSecondary 
                        onClick={() => setShowConfirm(false)}
                        disabled={loading}
                    >
                        Cancel
                    </Button>
                    <Button 
                        size="small"
                        color="primary"
                        onClick={onClickConfirmDeletion}
                        disabled={loading}
                        isLoading={loading}
                    >
                        Move to Recycle Bin
                    </Button>
                    <Button 
                        size="small"
                        color="red"
                        onClick={() => {
                            setShowConfirm(false);
                            setShowPermanentDelete(true);
                        }}
                        disabled={loading}
                        className="flex items-center gap-1"
                    >
                        Delete Forever
                    </Button>
                </Dialog.Footer>
            </Dialog>

            {/* Permanent Delete Confirmation Dialog */}
            <Dialog
                open={showPermanentDelete}
                onClose={() => setShowPermanentDelete(false)}
                title="Permanently Delete Files"
            >
                <div className="space-y-4">
                    <div className="bg-hyper-glass border border-red-400/20 rounded-lg p-4">
                        <div className="flex items-start gap-3">
                            <Trash2 size={16} className="text-red-400 flex-shrink-0 mt-0.5" />
                            <div className="text-sm">
                                <p className="text-red-400 font-medium mb-1">Permanent Deletion</p>
                                <p className="text-hyper-accent mb-2">
                                    Are you sure you want to permanently delete{' '}
                                    <span className="font-semibold text-hyper-primary">{selectedFiles.length} files</span>?
                                </p>
                                <p className="text-red-400 font-semibold mb-3">
                                    This action cannot be undone!
                                </p>
                                
                                {/* File List */}
                                <div className="bg-hyper-background border border-hyper-primary rounded p-3 max-h-32 overflow-y-auto">
                                    <ul className="text-xs text-hyper-accent space-y-1">
                                        {selectedFiles.slice(0, 15).map((file) => (
                                            <li key={file} className="font-mono break-all">{file}</li>
                                        ))}
                                        {selectedFiles.length > 15 && (
                                            <li className="text-hyper-secondary italic">
                                                and {selectedFiles.length - 15} more files...
                                            </li>
                                        )}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <Dialog.Footer>
                    <Button 
                        size="small" 
                        isSecondary 
                        onClick={() => setShowPermanentDelete(false)}
                        disabled={loading}
                    >
                        Cancel
                    </Button>
                    <Button 
                        size="small"
                        color="red"
                        onClick={onClickPermanentDeletion}
                        disabled={loading}
                        isLoading={loading}
                    >
                        Delete Forever
                    </Button>
                </Dialog.Footer>
            </Dialog>

            {/* Move Modal */}
            {showMove && (
                <RenameFileModal
                    files={selectedFiles}
                    visible
                    appear
                    useMoveTerminology
                    onDismissed={() => setShowMove(false)}
                />
            )}

            {/* Floating Action Bar */}
            <Portal>
                <div className="pointer-events-none fixed bottom-0 mb-6 flex justify-center w-full z-50">
                    <Fade timeout={75} in={selectedFiles.length > 0} unmountOnExit>
                        <div className="flex items-center gap-3 pointer-events-auto bg-hyper-sidebar border border-hyper-primary rounded-lg p-4 backdrop-blur-lg shadow-lg">
                            <div className="flex items-center gap-2 text-sm text-hyper-primary mr-3">
                                <span className="font-semibold">{selectedFiles.length}</span>
                                <span>file{selectedFiles.length !== 1 ? 's' : ''} selected</span>
                            </div>
                            
                            <Button 
                                size="small" 
                                isSecondary
                                onClick={() => setShowMove(true)}
                                className="flex items-center gap-2"
                            >
                                <Move size={16} />
                                Move
                            </Button>
                            
                            <Button 
                                size="small" 
                                isSecondary
                                onClick={onClickCompress}
                                className="flex items-center gap-2"
                            >
                                <Archive size={16} />
                                Archive
                            </Button>
                            
                            <Button 
                                size="small"
                                color="red"
                                onClick={() => setShowConfirm(true)}
                                className="flex items-center gap-2"
                            >
                                <Trash2 size={16} />
                                Delete
                            </Button>
                            
                            <button
                                onClick={() => setSelectedFiles([])}
                                className="w-8 h-8 flex items-center justify-center rounded-lg text-hyper-accent hover:bg-hyper-primary-10 hover:text-hyper-primary transition-colors ml-2"
                                title="Clear selection"
                            >
                                <X size={16} />
                            </button>
                        </div>
                    </Fade>
                </div>
            </Portal>
        </>
    );
};

export default MassActionsBar;
