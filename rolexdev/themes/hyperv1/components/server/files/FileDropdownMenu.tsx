import React, { memo, useRef, useState, useEffect, useCallback } from 'react';
import { 
    MoreVertical, 
    Edit, 
    Move, 
    Key, 
    Copy, 
    Download, 
    Archive, 
    FolderOpen, 
    Trash2,
    ArchiveRestore
} from 'lucide-react';
import RenameFileModal from '@rolexdev/themes/hyperv1/components/server/files/RenameFileModal';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { join } from 'path';
import deleteFiles from '@rolexdev/themes/hyperv1/api/server/files/deleteFiles';
import { moveToRecycleBin } from '@rolexdev/themes/hyperv1/api/server/files/recycleBin';
import SpinnerOverlay from '@/components/elements/SpinnerOverlay';
import copyFile from '@/api/server/files/copyFile';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import getFileDownloadUrl from '@/api/server/files/getFileDownloadUrl';
import useFlash from '@/plugins/useFlash';
import { FileObject } from '@/api/server/files/loadDirectory';
import useFileManagerSwr from '@rolexdev/themes/hyperv1/plugins/useFileManagerSwr';
import useEventListener from '@/plugins/useEventListener';
import compressFiles from '@/api/server/files/compressFiles';
import decompressFiles from '@/api/server/files/decompressFiles';
import isEqual from 'react-fast-compare';
import ChmodFileModal from '@rolexdev/themes/hyperv1/components/server/files/ChmodFileModal';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { createPortal } from 'react-dom';

type ModalType = 'rename' | 'move' | 'chmod';

const FileDropdownMenu = ({ file }: { file: FileObject }) => {
    const [showSpinner, setShowSpinner] = useState(false);
    const [modal, setModal] = useState<ModalType | null>(null);
    const [showDeleteChoice, setShowDeleteChoice] = useState(false);
    const [showPermanentDeleteConfirm, setShowPermanentDeleteConfirm] = useState(false);
    const [dropdownPosition, setDropdownPosition] = useState<{ top: number; left: number } | null>(null);
    const buttonRef = useRef<HTMLButtonElement>(null);
    const mountedRef = useRef(true);

    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const { mutate } = useFileManagerSwr();
    const { clearAndAddHttpError, clearFlashes } = useFlash();
    const directory = ServerContext.useStoreState((state) => state.files.directory);
    
    // Use global dropdown state
    const openDropdownFileKey = ServerContext.useStoreState((state) => state.files.openDropdownFileKey);
    const setOpenDropdownFileKey = ServerContext.useStoreActions((actions) => actions.files.setOpenDropdownFileKey);
    const isDropdownOpen = openDropdownFileKey === file.key;

    const closeDropdown = useCallback(() => {
        setOpenDropdownFileKey(null);
    }, [setOpenDropdownFileKey]);

    useEventListener(`pterodactyl:files:ctx:${file.key}`, (e: CustomEvent) => {
        // If clicking on the same file, toggle. Otherwise, always open
        if (isDropdownOpen) {
            setOpenDropdownFileKey(null);
        } else {
            setOpenDropdownFileKey(file.key);
        }
    });

    // Update dropdown position when it opens and on scroll
    useEffect(() => {
        if (!isDropdownOpen || !buttonRef.current) {
            setDropdownPosition(null);
            return;
        }

        const updatePosition = () => {
            if (!buttonRef.current) return;
            
            const rect = buttonRef.current.getBoundingClientRect();
            const dropdownWidth = 180; // min-width of dropdown
            const dropdownHeight = 300; // estimated max height
            const padding = 8; // padding from viewport edges
            
            // Calculate initial position
            let top = rect.bottom + 4;
            let left = rect.right - dropdownWidth;
            
            // Adjust horizontal position if it goes off screen
            if (left < padding) {
                left = rect.left; // Align to left edge of button
            }
            if (left + dropdownWidth > window.innerWidth - padding) {
                left = window.innerWidth - dropdownWidth - padding;
            }
            
            // Adjust vertical position if it goes off screen
            if (top + dropdownHeight > window.innerHeight - padding) {
                // Show above the button instead
                top = rect.top - dropdownHeight - 4;
                // If still off screen, position at the top with some padding
                if (top < padding) {
                    top = padding;
                }
            }
            
            setDropdownPosition({ top, left });
        };

        updatePosition();

        // Update position on scroll
        const handleScroll = () => {
            if (isDropdownOpen) {
                updatePosition();
            }
        };

        window.addEventListener('scroll', handleScroll, true); // Use capture to catch all scroll events
        window.addEventListener('resize', handleScroll);

        return () => {
            window.removeEventListener('scroll', handleScroll, true);
            window.removeEventListener('resize', handleScroll);
        };
    }, [isDropdownOpen]);

    // Handle click outside to close dropdown and close on scroll
    useEffect(() => {
        if (!isDropdownOpen) return;

        const handleClickOutside = (event: MouseEvent) => {
            if (buttonRef.current && !buttonRef.current.contains(event.target as Node)) {
                // Check if click is outside the dropdown portal too
                const dropdownElement = document.querySelector('[data-dropdown-menu="true"]');
                if (!dropdownElement || !dropdownElement.contains(event.target as Node)) {
                    closeDropdown();
                }
            }
        };

        const handleEscape = (event: KeyboardEvent) => {
            if (event.key === 'Escape') {
                closeDropdown();
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('keydown', handleEscape);
        
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleEscape);
        };
    }, [isDropdownOpen, closeDropdown]);

    // Cleanup effect to prevent memory leaks
    useEffect(() => {
        return () => {
            mountedRef.current = false;
        };
    }, []);

    const doRecycleBin = () => {
        setShowSpinner(true);
        clearFlashes('files');

        moveToRecycleBin(uuid, directory, [file.name])
            .then(() => {
                if (mountedRef.current) {
                    // Delay to allow exit animation to play
                    setTimeout(() => {
                        mutate((files) => files.filter((f) => f.key !== file.key), false);
                    }, 100);
                }
            })
            .catch((error) => {
                if (mountedRef.current) {
                    mutate();
                    clearAndAddHttpError({ key: 'files', error });
                }
            })
            .finally(() => {
                if (mountedRef.current) {
                    setShowSpinner(false);
                }
            });
    };

    const doDeletion = () => {
        setShowSpinner(true);
        clearFlashes('files');

        deleteFiles(uuid, directory, [file.name], true) // Pass true for permanent deletion
            .then(() => {
                if (mountedRef.current) {
                    // Delay to allow exit animation to play
                    setTimeout(() => {
                        mutate((files) => files.filter((f) => f.key !== file.key), false);
                    }, 100);
                }
            })
            .catch((error) => {
                if (mountedRef.current) {
                    mutate();
                    clearAndAddHttpError({ key: 'files', error });
                }
            })
            .finally(() => {
                if (mountedRef.current) {
                    setShowSpinner(false);
                }
            });
    };

    const doCopy = () => {
        setShowSpinner(true);
        clearFlashes('files');

        copyFile(uuid, join(directory, file.name))
            .then(() => {
                if (mountedRef.current) {
                    mutate();
                }
            })
            .catch((error) => {
                if (mountedRef.current) {
                    clearAndAddHttpError({ key: 'files', error });
                }
            })
            .then(() => {
                if (mountedRef.current) {
                    setShowSpinner(false);
                }
            });
    };

    const doDownload = () => {
        setShowSpinner(true);
        clearFlashes('files');

        getFileDownloadUrl(uuid, join(directory, file.name))
            .then((url) => {
                // @ts-expect-error this is valid
                window.location = url;
            })
            .catch((error) => {
                if (mountedRef.current) {
                    clearAndAddHttpError({ key: 'files', error });
                }
            })
            .then(() => {
                if (mountedRef.current) {
                    setShowSpinner(false);
                }
            });
    };

    const doArchive = () => {
        setShowSpinner(true);
        clearFlashes('files');

        compressFiles(uuid, directory, [file.name])
            .then(() => {
                if (mountedRef.current) {
                    mutate();
                }
            })
            .catch((error) => {
                if (mountedRef.current) {
                    clearAndAddHttpError({ key: 'files', error });
                }
            })
            .then(() => {
                if (mountedRef.current) {
                    setShowSpinner(false);
                }
            });
    };

    const doUnarchive = () => {
        setShowSpinner(true);
        clearFlashes('files');

        decompressFiles(uuid, directory, file.name)
            .then(() => {
                if (mountedRef.current) {
                    mutate();
                }
            })
            .catch((error) => {
                if (mountedRef.current) {
                    clearAndAddHttpError({ key: 'files', error });
                }
            })
            .then(() => {
                if (mountedRef.current) {
                    setShowSpinner(false);
                }
            });
    };

    return (
        <>
            {/* Delete Choice Dialog - Similar to Mass Actions */}
            <Dialog
                open={showDeleteChoice}
                onClose={() => setShowDeleteChoice(false)}
                title={`Delete ${file.isFile ? 'File' : 'Directory'}`}
            >
                <div className="space-y-4">
                    <div className="bg-hyper-glass border border-orange-400/20 rounded-lg p-4">
                        <div className="flex items-start gap-3">
                            <Trash2 size={16} className="text-orange-400 flex-shrink-0 mt-0.5" />
                            <div className="text-sm">
                                <p className="text-orange-400 font-medium mb-1">Choose Deletion Type</p>
                                <p className="text-hyper-accent mb-3">
                                    How would you like to delete{' '}
                                    <span className="font-semibold text-hyper-primary">{file.name}</span>?
                                </p>
                                
                                <div className="grid grid-cols-1 gap-3">
                                    {/* Move to Recycle Bin Option */}
                                    <div className="bg-hyper-background border border-blue-400/20 rounded p-3">
                                        <div className="flex items-start gap-3">
                                            <ArchiveRestore size={14} className="text-blue-400 flex-shrink-0 mt-0.5" />
                                            <div>
                                                <p className="text-blue-400 font-medium text-sm">Move to Recycle Bin</p>
                                                <p className="text-xs text-hyper-accent mt-1">
                                                    File can be restored within 7 days. Automatically deleted after that.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    {/* Permanent Delete Option */}
                                    <div className="bg-hyper-background border border-red-400/20 rounded p-3">
                                        <div className="flex items-start gap-3">
                                            <Trash2 size={14} className="text-red-400 flex-shrink-0 mt-0.5" />
                                            <div>
                                                <p className="text-red-400 font-medium text-sm">Delete Forever</p>
                                                <p className="text-xs text-hyper-accent mt-1">
                                                    File will be permanently deleted. This action cannot be undone.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <Dialog.Footer>
                    <Button 
                        size="small" 
                        isSecondary 
                        onClick={() => setShowDeleteChoice(false)}
                        disabled={showSpinner}
                    >
                        Cancel
                    </Button>
                    <Button 
                        size="small"
                        color="primary"
                        onClick={() => {
                            setShowDeleteChoice(false);
                            doRecycleBin();
                        }}
                        disabled={showSpinner}
                        isLoading={showSpinner}
                        className="flex items-center gap-1"
                    >
                        <ArchiveRestore size={14} />
                        Move to Recycle Bin
                    </Button>
                    <Button 
                        size="small"
                        color="red"
                        onClick={() => {
                            setShowDeleteChoice(false);
                            setShowPermanentDeleteConfirm(true);
                        }}
                        disabled={showSpinner}
                        className="flex items-center gap-1"
                    >
                        <Trash2 size={14} />
                        Delete Forever
                    </Button>
                </Dialog.Footer>
            </Dialog>

            {/* Permanent Delete Confirmation Dialog */}
            <Dialog
                open={showPermanentDeleteConfirm}
                onClose={() => setShowPermanentDeleteConfirm(false)}
                title={`Permanently Delete ${file.isFile ? 'File' : 'Directory'}`}
            >
                <div className="space-y-4">
                    <div className="bg-hyper-glass border border-red-400/20 rounded-lg p-4">
                        <div className="flex items-start gap-3">
                            <Trash2 size={16} className="text-red-400 flex-shrink-0 mt-0.5" />
                            <div className="text-sm">
                                <p className="text-red-400 font-medium mb-1">Permanent Deletion</p>
                                <p className="text-hyper-accent">
                                    You will not be able to recover the contents of{' '}
                                    <span className="font-semibold text-hyper-primary">{file.name}</span>{' '}
                                    once deleted. This action cannot be undone.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <Dialog.Footer>
                    <Button 
                        size="small" 
                        isSecondary 
                        onClick={() => setShowPermanentDeleteConfirm(false)}
                        disabled={showSpinner}
                    >
                        Cancel
                    </Button>
                    <Button 
                        size="small"
                        color="red"
                        onClick={() => {
                            setShowPermanentDeleteConfirm(false);
                            doDeletion();
                        }}
                        disabled={showSpinner}
                        isLoading={showSpinner}
                    >
                        Delete Permanently
                    </Button>
                </Dialog.Footer>
            </Dialog>

            {/* Modals */}
            {modal === 'chmod' && (
                <ChmodFileModal
                    visible
                    appear
                    files={[{ file: file.name, mode: file.modeBits }]}
                    onDismissed={() => setModal(null)}
                />
            )}
            {(modal === 'rename' || modal === 'move') && (
                <RenameFileModal
                    visible
                    appear
                    files={[file.name]}
                    useMoveTerminology={modal === 'move'}
                    onDismissed={() => setModal(null)}
                />
            )}

            {/* Dropdown Menu */}
            <div className="relative">
                <button
                    ref={buttonRef}
                    onClick={() => {
                        if (isDropdownOpen) {
                            closeDropdown();
                        } else {
                            setOpenDropdownFileKey(file.key);
                        }
                    }}
                    className="w-8 h-8 flex items-center justify-center rounded-lg text-hyper-accent hover:bg-hyper-primary-10 hover:text-hyper-primary transition-colors"
                >
                    <MoreVertical size={16} />
                </button>

                {/* Render dropdown in portal */}
                {isDropdownOpen && dropdownPosition && createPortal(
                    <div 
                        data-dropdown-menu="true"
                        className="fixed bg-hyper-sidebar border border-hyper-accent rounded-lg shadow-lg min-w-[180px] max-w-[200px] z-[9999] backdrop-blur-lg"
                        style={{
                            top: `${dropdownPosition.top}px`,
                            left: `${dropdownPosition.left}px`,
                        }}
                        tabIndex={0}
                    >
                        <div className="py-1">
                            {/* File operations */}
                            <Can action={'file.update'}>
                                <div 
                                    className="px-3 py-2 cursor-pointer hover:bg-hyper-primary-10 text-hyper-primary flex items-center transition-colors"
                                    onMouseDown={(e) => {
                                        e.preventDefault();
                                        setModal('rename');
                                        closeDropdown();
                                    }}
                                >
                                    <Edit size={14} />
                                    <span className="ml-2">Rename</span>
                                </div>
                                <div 
                                    className="px-3 py-2 cursor-pointer hover:bg-hyper-primary-10 text-hyper-primary flex items-center transition-colors"
                                    onMouseDown={(e) => {
                                        e.preventDefault();
                                        setModal('move');
                                        closeDropdown();
                                    }}
                                >
                                    <Move size={14} />
                                    <span className="ml-2">Move</span>
                                </div>
                                <div 
                                    className="px-3 py-2 cursor-pointer hover:bg-hyper-primary-10 text-hyper-primary flex items-center transition-colors"
                                    onMouseDown={(e) => {
                                        e.preventDefault();
                                        setModal('chmod');
                                        closeDropdown();
                                    }}
                                >
                                    <Key size={14} />
                                    <span className="ml-2">Permissions</span>
                                </div>
                            </Can>

                            {/* Copy for files */}
                            {file.isFile && (
                                <Can action={'file.create'}>
                                    <div 
                                        className="px-3 py-2 cursor-pointer hover:bg-hyper-primary-10 text-hyper-primary flex items-center transition-colors"
                                        onMouseDown={(e) => {
                                            e.preventDefault();
                                            doCopy();
                                            closeDropdown();
                                        }}
                                    >
                                        <Copy size={14} />
                                        <span className="ml-2">Copy</span>
                                    </div>
                                </Can>
                            )}

                            {/* Archive operations */}
                            {file.isArchiveType() ? (
                                <Can action={'file.create'}>
                                    <div 
                                        className="px-3 py-2 cursor-pointer hover:bg-hyper-primary-10 text-hyper-primary flex items-center transition-colors"
                                        onMouseDown={(e) => {
                                            e.preventDefault();
                                            doUnarchive();
                                            closeDropdown();
                                        }}
                                    >
                                        <FolderOpen size={14} />
                                        <span className="ml-2">Extract</span>
                                    </div>
                                </Can>
                            ) : (
                                <Can action={'file.archive'}>
                                    <div 
                                        className="px-3 py-2 cursor-pointer hover:bg-hyper-primary-10 text-hyper-primary flex items-center transition-colors"
                                        onMouseDown={(e) => {
                                            e.preventDefault();
                                            doArchive();
                                            closeDropdown();
                                        }}
                                    >
                                        <Archive size={14} />
                                        <span className="ml-2">Archive</span>
                                    </div>
                                </Can>
                            )}

                            {/* Download for files */}
                            {file.isFile && (
                                <div 
                                    className="px-3 py-2 cursor-pointer hover:bg-hyper-primary-10 text-hyper-primary flex items-center transition-colors"
                                    onMouseDown={(e) => {
                                        e.preventDefault();
                                        doDownload();
                                        closeDropdown();
                                    }}
                                >
                                    <Download size={14} />
                                    <span className="ml-2">Download</span>
                                </div>
                            )}

                            {/* Delete Option */}
                            <Can action={'file.delete'}>
                                <div className="border-t border-hyper-primary my-1"></div>
                                <div 
                                    className="px-3 py-2 cursor-pointer hover:bg-red-400/10 hover:text-red-400 text-red-500 flex items-center transition-colors"
                                    onMouseDown={(e) => {
                                        e.preventDefault();
                                        setShowDeleteChoice(true);
                                        closeDropdown();
                                    }}
                                >
                                    <Trash2 size={14} />
                                    <span className="ml-2">Delete</span>
                                </div>
                            </Can>
                        </div>
                    </div>,
                    document.body
                )}
            </div>

            <SpinnerOverlay visible={showSpinner} fixed size={'large'} />
        </>
    );
};

export default memo(FileDropdownMenu, isEqual);
