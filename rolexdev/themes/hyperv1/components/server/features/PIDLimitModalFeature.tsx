import React, { useEffect, useState } from 'react';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import FlashMessageRender from '@/components/FlashMessageRender';
import useFlash from '@/plugins/useFlash';
import { SocketEvent } from '@/components/server/events';
import { useStoreState } from 'easy-peasy';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import { AlertTriangle, Settings, X, Users } from 'lucide-react';

const PIDLimitModalFeature = () => {
    const [visible, setVisible] = useState(false);
    const [loading] = useState(false);

    const status = ServerContext.useStoreState((state) => state.status.value);
    const { clearFlashes } = useFlash();
    const { connected, instance } = ServerContext.useStoreState((state) => state.socket);
    const isAdmin = useStoreState((state) => state.user.data!.rootAdmin);

    useEffect(() => {
        if (!connected || !instance || status === 'running') return;

        const errors = [
            'pthread_create failed',
            'failed to create thread',
            'unable to create thread',
            'unable to create native thread',
            'unable to create new native thread',
            'exception in thread "craft async scheduler management thread"',
        ];

        const listener = (line: string) => {
            if (errors.some((p) => line.toLowerCase().includes(p))) {
                setVisible(true);
            }
        };

        instance.addListener(SocketEvent.CONSOLE_OUTPUT, listener);

        return () => {
            instance.removeListener(SocketEvent.CONSOLE_OUTPUT, listener);
        };
    }, [connected, instance, status]);

    useEffect(() => {
        clearFlashes('feature:pidLimit');
    }, []);

    return (
        <Dialog
            open={visible}
            onClose={() => setVisible(false)}
            title={isAdmin ? "Memory or Process Limit Reached" : "Possible Resource Limit Reached"}
            description={isAdmin ? "This server has reached the maximum process or memory limit." : "This server is attempting to use more resources than allocated."}
            preventExternalClose={true}
        >
            <Dialog.Icon type="warning" />
            
            <div className="space-y-6">
                <FlashMessageRender key="feature:pidLimit" className="mb-4" />
                
                <div className="bg-hyper-background border border-hyper-primary rounded-lg p-4">
                    <div className="flex items-start gap-3">
                        <AlertTriangle size={20} className="text-yellow-500 flex-shrink-0 mt-0.5" />
                        <div className="space-y-3">
                            {isAdmin ? (
                                <>
                                    <h3 className="text-lg font-semibold text-hyper-primary flex items-center gap-2">
                                        <Settings size={18} />
                                        System Administrator Information
                                    </h3>
                                    <div className="space-y-3 text-sm text-hyper-secondary">
                                        <p>This server has reached the maximum process or memory limit.</p>
                                        <div className="bg-hyper-card border border-hyper-accent/20 rounded-lg p-3">
                                            <p className="mb-2 font-medium text-hyper-foreground">Configuration Solution:</p>
                                            <p>
                                                Increasing <code className="bg-hyper-background px-2 py-1 rounded text-hyper-accent font-mono text-xs">container_pid_limit</code> in the wings
                                                configuration file <code className="bg-hyper-background px-2 py-1 rounded text-hyper-accent font-mono text-xs">config.yml</code> might help resolve
                                                this issue.
                                            </p>
                                        </div>
                                        <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3">
                                            <p className="text-yellow-600 font-medium text-xs">
                                                <strong>Note:</strong> Wings must be restarted for the configuration file changes to take effect
                                            </p>
                                        </div>
                                    </div>
                                </>
                            ) : (
                                <>
                                    <h3 className="text-lg font-semibold text-hyper-primary flex items-center gap-2">
                                        <Users size={18} />
                                        Resource Limit Issue
                                    </h3>
                                    <div className="space-y-3 text-sm text-hyper-secondary">
                                        <p>
                                            This server is attempting to use more resources than allocated. Please contact the administrator
                                            and provide them with the error below.
                                        </p>
                                        <div className="bg-hyper-card border border-hyper-accent/20 rounded-lg p-3">
                                            <p className="mb-2 font-medium text-hyper-foreground text-xs">Error Details:</p>
                                            <code className="bg-hyper-background px-2 py-1 rounded text-hyper-accent font-mono text-xs block">
                                                pthread_create failed, Possibly out of memory or process/resource limits reached
                                            </code>
                                        </div>
                                    </div>
                                </>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            <Dialog.Footer>
                <Button 
                    onClick={() => setVisible(false)}
                    className="flex items-center gap-2"
                    size="small"
                    disabled={loading}
                >
                    <X size={16} />
                    Close
                </Button>
            </Dialog.Footer>
        </Dialog>
    );
};

export default PIDLimitModalFeature;
