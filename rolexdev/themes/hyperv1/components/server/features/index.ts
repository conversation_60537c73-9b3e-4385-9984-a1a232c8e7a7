import { ComponentType, lazy } from 'react';

/**
 * Custom features should be registered here as lazy components so that they do
 * not impact the generated JS bundle size. They will be automatically loaded in
 * whenever they are actually loaded for the client (which may be never, depending
 * on the feature and the egg).
 */
const features: Record<string, ComponentType> = {
    eula: lazy(() => import('@rolexdev/themes/hyperv1/components/server/features/eula/EulaModalFeature')),
    java_version: lazy(() => import('@rolexdev/themes/hyperv1/components/server/features/JavaVersionModalFeature')),
    gsl_token: lazy(() => import('@rolexdev/themes/hyperv1/components/server/features/GSLTokenModalFeature')),
    pid_limit: lazy(() => import('@rolexdev/themes/hyperv1/components/server/features/PIDLimitModalFeature')),
    steam_disk_space: lazy(() => import('@rolexdev/themes/hyperv1/components/server/features/SteamDiskSpaceFeature')),
};

export default features;
