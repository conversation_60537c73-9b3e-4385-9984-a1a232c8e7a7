import React, { useEffect, useState } from 'react';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import FlashMessageRender from '@/components/FlashMessageRender';
import useFlash from '@/plugins/useFlash';
import { SocketEvent, SocketRequest } from '@/components/server/events';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import updateStartupVariable from '@/api/server/updateStartupVariable';
import { Form, Formik } from 'formik';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import { AlertTriangle, Key, Save, ExternalLink } from 'lucide-react';

interface Values {
    gslToken: string;
}

const GSLTokenModalFeature = () => {
    const [visible, setVisible] = useState(false);
    const [loading, setLoading] = useState(false);

    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const status = ServerContext.useStoreState((state) => state.status.value);
    const { clearFlashes, clearAndAddHttpError } = useFlash();
    const { connected, instance } = ServerContext.useStoreState((state) => state.socket);

    useEffect(() => {
        if (!connected || !instance || status === 'running') return;

        const errors = ['(gsl token expired)', '(account not found)'];

        const listener = (line: string) => {
            if (errors.some((p) => line.toLowerCase().includes(p))) {
                setVisible(true);
            }
        };

        instance.addListener(SocketEvent.CONSOLE_OUTPUT, listener);

        return () => {
            instance.removeListener(SocketEvent.CONSOLE_OUTPUT, listener);
        };
    }, [connected, instance, status]);

    const updateGSLToken = (values: Values) => {
        setLoading(true);
        clearFlashes('feature:gslToken');

        updateStartupVariable(uuid, 'STEAM_ACC', values.gslToken)
            .then(() => {
                if (instance) {
                    instance.send(SocketRequest.SET_STATE, 'restart');
                }

                setLoading(false);
                setVisible(false);
            })
            .catch((error) => {
                console.error(error);
                clearAndAddHttpError({ key: 'feature:gslToken', error });
            })
            .then(() => setLoading(false));
    };

    useEffect(() => {
        clearFlashes('feature:gslToken');
    }, []);

    return (
        <Formik onSubmit={updateGSLToken} initialValues={{ gslToken: '' }}>
            {({ isSubmitting }) => (
                <Dialog
                    open={visible}
                    onClose={() => setVisible(false)}
                    title="Invalid GSL Token"
                    description="Your Gameserver Login Token (GSL token) is invalid or has expired."
                    preventExternalClose={true}
                >
                    <Dialog.Icon type="warning" />
                    
                    <Form className="space-y-6">
                        <FlashMessageRender key="feature:gslToken" className="mb-4" />
                        
                        <div className="bg-hyper-background border border-hyper-primary rounded-lg p-4">
                            <div className="flex items-start gap-3">
                                <AlertTriangle size={20} className="text-yellow-500 flex-shrink-0 mt-0.5" />
                                <div className="space-y-3">
                                    <h3 className="text-lg font-semibold text-hyper-primary">Token Issues Detected</h3>
                                    <div className="space-y-2 text-sm text-hyper-secondary">
                                        <p>
                                            It seems like your Gameserver Login Token (GSL token) is invalid or has expired.
                                        </p>
                                        <p>
                                            You can either generate a new one and enter it below or leave the field blank to remove it completely.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="space-y-4">
                            <Field
                                light
                                name="gslToken"
                                label="GSL Token"
                                description="Visit https://steamcommunity.com/dev/managegameservers to generate a token."
                                placeholder="Enter your GSL token here..."
                                autoFocus
                            />
                        </div>

                        <Dialog.Footer>
                            <Button 
                                isSecondary 
                                onClick={() => setVisible(false)}
                                className="flex items-center gap-2"
                                size="small"
                                disabled={loading || isSubmitting}
                            >
                                Cancel
                            </Button>
                            <Button 
                                type="submit"
                                className="flex items-center gap-2"
                                size="small"
                                isLoading={loading || isSubmitting}
                                disabled={loading || isSubmitting}
                            >
                                <Save size={16} />
                                Update GSL Token
                            </Button>
                        </Dialog.Footer>
                    </Form>
                </Dialog>
            )}
        </Formik>
    );
};

export default GSLTokenModalFeature;
