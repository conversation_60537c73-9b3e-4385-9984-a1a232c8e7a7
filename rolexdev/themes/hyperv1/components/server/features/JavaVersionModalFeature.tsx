import React, { useEffect, useState } from 'react';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import setSelectedDockerImage from '@/api/server/setSelectedDockerImage';
import FlashMessageRender from '@/components/FlashMessageRender';
import useFlash from '@/plugins/useFlash';
import { SocketEvent, SocketRequest } from '@/components/server/events';
import Select from '@/components/elements/Select';
import useWebsocketEvent from '@rolexdev/themes/hyperv1/plugins/useWebsocketEvent';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import getServerStartup from '@/api/swr/getServerStartup';
import InputSpinner from '@/components/elements/InputSpinner';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import { AlertTriangle, Coffee, Download, X } from 'lucide-react';

const MATCH_ERRORS = [
    'minecraft 1.17 requires running the server with java 16 or above',
    'minecraft 1.18 requires running the server with java 17 or above',
    'Minecraft 1.19 requires running the server with Java 17 or above',
    'java.lang.unsupportedclassversionerror',
    'unsupported major.minor version',
    'has been compiled by a more recent version of the java runtime',
];

const JavaVersionModalFeature = () => {
    const [visible, setVisible] = useState(false);
    const [loading, setLoading] = useState(false);
    const [selectedVersion, setSelectedVersion] = useState('');

    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const status = ServerContext.useStoreState((state) => state.status.value);
    const { clearFlashes, clearAndAddHttpError } = useFlash();
    const { instance } = ServerContext.useStoreState((state) => state.socket);

    const { data, isValidating, mutate } = getServerStartup(uuid, null, { revalidateOnMount: false });

    useEffect(() => {
        if (!visible) return;

        mutate().then((value) => {
            setSelectedVersion(Object.values(value?.dockerImages || [])[0] || '');
        });
    }, [visible]);

    useWebsocketEvent(SocketEvent.CONSOLE_OUTPUT, (data) => {
        if (status === 'running') return;

        if (MATCH_ERRORS.some((p) => data.toLowerCase().includes(p.toLowerCase()))) {
            setVisible(true);
        }
    });

    const updateJava = () => {
        setLoading(true);
        clearFlashes('feature:javaVersion');

        setSelectedDockerImage(uuid, selectedVersion)
            .then(() => {
                if (status === 'offline' && instance) {
                    instance.send(SocketRequest.SET_STATE, 'restart');
                }
                setVisible(false);
            })
            .catch((error) => clearAndAddHttpError({ key: 'feature:javaVersion', error }))
            .then(() => setLoading(false));
    };

    useEffect(() => {
        clearFlashes('feature:javaVersion');
    }, []);

    return (
        <Dialog
            open={visible}
            onClose={() => setVisible(false)}
            title="Unsupported Java Version"
            description="This server is currently running an unsupported version of Java and cannot be started."
            preventExternalClose={true}
        >
            <Dialog.Icon type="warning" />
            
            <div className="space-y-6">
                <FlashMessageRender key="feature:javaVersion" className="mb-4" />
                
                <div className="bg-hyper-background border border-hyper-primary rounded-lg p-4">
                    <div className="flex items-start gap-3">
                        <AlertTriangle size={20} className="text-yellow-500 flex-shrink-0 mt-0.5" />
                        <div className="space-y-3">
                            <h3 className="text-lg font-semibold text-hyper-primary">Java Version Compatibility Issue</h3>
                            <div className="space-y-2 text-sm text-hyper-secondary">
                                <p>
                                    This server is currently running an unsupported version of Java and cannot be started.
                                </p>
                                <Can action="startup.docker-image">
                                    <p>
                                        Please select a supported version from the list below to continue starting the server.
                                    </p>
                                </Can>
                            </div>
                        </div>
                    </div>
                </div>

                <Can action="startup.docker-image">
                    <div className="space-y-3">
                        <div className="flex items-center gap-2">
                            <Coffee size={16} className="text-hyper-accent" />
                            <label className="text-sm font-medium text-hyper-foreground">
                                Select Java Version
                            </label>
                        </div>
                        <InputSpinner visible={!data || isValidating}>
                            <Select 
                                disabled={!data} 
                                onChange={(e) => setSelectedVersion(e.target.value)}
                                className="w-full bg-hyper-background border border-hyper-primary rounded-lg p-3 text-hyper-foreground focus:border-hyper-accent transition-colors"
                            >
                                {!data ? (
                                    <option disabled>Loading Java versions...</option>
                                ) : (
                                    Object.keys(data.dockerImages).map((key) => (
                                        <option key={key} value={data.dockerImages[key]}>
                                            {key}
                                        </option>
                                    ))
                                )}
                            </Select>
                        </InputSpinner>
                    </div>
                </Can>
            </div>

            <Dialog.Footer>
                <Button 
                    isSecondary 
                    onClick={() => setVisible(false)}
                    className="flex items-center gap-2"
                    size="small"
                    disabled={loading}
                >
                    <X size={16} />
                    Cancel
                </Button>
                <Can action="startup.docker-image">
                    <Button 
                        onClick={updateJava}
                        className="flex items-center gap-2"
                        size="small"
                        isLoading={loading}
                        disabled={loading}
                    >
                        <Download size={16} />
                        Update Docker Image
                    </Button>
                </Can>
            </Dialog.Footer>
        </Dialog>
    );
};

export default JavaVersionModalFeature;
