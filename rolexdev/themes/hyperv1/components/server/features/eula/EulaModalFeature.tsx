import React, { useEffect, useState } from 'react';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import saveFileContents from '@/api/server/files/saveFileContents';
import FlashMessageRender from '@/components/FlashMessageRender';
import useFlash from '@/plugins/useFlash';
import { SocketEvent, SocketRequest } from '@/components/server/events';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import { FileText, Check, X } from 'lucide-react';

const EulaModalFeature = () => {
    const [visible, setVisible] = useState(false);
    const [loading, setLoading] = useState(false);

    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const status = ServerContext.useStoreState((state) => state.status.value);
    const { clearFlashes, clearAndAddHttpError } = useFlash();
    const { connected, instance } = ServerContext.useStoreState((state) => state.socket);

    useEffect(() => {
        if (!connected || !instance || status === 'running') return;

        const listener = (line: string) => {
            if (line.toLowerCase().indexOf('you need to agree to the eula in order to run the server') >= 0) {
                setVisible(true);
            }
        };

        instance.addListener(SocketEvent.CONSOLE_OUTPUT, listener);

        return () => {
            instance.removeListener(SocketEvent.CONSOLE_OUTPUT, listener);
        };
    }, [connected, instance, status]);

    const onAcceptEULA = () => {
        setLoading(true);
        clearFlashes('feature:eula');

        saveFileContents(uuid, 'eula.txt', 'eula=true')
            .then(() => {
                if (status === 'offline' && instance) {
                    instance.send(SocketRequest.SET_STATE, 'restart');
                }

                setLoading(false);
                setVisible(false);
            })
            .catch((error) => {
                console.error(error);
                clearAndAddHttpError({ key: 'feature:eula', error });
            })
            .then(() => setLoading(false));
    };

    useEffect(() => {
        clearFlashes('feature:eula');
    }, []);

    return (
        <Dialog
            open={visible}
            onClose={() => setVisible(false)}
            title="Accept Minecraft® EULA"
            description="By pressing 'I Accept' below you are indicating your agreement to the Minecraft® EULA."
            preventExternalClose={true}
        >
            <Dialog.Icon type="info" />
            
            <div className="space-y-6">
                <FlashMessageRender key="feature:eula" className="mb-4" />
                
                <div className="bg-hyper-background border border-hyper-primary rounded-lg p-4">
                    <div className="flex items-start gap-3">
                        <FileText size={20} className="text-hyper-accent flex-shrink-0 mt-0.5" />
                        <div className="space-y-3">
                            <h3 className="text-lg font-semibold text-hyper-primary">Minecraft® End User License Agreement</h3>
                            <p className="text-sm text-hyper-secondary leading-relaxed">
                                This server requires acceptance of the Minecraft® EULA to continue. By clicking "<span className='text-hyper-accent'>I Accept</span>" below, 
                                you acknowledge that you have read and agree to be bound by the terms and conditions outlined in 
                                the Minecraft® End User License Agreement.
                            </p>
                            <div className="pt-2">
                                <a
                                    target="_blank"
                                    className="inline-flex items-center gap-2 text-hyper-accent hover:text-hyper-primary transition-colors duration-200 font-medium text-sm"
                                    rel="noreferrer noopener"
                                    href="https://www.minecraft.net/eula"
                                >
                                    <FileText size={16} />
                                    View Minecraft® EULA
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <Dialog.Footer>
                <Button 
                    isSecondary 
                    onClick={() => setVisible(false)}
                    className="flex items-center gap-2"
                    size="small"
                    disabled={loading}
                >
                    <X size={16} />
                    Cancel
                </Button>
                <Button 
                    onClick={onAcceptEULA}
                    className="flex items-center gap-2"
                    size="small"
                    isLoading={loading}
                    disabled={loading}
                >
                    <Check size={16} />
                    I Accept
                </Button>
            </Dialog.Footer>
        </Dialog>
    );
};

export default EulaModalFeature;
