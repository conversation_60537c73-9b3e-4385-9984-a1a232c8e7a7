import React, { useCallback, useEffect, useState } from 'react';
import tw from 'twin.macro';
import VariableBox from '@rolexdev/themes/hyperv1/components/server/startup/VariableBox';
import ServerContentBlock from '@rolexdev/themes/hyperv1/components/elements/ServerContentBlock';
import getServerStartup from '@/api/swr/getServerStartup';
import Spinner from '@/components/elements/Spinner';
import { ServerError } from '@/components/elements/ScreenBlock';
import { httpErrorToHuman } from '@/api/http';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { useDeepCompareEffect } from '@/plugins/useDeepCompareEffect';
import isEqual from 'react-fast-compare';
import Input from '@rolexdev/themes/hyperv1/components/elements/Input';
import setSelectedDockerImage from '@/api/server/setSelectedDockerImage';
import InputSpinner from '@/components/elements/InputSpinner';
import useFlash from '@/plugins/useFlash';
import { Terminal, Settings, Container } from 'lucide-react';
import DockerImageSelector from '@rolexdev/themes/hyperv1/components/server/startup/DockerImageSelector';
import { PageTransition, AnimatedCard, AnimatedContainer } from '@rolexdev/themes/hyperv1/components/elements/animations';

const StartupContainer = () => {
    const [loading, setLoading] = useState(false);
    const { clearFlashes, clearAndAddHttpError } = useFlash();

    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const variables = ServerContext.useStoreState(
        ({ server }) => ({
            variables: server.data!.variables,
            invocation: server.data!.invocation,
            dockerImage: server.data!.dockerImage,
        }),
        isEqual
    );

    const { data, error, isValidating, mutate } = getServerStartup(uuid, {
        ...variables,
        dockerImages: { [variables.dockerImage]: variables.dockerImage },
    });

    const setServerFromState = ServerContext.useStoreActions((actions) => actions.server.setServerFromState);
    const isCustomImage =
        data &&
        !Object.values(data.dockerImages)
            .map((v) => v.toLowerCase())
            .includes(variables.dockerImage.toLowerCase());

    useEffect(() => {
        // Since we're passing in initial data this will not trigger on mount automatically. We
        // want to always fetch fresh information from the API however when we're loading the startup
        // information.
        mutate();
    }, []);

    useDeepCompareEffect(() => {
        if (!data) return;

        setServerFromState((s) => ({
            ...s,
            invocation: data.invocation,
            variables: data.variables,
        }));
    }, [data]);

    const updateSelectedDockerImage = useCallback(
        (image: string) => {
            setLoading(true);
            clearFlashes('startup:image');

            setSelectedDockerImage(uuid, image)
                .then(() => setServerFromState((s) => ({ ...s, dockerImage: image })))
                .catch((error) => {
                    console.error(error);
                    clearAndAddHttpError({ key: 'startup:image', error });
                })
                .then(() => setLoading(false));
        },
        [uuid]
    );

    return !data ? (
        !error || (error && isValidating) ? (
            <PageTransition>
                <ServerContentBlock title={'Startup Settings'}>
                    <AnimatedCard>
                        <Spinner centered size={Spinner.Size.LARGE} />
                    </AnimatedCard>
                </ServerContentBlock>
            </PageTransition>
        ) : (
            <PageTransition>
                <ServerContentBlock title={'Startup Settings'}>
                    <AnimatedCard>
                        <ServerError title={'Oops!'} message={httpErrorToHuman(error)} onRetry={() => mutate()} />
                    </AnimatedCard>
                </ServerContentBlock>
            </PageTransition>
        )
    ) : (
        <PageTransition>
            <ServerContentBlock title={'Startup Settings'} showFlashKey={'startup:image'}>
                <AnimatedContainer staggerChildren={0.1}>
                    {/* Header Section */}
                    <AnimatedCard>
                        <div className="flex items-center gap-3 mb-6">
                            <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                                <Settings size={20} className="text-hyper-accent" />
                            </div>
                            <div>
                                <h2 className="text-lg font-semibold text-hyper-primary">Startup Configuration</h2>
                                <p className="text-sm text-hyper-muted-foreground">
                                    Configure startup command, Docker image, and environment variables for your server.
                                </p>
                            </div>
                        </div>
                    </AnimatedCard>

                    {/* Card Grid Layout - Using consistent 342px width cards */}
                    <AnimatedCard>
                        <div className="flex flex-wrap gap-6 justify-center lg:justify-start">
                            {/* Startup Command Card */}
                            <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-6 w-[342px] transition-all duration-200 hover:border-hyper-accent">
                                <div className="flex items-center gap-3 mb-6">
                                    <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                                        <Terminal size={20} className="text-hyper-accent" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <h2 className="text-lg font-semibold text-hyper-primary">Startup Command</h2>
                                        <p className="text-sm text-hyper-accent">Server execution command</p>
                                    </div>
                                </div>
                                
                                <div className="bg-hyper-background border border-hyper-primary rounded-lg p-4">
                                    <p className="font-mono text-sm text-hyper-foreground break-all leading-relaxed">{data.invocation}</p>
                                </div>
                            </div>

                            {/* Docker Image Card */}
                            <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-6 w-[342px] transition-all duration-200 hover:border-hyper-accent z-[11]">
                                <div className="flex items-center gap-3 mb-6">
                                    <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                                        <Container size={20} className="text-hyper-accent" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <h2 className="text-lg font-semibold text-hyper-primary">Docker Image</h2>
                                        <p className="text-sm text-hyper-accent">Container runtime image</p>
                                    </div>
                                </div>
                                
                                {Object.keys(data.dockerImages).length > 1 && !isCustomImage ? (
                                    <div className="space-y-4">
                                        <label className="block text-sm font-medium text-hyper-foreground">
                                            Select Docker Image
                                        </label>
                                        <InputSpinner visible={loading}>
                                            <DockerImageSelector
                                                dockerImages={data.dockerImages}
                                                currentImage={variables.dockerImage}
                                                disabled={Object.keys(data.dockerImages).length < 2}
                                                loading={loading}
                                                onChange={updateSelectedDockerImage}
                                            />
                                        </InputSpinner>
                                        <div className="bg-hyper-glass border border-hyper-accent rounded-lg p-4">
                                            <div className="flex items-start gap-3">
                                                <div className="w-1 h-6 bg-hyper-accent rounded-full flex-shrink-0 mt-0.5"></div>
                                                <p className="text-sm text-hyper-foreground">
                                                    This is an advanced feature allowing you to select a Docker image to use when running
                                                    this server instance.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="space-y-4">
                                        <label className="block text-sm font-medium text-hyper-foreground">
                                            Docker Image
                                        </label>
                                        <Input disabled readOnly value={variables.dockerImage} isLight />
                                        {isCustomImage && (
                                            <div className="bg-hyper-glass border border-hyper-accent rounded-lg p-4">
                                                <div className="flex items-start gap-3">
                                                    <div className="w-1 h-6 bg-hyper-accent rounded-full flex-shrink-0 mt-0.5"></div>
                                                    <p className="text-sm text-hyper-foreground">
                                                        This {"server's"} Docker image has been manually set by an administrator and cannot
                                                        be changed through this UI.
                                                    </p>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>

                            {/* Environment Variables Cards */}
                            {data.variables.map((variable) => (
                                <div key={variable.envVariable} className="w-[342px]">
                                    <VariableBox variable={variable} />
                                </div>
                            ))}
                        </div>
                    </AnimatedCard>
                </AnimatedContainer>
            </ServerContentBlock>
        </PageTransition>
    );
};

export default StartupContainer;
