import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';

interface VariableSelectorProps {
    options: string[];
    currentValue: string;
    disabled: boolean;
    onChange: (value: string) => void;
    name: string;
}

const VariableSelector: React.FC<VariableSelectorProps> = ({
    options,
    currentValue,
    disabled,
    onChange,
    name,
}) => {
    const [open, setOpen] = useState(false);

    const handleSelect = (value: string) => {
        onChange(value);
        setOpen(false);
    };

    return (
        <div className="relative" tabIndex={0} onBlur={() => setTimeout(() => setOpen(false), 100)}>
            <button
                className={`flex items-center justify-between px-3 py-2 rounded-lg bg-hyper-card border border-hyper-accent text-hyper-primary w-full hover:bg-hyper-primary-10 transition ${
                    disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                }`}
                onClick={() => !disabled && setOpen(v => !v)}
                disabled={disabled}
                aria-haspopup="listbox"
                aria-expanded={open}
                type="button"
            >
                <span className="truncate mr-2 text-sm font-medium">
                    {currentValue || 'Select value...'}
                </span>
                <ChevronDown 
                    size={16} 
                    className={`text-hyper-accent transition-transform duration-200 ${
                        open ? 'rotate-180' : ''
                    }`} 
                />
            </button>
            {open && (
                <div className="z-[9999] absolute left-0 mt-2 w-full bg-hyper-sidebar backdrop-blur-lg border border-hyper-accent rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {options.map(option => (
                        <div
                            key={option}
                            className={`px-3 py-2 cursor-pointer bg-hyper-sidebar backdrop-blur-lg hover:bg-hyper-primary-10 truncate text-hyper-primary text-sm ${
                                option === currentValue ? 'bg-hyper-primary-30 font-bold' : ''
                            }`}
                            onMouseDown={e => {
                                e.preventDefault();
                                handleSelect(option);
                            }}
                        >
                            <span className="font-medium">{option}</span>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default VariableSelector;