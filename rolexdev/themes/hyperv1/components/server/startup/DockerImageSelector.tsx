import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';

interface DockerImageSelectorProps {
    dockerImages: Record<string, string>;
    currentImage: string;
    disabled: boolean;
    loading: boolean;
    onChange: (image: string) => void;
}

const DockerImageSelector: React.FC<DockerImageSelectorProps> = ({
    dockerImages,
    currentImage,
    disabled,
    loading,
    onChange,
}) => {
    const [open, setOpen] = useState(false);

    const currentImageKey = Object.keys(dockerImages).find(key => dockerImages[key] === currentImage);
    const currentImageName = currentImageKey || currentImage;

    const handleSelect = (imageKey: string) => {
        onChange(dockerImages[imageKey]);
        setOpen(false);
    };

    return (
        <div className="relative" tabIndex={0} onBlur={() => setTimeout(() => setOpen(false), 100)}>
            <button
                className={`flex items-center justify-between px-3 py-2 rounded-lg bg-hyper-card border border-hyper-accent text-hyper-primary w-full hover:bg-hyper-primary-10 transition ${
                    disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                } ${loading ? 'pointer-events-none' : ''}`}
                onClick={() => !disabled && !loading && setOpen(v => !v)}
                disabled={disabled || loading}
                aria-haspopup="listbox"
                aria-expanded={open}
                type="button"
            >
                <span className="truncate mr-2 text-sm font-medium">
                    {currentImageName}
                </span>
                <ChevronDown 
                    size={16} 
                    className={`text-hyper-accent transition-transform duration-200 ${
                        open ? 'rotate-180' : ''
                    }`} 
                />
            </button>
            {open && (
                <div className="z-[9999] absolute left-0 mt-2 w-full bg-hyper-sidebar backdrop-blur-lg border border-hyper-accent rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {Object.keys(dockerImages).map(imageKey => (
                        <div
                            key={dockerImages[imageKey]}
                            className={`px-3 py-2 cursor-pointer bg-hyper-sidebar backdrop-blur-lg hover:bg-hyper-primary-10 truncate text-hyper-primary text-sm ${
                                dockerImages[imageKey] === currentImage ? 'bg-hyper-primary-30 font-bold' : ''
                            }`}
                            onMouseDown={e => {
                                e.preventDefault();
                                handleSelect(imageKey);
                            }}
                        >
                            <div className="flex flex-col">
                                <span className="font-medium">{imageKey}</span>
                                <span className="text-xs text-hyper-accent font-mono opacity-75">
                                    {dockerImages[imageKey]}
                                </span>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default DockerImageSelector;