import React, { memo, useState } from 'react';
import { ServerEggVariable } from '@/api/server/types';
import { usePermissions } from '@rolexdev/themes/hyperv1/plugins/usePermissions';
import InputSpinner from '@/components/elements/InputSpinner';
import Input from '@rolexdev/themes/hyperv1/components/elements/Input';
import Switch from '@/components/elements/Switch';
import { debounce } from 'debounce';
import updateStartupVariable from '@/api/server/updateStartupVariable';
import useFlash from '@/plugins/useFlash';
import FlashMessageRender from '@/components/FlashMessageRender';
import getServerStartup from '@/api/swr/getServerStartup';
import isEqual from 'react-fast-compare';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { Variable, Eye, EyeOff } from 'lucide-react';
import VariableSelector from '@rolexdev/themes/hyperv1/components/server/startup/VariableSelector';

interface Props {
    variable: ServerEggVariable;
}

const VariableBox = ({ variable }: Props) => {
    const FLASH_KEY = `server:startup:${variable.envVariable}`;

    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const [loading, setLoading] = useState(false);
    const [canEdit] = usePermissions(['startup.update']);
    const { clearFlashes, clearAndAddHttpError } = useFlash();
    const { mutate } = getServerStartup(uuid);

    const setVariableValue = debounce((value: string) => {
        setLoading(true);
        clearFlashes(FLASH_KEY);

        updateStartupVariable(uuid, variable.envVariable, value)
            .then(([response, invocation]) =>
                mutate(
                    (data) => ({
                        ...data,
                        invocation,
                        variables: (data.variables || []).map((v) =>
                            v.envVariable === response.envVariable ? response : v
                        ),
                    }),
                    false
                )
            )
            .catch((error) => {
                console.error(error);
                clearAndAddHttpError({ error, key: FLASH_KEY });
            })
            .then(() => setLoading(false));
    }, 500);

    const useSwitch = variable.rules.some(
        (v) => v === 'boolean' || v === 'in:0,1' || v === 'in:1,0' || v === 'in:true,false' || v === 'in:false,true'
    );
    const isStringSwitch = variable.rules.some((v) => v === 'string');
    const selectValues = variable.rules.find((v) => v.startsWith('in:'))?.split(',') || [];

    return (
        <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-6 w-[342px] transition-all duration-200 hover:border-hyper-accent hover:scale-[1.02] hover:shadow-lg hover:shadow-hyper-accent/10">
            {/* Variable Header */}
            <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center flex-shrink-0">
                    <Variable size={20} className="text-hyper-accent" />
                </div>
                <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-lg font-semibold text-hyper-primary">
                            {variable.name}
                        </h3>
                        {!variable.isEditable && (
                            <span className="inline-flex items-center gap-1 bg-hyper-glass text-hyper-accent text-xs py-1 px-2 rounded-full border border-hyper-accent">
                                <EyeOff size={12} />
                                Read Only
                            </span>
                        )}
                    </div>
                    <p className="text-sm text-hyper-accent font-mono">
                        {variable.envVariable}
                    </p>
                </div>
            </div>

            {/* Flash Messages */}
            <FlashMessageRender byKey={FLASH_KEY} className="mb-4" />

            {/* Variable Input */}
            <div className="space-y-4">
                <InputSpinner visible={loading}>
                    {useSwitch ? (
                        <div className="flex items-center justify-between p-4 bg-hyper-background border border-hyper-primary rounded-lg">
                            <span className="text-sm font-medium text-hyper-foreground">
                                Enable {variable.name}
                            </span>
                            <Switch
                                readOnly={!canEdit || !variable.isEditable}
                                name={variable.envVariable}
                                defaultChecked={
                                    isStringSwitch ? variable.serverValue === 'true' : variable.serverValue === '1'
                                }
                                onChange={() => {
                                    if (canEdit && variable.isEditable) {
                                        if (isStringSwitch) {
                                            setVariableValue(variable.serverValue === 'true' ? 'false' : 'true');
                                        } else {
                                            setVariableValue(variable.serverValue === '1' ? '0' : '1');
                                        }
                                    }
                                }}
                            />
                        </div>
                    ) : (
                        <>
                            {selectValues.length > 0 ? (
                                <div className="space-y-3">
                                    <label className="block text-sm font-medium text-hyper-foreground">
                                        Select Value
                                    </label>
                                    <VariableSelector
                                        options={selectValues.map(val => val.replace('in:', ''))}
                                        currentValue={variable.serverValue ?? variable.defaultValue}
                                        disabled={!canEdit || !variable.isEditable}
                                        onChange={(value) => setVariableValue(value)}
                                        name={variable.envVariable}
                                    />
                                </div>
                            ) : (
                                <div className="space-y-3">
                                    <label className="block text-sm font-medium text-hyper-foreground">
                                        Value
                                    </label>
                                    <Input
                                        onKeyUp={(e) => {
                                            if (canEdit && variable.isEditable) {
                                                setVariableValue(e.currentTarget.value);
                                            }
                                        }}
                                        readOnly={!canEdit || !variable.isEditable}
                                        name={variable.envVariable}
                                        defaultValue={variable.serverValue ?? ''}
                                        placeholder={variable.defaultValue}
                                        isLight
                                    />
                                </div>
                            )}
                        </>
                    )}
                </InputSpinner>

                {/* Variable Description */}
                {variable.description && (
                    <div className="bg-hyper-glass border border-hyper-accent rounded-lg p-4">
                        <div className="flex items-start gap-3">
                            <div className="w-1 h-6 bg-hyper-accent rounded-full flex-shrink-0 mt-0.5"></div>
                            <p className="text-sm text-hyper-foreground leading-relaxed">
                                {variable.description}
                            </p>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default memo(VariableBox, isEqual);
