import React, { useEffect, useState } from 'react';
import { ActivityLogFilters, useActivityLogs } from '@rolexdev/themes/hyperv1/api/server/activity';
import ServerContentBlock from '@rolexdev/themes/hyperv1/components/elements/ServerContentBlock';
import { useFlashKey } from '@/plugins/useFlash';
import FlashMessageRender from '@/components/FlashMessageRender';
import Spinner from '@/components/elements/Spinner';
import ActivityLogEntry from '@rolexdev/themes/hyperv1/components/elements/activity/ActivityLogEntry';
import PaginationFooter from '@rolexdev/themes/hyperv1/components/elements/table/PaginationFooter';
import { DesktopComputerIcon } from '@heroicons/react/solid';
import { X, Activity } from 'lucide-react';
import classNames from 'classnames';
import { styles as btnStyles } from '@/components/elements/button/index';
import useLocationHash from '@/plugins/useLocationHash';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import Tooltip from '@rolexdev/themes/hyperv1/components/elements/tooltip/Tooltip';
import { PageTransition, AnimatedCard, AnimatedList } from '@rolexdev/themes/hyperv1/components/elements/animations';

export default () => {
    const { hash } = useLocationHash();
    const { clearAndAddHttpError } = useFlashKey('server:activity');
    const [filters, setFilters] = useState<ActivityLogFilters>({ page: 1, sorts: { timestamp: -1 } });

    const { data, isValidating, error } = useActivityLogs(filters, {
        revalidateOnMount: true,
        revalidateOnFocus: false,
    });

    useEffect(() => {
        setFilters((value) => ({ ...value, filters: { ip: hash.ip, event: hash.event } }));
    }, [hash]);

    useEffect(() => {
        clearAndAddHttpError(error);
    }, [error]);

    return (
        <ServerContentBlock title={'Activity Log'}>
            <PageTransition>
                {/* Header Section */}
                <AnimatedCard delay={0.1} variant="default" className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                        <Activity size={20} className="text-hyper-accent" />
                    </div>
                    <div>
                        <h2 className="text-lg font-semibold text-hyper-primary">Server Activity</h2>
                        <p className="text-sm text-hyper-muted-foreground">
                            Monitor all server actions, events, and user activity logs.
                        </p>
                    </div>
                </AnimatedCard>

                <FlashMessageRender byKey={'server:activity'} />
                {(filters.filters?.event || filters.filters?.ip) && (
                    <AnimatedCard delay={0.2} variant="default" className={'flex justify-end mb-2'}>
                        <Button
                            isSecondary
                            size={'xsmall'}
                            className={classNames(btnStyles.button, btnStyles.text, 'w-full sm:w-auto items-center')}
                            onClick={() => setFilters((value) => ({ ...value, filters: {} }))}
                        >
                            <X size={16} />
                            <span>
                                Clear Filters 
                            </span>
                        </Button>
                    </AnimatedCard>
                )}
                {!data && isValidating ? (
                    <Spinner centered />
                ) : (
                    <AnimatedCard delay={0.3} variant="default" className={'rounded-xl mb-2 max-w-[1000px] mx-auto'}>
                        <AnimatedList variant="slide" staggerDelay={0.05}>
                            {data?.items.map((activity, index) => (
                                <ActivityLogEntry
                                    key={activity.id}
                                    activity={activity}
                                    isLast={index === data.items.length - 1}
                                >
                                    {typeof activity.properties.useragent === 'string' && (
                                        <Tooltip content={activity.properties.useragent} placement={'top'}>
                                            <span>
                                                <DesktopComputerIcon height={16} width={16} className='text-hyper-accent'/>
                                            </span>
                                        </Tooltip>
                                    )}
                                </ActivityLogEntry>
                            ))}
                        </AnimatedList>
                    </AnimatedCard>
                )}
                {data && (
                    <AnimatedCard delay={0.4} variant="default">
                        <PaginationFooter
                            pagination={data.pagination}
                            onPageSelect={(page, perPage) =>
                                setFilters((value) => ({
                                    ...value,
                                    page,
                                    perPage,
                                }))
                            }
                        />
                    </AnimatedCard>
                )}
            </PageTransition>
        </ServerContentBlock>
    );
};
