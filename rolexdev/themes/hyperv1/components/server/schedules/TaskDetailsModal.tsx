import React, { useContext, useEffect, useState } from 'react';
import { Schedule, Task } from '@/api/server/schedules/getServerSchedules';
import { Field as FormikField, Form, Formik, FormikHelpers, useField } from 'formik';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import createOrUpdateScheduleTask from '@/api/server/schedules/createOrUpdateScheduleTask';
import { httpErrorToHuman } from '@/api/http';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import FlashMessageRender from '@/components/FlashMessageRender';
import { boolean, number, object, string } from 'yup';
import useFlash from '@/plugins/useFlash';
import FormikFieldWrapper from '@/components/elements/FormikFieldWrapper';
import { Textarea } from '@rolexdev/themes/hyperv1/components/elements/Input';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import Select from '@/components/elements/Select';
import FormikSwitch from '@rolexdev/themes/hyperv1/components/elements/FormikSwitch';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import { Command, Power, Archive, Clock, ChevronDown } from 'lucide-react';

interface Props {
    schedule: Schedule;
    task?: Task;
    visible: boolean;
    onModalDismissed: () => void;
}

interface Values {
    action: string;
    payload: string;
    timeOffset: string;
    continueOnFailure: boolean;
}

const schema = object().shape({
    action: string().required().oneOf(['command', 'power', 'backup']),
    payload: string().when('action', {
        is: (v) => v !== 'backup',
        then: string().required('A task payload must be provided.'),
        otherwise: string(),
    }),
    continueOnFailure: boolean(),
    timeOffset: number()
        .typeError('The time offset must be a valid number between 0 and 900.')
        .required('A time offset value must be provided.')
        .min(0, 'The time offset must be at least 0 seconds.')
        .max(900, 'The time offset must be less than 900 seconds.'),
});

const ActionListener = () => {
    const [{ value }, { initialValue: initialAction }] = useField<string>('action');
    const [, { initialValue: initialPayload }, { setValue, setTouched }] = useField<string>('payload');

    useEffect(() => {
        if (value !== initialAction) {
            setValue(value === 'power' ? 'start' : '');
            setTouched(false);
        } else {
            setValue(initialPayload || '');
            setTouched(false);
        }
    }, [value]);

    return null;
};

// Custom Action Selector Component
const ActionSelector = ({ value, onChange, disabled = false }: { value: string; onChange: (value: string) => void; disabled?: boolean }) => {
    const [open, setOpen] = useState(false);

    const actions = [
        { value: 'command', label: 'Send command', icon: Command },
        { value: 'power', label: 'Send power action', icon: Power },
        { value: 'backup', label: 'Create backup', icon: Archive },
    ];

    const currentAction = actions.find(action => action.value === value);
    const CurrentIcon = currentAction?.icon || Command;

    const handleSelect = (actionValue: string) => {
        onChange(actionValue);
        setOpen(false);
    };

    return (
        <div className="relative" tabIndex={0} onBlur={() => setTimeout(() => setOpen(false), 100)}>
            <button
                className={`flex items-center justify-between px-3 py-2 rounded-lg bg-hyper-card border border-hyper-accent text-hyper-primary w-full hover:bg-hyper-primary-10 transition ${
                    disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                }`}
                onClick={() => !disabled && setOpen(v => !v)}
                disabled={disabled}
                aria-haspopup="listbox"
                aria-expanded={open}
                type="button"
            >
                <div className="flex items-center gap-2">
                    <CurrentIcon size={16} className="text-hyper-accent" />
                    <span className="truncate text-sm font-medium">
                        {currentAction?.label}
                    </span>
                </div>
                <ChevronDown 
                    size={16} 
                    className={`text-hyper-accent transition-transform duration-200 ${
                        open ? 'rotate-180' : ''
                    }`} 
                />
            </button>
            {open && (
                <div className="z-[9999] absolute left-0 mt-2 w-full bg-hyper-sidebar backdrop-blur-lg border border-hyper-accent rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {actions.map(action => {
                        const ActionIcon = action.icon;
                        return (
                            <div
                                key={action.value}
                                className={`px-3 py-2 cursor-pointer bg-hyper-sidebar backdrop-blur-lg hover:bg-hyper-primary-10 text-hyper-primary text-sm flex items-center gap-2 ${
                                    action.value === value ? 'bg-hyper-primary-30 font-bold' : ''
                                }`}
                                onMouseDown={e => {
                                    e.preventDefault();
                                    handleSelect(action.value);
                                }}
                            >
                                <ActionIcon size={16} className="text-hyper-accent" />
                                <span className="font-medium">{action.label}</span>
                            </div>
                        );
                    })}
                </div>
            )}
        </div>
    );
};

// Custom Power Action Selector Component
const PowerActionSelector = ({ value, onChange, disabled = false }: { value: string; onChange: (value: string) => void; disabled?: boolean }) => {
    const [open, setOpen] = useState(false);

    const powerActions = [
        { value: 'start', label: 'Start the server' },
        { value: 'restart', label: 'Restart the server' },
        { value: 'stop', label: 'Stop the server' },
        { value: 'kill', label: 'Terminate the server' },
    ];

    const currentAction = powerActions.find(action => action.value === value);

    const handleSelect = (actionValue: string) => {
        onChange(actionValue);
        setOpen(false);
    };

    return (
        <div className="relative" tabIndex={0} onBlur={() => setTimeout(() => setOpen(false), 100)}>
            <button
                className={`flex items-center justify-between px-3 py-2 rounded-lg bg-hyper-card border border-hyper-accent text-hyper-primary w-full hover:bg-hyper-primary-10 transition ${
                    disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                }`}
                onClick={() => !disabled && setOpen(v => !v)}
                disabled={disabled}
                aria-haspopup="listbox"
                aria-expanded={open}
                type="button"
            >
                <div className="flex items-center gap-2">
                    <Power size={16} className="text-hyper-accent" />
                    <span className="truncate text-sm font-medium">
                        {currentAction?.label || 'Select power action'}
                    </span>
                </div>
                <ChevronDown 
                    size={16} 
                    className={`text-hyper-accent transition-transform duration-200 ${
                        open ? 'rotate-180' : ''
                    }`} 
                />
            </button>
            {open && (
                <div className="z-[9999] absolute left-0 mt-2 w-full bg-hyper-sidebar backdrop-blur-lg border border-hyper-accent rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {powerActions.map(action => (
                        <div
                            key={action.value}
                            className={`px-3 py-2 cursor-pointer bg-hyper-sidebar backdrop-blur-lg hover:bg-hyper-primary-10 text-hyper-primary text-sm flex items-center gap-2 ${
                                action.value === value ? 'bg-hyper-primary-30 font-bold' : ''
                            }`}
                            onMouseDown={e => {
                                e.preventDefault();
                                handleSelect(action.value);
                            }}
                        >
                            <Power size={16} className="text-hyper-accent" />
                            <span className="font-medium">{action.label}</span>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

const TaskDetailsModal = ({ schedule, task, visible, onModalDismissed }: Props) => {
    const { clearFlashes, addError } = useFlash();

    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const appendSchedule = ServerContext.useStoreActions((actions) => actions.schedules.appendSchedule);
    const backupLimit = ServerContext.useStoreState((state) => state.server.data!.featureLimits.backups);

    useEffect(() => {
        return () => {
            clearFlashes('schedule:task');
        };
    }, []);

    const submit = (values: Values, { setSubmitting }: FormikHelpers<Values>) => {
        clearFlashes('schedule:task');
        if (backupLimit === 0 && values.action === 'backup') {
            setSubmitting(false);
            addError({
                message: "A backup task cannot be created when the server's backup limit is set to 0.",
                key: 'schedule:task',
            });
        } else {
            createOrUpdateScheduleTask(uuid, schedule.id, task?.id, values)
                .then((task) => {
                    let tasks = schedule.tasks.map((t) => (t.id === task.id ? task : t));
                    if (!schedule.tasks.find((t) => t.id === task.id)) {
                        tasks = [...tasks, task];
                    }

                    appendSchedule({ ...schedule, tasks });
                    onModalDismissed();
                })
                .catch((error) => {
                    console.error(error);
                    setSubmitting(false);
                    addError({ message: httpErrorToHuman(error), key: 'schedule:task' });
                });
        }
    };

    return (
        <Dialog
            open={visible}
            onClose={onModalDismissed}
            title={task ? 'Edit Task' : 'Create Task'}
        >
            <Formik
                onSubmit={submit}
                validationSchema={schema}
                initialValues={{
                    action: task?.action || 'command',
                    payload: task?.payload || '',
                    timeOffset: task?.timeOffset.toString() || '0',
                    continueOnFailure: task?.continueOnFailure || false,
                }}
            >
                {({ isSubmitting, values, submitForm }) => (
                    <Form>
                        <FlashMessageRender byKey={'schedule:task'} className="mb-6" />
                        
                        {/* Action and Time Offset */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <FormikFieldWrapper
                                    name={'action'}
                                    label={'Action'}
                                    description={'Select the type of task to execute on the schedule.'}
                                >
                                    <ActionListener />
                                    <FormikField name="action">
                                        {({ field, form }: any) => (
                                            <ActionSelector
                                                value={field.value}
                                                onChange={(value) => form.setFieldValue('action', value)}
                                                disabled={isSubmitting}
                                            />
                                        )}
                                    </FormikField>
                                </FormikFieldWrapper>
                            </div>
                            
                            <Field
                                light
                                name={'timeOffset'}
                                label={'Time offset (in seconds)'}
                                description={
                                    'The amount of time to wait after the previous task executes before running this one. If this is the first task on a schedule this will not be applied.'
                                }
                            />
                        </div>
                        
                        {/* Payload Section */}
                        <div className="mb-6">
                            {values.action === 'command' ? (
                                <div>
                                    <FormikFieldWrapper
                                        name={'payload'}
                                        label={'Command'}
                                        description={'Enter the command that should be executed when this task runs.'}
                                    >
                                        <FormikField 
                                            as={Textarea} 
                                            name={'payload'} 
                                            rows={6}
                                            isLight
                                            placeholder="say Server restart in 5 minutes...
restart"
                                            className="font-mono text-sm"
                                        />
                                    </FormikFieldWrapper>
                                </div>
                            ) : values.action === 'power' ? (
                                <div>
                                    <FormikFieldWrapper
                                        name={'payload'}
                                        label={'Power Action'}
                                        description={'Select the power action to send to the server.'}
                                    >
                                        <FormikField name="payload">
                                            {({ field, form }: any) => (
                                                <PowerActionSelector
                                                    value={field.value}
                                                    onChange={(value) => form.setFieldValue('payload', value)}
                                                    disabled={isSubmitting}
                                                />
                                            )}
                                        </FormikField>
                                    </FormikFieldWrapper>
                                </div>
                            ) : (
                                <div>
                                    <FormikFieldWrapper
                                        name={'payload'}
                                        label={'Ignored Files & Directories'}
                                        description={
                                            'Enter the files or folders to ignore while generating this backup. Leave blank to use the contents of the .pteroignore file in the root of the server directory if present. Wildcard matching of files and folders is supported in addition to negating a rule by prefixing the path with an exclamation point.'
                                        }
                                    >
                                        <FormikField 
                                            as={Textarea} 
                                            name={'payload'} 
                                            rows={6}
                                            isLight
                                            placeholder="*.log
temp/
cache/
!important.log"
                                            className="font-mono text-sm"
                                        />
                                    </FormikFieldWrapper>
                                </div>
                            )}
                        </div>
                        
                        {/* Continue on Failure Setting */}
                        <div className="bg-hyper-sidebar border border-hyper-primary rounded-lg p-4 mb-6">
                            <FormikSwitch
                                name={'continueOnFailure'}
                                description={'Future tasks will be run when this task fails.'}
                                label={'Continue on Failure'}
                            />
                        </div>

                        {/* Footer Actions */}
                        <Dialog.Footer>
                            <Button 
                                type="button" 
                                isSecondary 
                                onClick={onModalDismissed}
                                disabled={isSubmitting}
                                size="small"
                            >
                                Cancel
                            </Button>
                            <Button 
                                onClick={submitForm}
                                disabled={isSubmitting}
                                className="flex items-center gap-2"
                                size="small"
                            >
                                {values.action === 'command' && <Command size={16} />}
                                {values.action === 'power' && <Power size={16} />}
                                {values.action === 'backup' && <Archive size={16} />}
                                {task ? 'Save Changes' : 'Create Task'}
                            </Button>
                        </Dialog.Footer>
                    </Form>
                )}
            </Formik>
        </Dialog>
    );
};

export default TaskDetailsModal;
