import React, { useEffect, useState } from 'react';
import getServerSchedules from '@/api/server/schedules/getServerSchedules';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import Spinner from '@/components/elements/Spinner';
import { useHistory, useRouteMatch } from 'react-router-dom';
import FlashMessageRender from '@/components/FlashMessageRender';
import ScheduleRow from '@rolexdev/themes/hyperv1/components/server/schedules/ScheduleRow';
import { httpErrorToHuman } from '@/api/http';
import EditScheduleModal from '@rolexdev/themes/hyperv1/components/server/schedules/EditScheduleModal';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import useFlash from '@/plugins/useFlash';
import ServerContentBlock from '@rolexdev/themes/hyperv1/components/elements/ServerContentBlock';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import PaginationFooter from '@rolexdev/themes/hyperv1/components/elements/table/PaginationFooter';
import { CalendarClock, Plus } from 'lucide-react';
import { PageTransition, AnimatedCard, AnimatedContainer } from '@rolexdev/themes/hyperv1/components/elements/animations';

export default () => {
    const match = useRouteMatch();
    const history = useHistory();

    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const { clearFlashes, addError } = useFlash();
    const [loading, setLoading] = useState(true);
    const [visible, setVisible] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [perPage, setPerPage] = useState(10);

    const schedules = ServerContext.useStoreState((state) => state.schedules.data);
    const setSchedules = ServerContext.useStoreActions((actions) => actions.schedules.setSchedules);

    // Calculate pagination
    const totalSchedules = schedules.length;
    const totalPages = Math.ceil(totalSchedules / perPage);
    const startIndex = (currentPage - 1) * perPage;
    const endIndex = startIndex + perPage;
    const paginatedSchedules = schedules.slice(startIndex, endIndex);

    const pagination = {
        currentPage,
        perPage,
        total: totalSchedules,
        count: paginatedSchedules.length,
        totalPages,
    };

    const handlePageSelect = (page: number, newPerPage: number) => {
        setCurrentPage(page);
        if (newPerPage !== perPage) {
            setPerPage(newPerPage);
            // Adjust current page if needed when changing perPage
            const newTotalPages = Math.ceil(totalSchedules / newPerPage);
            if (page > newTotalPages) {
                setCurrentPage(newTotalPages || 1);
            }
        }
    };

    useEffect(() => {
        clearFlashes('schedules');
        getServerSchedules(uuid)
            .then((schedules) => setSchedules(schedules))
            .catch((error) => {
                addError({ message: httpErrorToHuman(error), key: 'schedules' });
                console.error(error);
            })
            .then(() => setLoading(false));
    }, []);

    return (
        <PageTransition>
            <ServerContentBlock title={'Schedules'}>
                <FlashMessageRender byKey={'schedules'} className="mb-6" />
                
                <AnimatedContainer staggerChildren={0.1}>
                    {/* Header Section */}
                    <AnimatedCard>
                        <div className="flex items-center gap-3 mb-6">
                            <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                                <CalendarClock size={20} className="text-hyper-accent" />
                            </div>
                            <div className="flex-1">
                                <h2 className="text-xl font-semibold text-hyper-primary">Server Schedules</h2>
                                <p className="text-sm text-hyper-muted-foreground">
                                    Automate server tasks with cron-based scheduling
                                </p>
                            </div>
                        </div>
                    </AnimatedCard>

                    {!schedules.length && loading ? (
                        <AnimatedCard>
                            <Spinner size={'large'} centered />
                        </AnimatedCard>
                    ) : (
                        <>
                            {schedules.length === 0 ? (
                                <AnimatedCard>
                                    <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-8 text-center w-[342px] mx-auto">
                                        <div className="w-16 h-16 rounded-lg bg-hyper-primary-10 flex items-center justify-center mx-auto mb-4">
                                            <CalendarClock size={32} className="text-hyper-accent" />
                                        </div>
                                        <h3 className="text-lg font-semibold text-hyper-primary mb-2">No Schedules Yet</h3>
                                        <p className="text-sm text-hyper-accent mb-6">
                                            It looks like there are no schedules currently configured for this server. Create your first schedule to get started.
                                        </p>
                                        <Can action={'schedule.create'}>
                                            <Button 
                                                onClick={() => setVisible(true)}
                                                className="flex items-center gap-2"
                                            >
                                                <Plus size={16} />
                                                Create First Schedule
                                            </Button>
                                        </Can>
                                    </div>
                                </AnimatedCard>
                            ) : (
                                <>
                                    <AnimatedCard>
                                        <div className="flex flex-wrap gap-6 justify-center lg:justify-start">
                                            {paginatedSchedules.map((schedule) => (
                                                <div
                                                    key={schedule.id}
                                                    className="cursor-pointer transition-all duration-200"
                                                    onClick={(e: any) => {
                                                        e.preventDefault();
                                                        history.push(`${match.url}/${schedule.id}`);
                                                    }}
                                                >
                                                    <ScheduleRow schedule={schedule} />
                                                </div>
                                            ))}
                                            
                                            {/* Add New Schedule Card */}
                                            <Can action={'schedule.create'}>
                                                <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary border-dashed rounded-lg p-6 w-[342px] transition-all duration-200 hover:border-hyper-accent flex flex-col items-center justify-center text-center">
                                                    <div className="w-16 h-16 rounded-lg bg-hyper-primary-10 flex items-center justify-center mx-auto mb-4">
                                                        <Plus size={32} className="text-hyper-accent" />
                                                    </div>
                                                    <h3 className="text-lg font-semibold text-hyper-primary mb-2">Add New Schedule</h3>
                                                    <p className="text-sm text-hyper-accent mb-6">
                                                        Create automated tasks that run on a schedule to manage your server efficiently.
                                                    </p>
                                                    <Button 
                                                        onClick={() => setVisible(true)}
                                                        className="flex items-center gap-2"
                                                        size="small"
                                                    >
                                                        <Plus size={16} />
                                                        Create Schedule
                                                    </Button>
                                                </div>
                                            </Can>
                                        </div>
                                    </AnimatedCard>
                                    
                                    {totalSchedules > perPage && (
                                        <AnimatedCard>
                                            <PaginationFooter
                                                pagination={pagination}
                                                onPageSelect={handlePageSelect}
                                                className="mt-6"
                                            />
                                        </AnimatedCard>
                                    )}
                                </>
                            )}
                        </>
                    )}
                </AnimatedContainer>
                
                <EditScheduleModal 
                    visible={visible} 
                    onModalDismissed={() => setVisible(false)} 
                />
            </ServerContentBlock>
        </PageTransition>
    );
};
