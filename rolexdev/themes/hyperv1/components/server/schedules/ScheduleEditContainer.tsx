import React, { useCallback, useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import getServerSchedule from '@/api/server/schedules/getServerSchedule';
import Spinner from '@/components/elements/Spinner';
import FlashMessageRender from '@/components/FlashMessageRender';
import EditScheduleModal from '@rolexdev/themes/hyperv1/components/server/schedules/EditScheduleModal';
import NewTaskButton from '@rolexdev/themes/hyperv1/components/server/schedules/NewTaskButton';
import DeleteScheduleButton from '@rolexdev/themes/hyperv1/components/server/schedules/DeleteScheduleButton';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import useFlash from '@/plugins/useFlash';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import ServerContentBlock from '@rolexdev/themes/hyperv1/components/elements/ServerContentBlock';
import ScheduleTaskRow from '@rolexdev/themes/hyperv1/components/server/schedules/ScheduleTaskRow';
import isEqual from 'react-fast-compare';
import { format } from 'date-fns';
import RunScheduleButton from '@rolexdev/themes/hyperv1/components/server/schedules/RunScheduleButton';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { CalendarClock, Clock, Edit3, Play, Square, AlertTriangle, Trash2 } from 'lucide-react';

interface Params {
    id: string;
}

const CronBox = ({ title, value }: { title: string; value: string }) => (
    <div className="bg-hyper-sidebar border border-hyper-primary rounded-lg p-4">
        <p className="text-xs text-hyper-muted-foreground font-medium uppercase tracking-wide">{title}</p>
        <p className="text-lg font-mono text-hyper-accent mt-1">{value}</p>
    </div>
);

const ActivePill = ({ active }: { active: boolean }) => (
    <span
        className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${
            active 
                ? 'bg-green-900 text-green-300 border border-green-600' 
                : 'bg-red-900 text-red-300 border border-red-600'
        }`}
    >
        {active ? (
            <>
                <Play size={8} />
                Active
            </>
        ) : (
            <>
                <Square size={8} />
                Inactive
            </>
        )}
    </span>
);

export default () => {
    const history = useHistory();
    const { id: scheduleId } = useParams<Params>();

    const id = ServerContext.useStoreState((state) => state.server.data!.id);
    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);

    const { clearFlashes, clearAndAddHttpError } = useFlash();
    const [isLoading, setIsLoading] = useState(true);
    const [showEditModal, setShowEditModal] = useState(false);

    const schedule = ServerContext.useStoreState(
        (st) => st.schedules.data.find((s) => s.id === Number(scheduleId)),
        isEqual
    );
    const appendSchedule = ServerContext.useStoreActions((actions) => actions.schedules.appendSchedule);

    useEffect(() => {
        if (schedule?.id === Number(scheduleId)) {
            setIsLoading(false);
            return;
        }

        clearFlashes('schedules');
        getServerSchedule(uuid, Number(scheduleId))
            .then((schedule) => appendSchedule(schedule))
            .catch((error) => {
                console.error(error);
                clearAndAddHttpError({ error, key: 'schedules' });
            })
            .then(() => setIsLoading(false));
    }, [scheduleId]);

    const toggleEditModal = useCallback(() => {
        setShowEditModal((s) => !s);
    }, []);

    return (
        <ServerContentBlock title={'Schedule Details'}>
            <FlashMessageRender byKey={'schedules'} className="mb-6" />
            {!schedule || isLoading ? (
                <Spinner size={'large'} centered />
            ) : (
                <div className="space-y-6">
                    {/* Schedule Header */}
                    <div className="bg-hyper-sidebar border border-hyper-primary rounded-lg p-6">
                        <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-4">
                                <div className="w-12 h-12 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                                    <CalendarClock size={24} className="text-hyper-accent" />
                                </div>
                                <div>
                                    <div className="flex items-center gap-3 mb-1">
                                        <h1 className="text-2xl font-semibold text-hyper-primary">{schedule.name}</h1>
                                        {schedule.isProcessing ? (
                                            <span className="inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium bg-yellow-900 text-yellow-300 border border-yellow-600">
                                                <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse" />
                                                Processing
                                            </span>
                                        ) : (
                                            <ActivePill active={schedule.isActive} />
                                        )}
                                    </div>
                                    <div className="flex items-center gap-6 text-sm text-hyper-muted-foreground">
                                        <div className="flex items-center gap-2">
                                            <Clock size={14} />
                                            <span>
                                                Last run: {schedule.lastRunAt ? format(schedule.lastRunAt, "MMM do 'at' h:mma") : 'never'}
                                            </span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <AlertTriangle size={14} />
                                            <span>
                                                Next run: {schedule.nextRunAt ? format(schedule.nextRunAt, "MMM do 'at' h:mma") : 'not scheduled'}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="flex items-center gap-3">
                                <Can action={'schedule.update'}>
                                    <Button isSecondary onClick={toggleEditModal} className="flex items-center gap-2">
                                        <Edit3 size={16} />
                                        Edit
                                    </Button>
                                    <NewTaskButton schedule={schedule} />
                                </Can>
                            </div>
                        </div>
                        
                        {/* Cron Expression */}
                        <div className="grid grid-cols-5 gap-4">
                            <CronBox title={'Minute'} value={schedule.cron.minute} />
                            <CronBox title={'Hour'} value={schedule.cron.hour} />
                            <CronBox title={'Day (Month)'} value={schedule.cron.dayOfMonth} />
                            <CronBox title={'Month'} value={schedule.cron.month} />
                            <CronBox title={'Day (Week)'} value={schedule.cron.dayOfWeek} />
                        </div>
                    </div>

                    {/* Tasks Section */}
                    <div className="bg-hyper-sidebar border border-hyper-primary rounded-lg">
                        <div className="p-6 border-b border-hyper-primary">
                            <h2 className="text-lg font-semibold text-hyper-primary">Schedule Tasks</h2>
                            <p className="text-sm text-hyper-muted-foreground mt-1">
                                Tasks that will be executed when this schedule runs
                            </p>
                        </div>
                        
                        <div className="divide-y divide-hyper-primary">
                            {schedule.tasks.length > 0
                                ? schedule.tasks
                                      .sort((a, b) =>
                                          a.sequenceId === b.sequenceId ? 0 : a.sequenceId > b.sequenceId ? 1 : -1
                                      )
                                      .map((task) => (
                                          <ScheduleTaskRow
                                              key={`${schedule.id}_${task.id}`}
                                              task={task}
                                              schedule={schedule}
                                          />
                                      ))
                                : (
                                    <div className="p-8 text-center">
                                        <div className="w-16 h-16 rounded-lg bg-hyper-primary-10 flex items-center justify-center mx-auto mb-4">
                                            <CalendarClock size={32} className="text-hyper-accent" />
                                        </div>
                                        <h3 className="text-lg font-semibold text-hyper-primary mb-2">No Tasks Configured</h3>
                                        <p className="text-sm text-hyper-accent mb-4">
                                            This schedule doesn't have any tasks yet. Add a task to get started.
                                        </p>
                                        <Can action={'schedule.update'}>
                                            <NewTaskButton schedule={schedule} />
                                        </Can>
                                    </div>
                                )}
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center justify-between">
                        <Can action={'schedule.delete'}>
                            <DeleteScheduleButton
                                scheduleId={schedule.id}
                                onDeleted={() => history.push(`/server/${id}/schedules`)}
                            />
                        </Can>
                        
                        {schedule.tasks.length > 0 && (
                            <Can action={'schedule.update'}>
                                <RunScheduleButton schedule={schedule} />
                            </Can>
                        )}
                    </div>

                    <EditScheduleModal 
                        visible={showEditModal} 
                        schedule={schedule} 
                        onModalDismissed={toggleEditModal} 
                    />
                </div>
            )}
        </ServerContentBlock>
    );
};
