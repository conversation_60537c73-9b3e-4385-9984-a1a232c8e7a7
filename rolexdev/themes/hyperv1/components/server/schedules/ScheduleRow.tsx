import React from 'react';
import { Schedule } from '@/api/server/schedules/getServerSchedules';
import { CalendarClock, Clock, Play, Square, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';

export default ({ schedule }: { schedule: Schedule }) => (
    <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-6 w-[342px] transition-all duration-200 hover:border-hyper-accent hover:scale-[1.02] hover:shadow-lg hover:shadow-hyper-accent/10">
        {/* Header Section */}
        <div className="flex items-center gap-3 mb-6">
            <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                <CalendarClock size={20} className="text-hyper-accent" />
            </div>
            <div className="flex-1 min-w-0">
                <h3 className="text-lg font-semibold text-hyper-primary truncate">{schedule.name}</h3>
                <p className="text-sm text-hyper-accent">Scheduled Task</p>
            </div>
        </div>

        {/* Status Section */}
        <div className="mb-6">
            <label className="block text-sm font-medium text-hyper-foreground mb-2">Status</label>
            <span
                className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium border ${
                    schedule.isProcessing
                        ? 'bg-yellow-500/10 text-yellow-400 border-yellow-500/30'
                        : schedule.isActive
                        ? 'bg-green-500/10 text-green-400 border-green-500/30'
                        : 'bg-gray-500/10 text-gray-400 border-gray-500/30'
                }`}
            >
                {schedule.isProcessing ? (
                    <>
                        <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse" />
                        Processing
                    </>
                ) : schedule.isActive ? (
                    <>
                        <Play size={14} />
                        Active
                    </>
                ) : (
                    <>
                        <Square size={14} />
                        Inactive
                    </>
                )}
            </span>
        </div>

        {/* Schedule Details */}
        <div className="space-y-4 mb-6">
            <div>
                <label className="block text-sm font-medium text-hyper-foreground mb-2">CRON Expression</label>
                <div className="bg-hyper-background border border-hyper-primary rounded-lg p-3">
                    <code className="text-sm font-mono text-hyper-foreground">
                        {schedule.cron.minute} {schedule.cron.hour} {schedule.cron.dayOfMonth} {schedule.cron.month} {schedule.cron.dayOfWeek}
                    </code>
                </div>
            </div>
            
            <div>
                <label className="block text-sm font-medium text-hyper-foreground mb-2">Last Run</label>
                <div className="flex items-center gap-2 bg-hyper-background border border-hyper-primary rounded-lg p-3">
                    <Clock size={16} className="text-hyper-accent" />
                    <span className="text-sm text-hyper-foreground">
                        {schedule.lastRunAt ? format(schedule.lastRunAt, "MMM do 'at' h:mma") : 'Never executed'}
                    </span>
                </div>
            </div>
            
            <div>
                <label className="block text-sm font-medium text-hyper-foreground mb-2">Next Run</label>
                <div className="flex items-center gap-2 bg-hyper-background border border-hyper-primary rounded-lg p-3">
                    <AlertCircle size={16} className="text-hyper-accent" />
                    <span className="text-sm text-hyper-foreground">
                        {schedule.nextRunAt ? format(schedule.nextRunAt, "MMM do 'at' h:mma") : 'Not scheduled'}
                    </span>
                </div>
            </div>
        </div>
    </div>
);
