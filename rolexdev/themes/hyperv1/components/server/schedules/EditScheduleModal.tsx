import React, { useEffect, useState } from 'react';
import { Schedule } from '@/api/server/schedules/getServerSchedules';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import { Form, Formik, FormikHelpers } from 'formik';
import FormikSwitch from '@rolexdev/themes/hyperv1/components/elements/FormikSwitch';
import createOrUpdateSchedule from '@/api/server/schedules/createOrUpdateSchedule';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { httpErrorToHuman } from '@/api/http';
import FlashMessageRender from '@/components/FlashMessageRender';
import useFlash from '@/plugins/useFlash';
import ScheduleCheatsheetCards from '@rolexdev/themes/hyperv1/components/server/schedules/ScheduleCheatsheetCards';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import Switch from '@rolexdev/themes/hyperv1/components/elements/Switch';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import { CalendarClock, Save } from 'lucide-react';

interface Props {
    schedule?: Schedule;
    visible: boolean;
    onModalDismissed: () => void;
}

interface Values {
    name: string;
    dayOfWeek: string;
    month: string;
    dayOfMonth: string;
    hour: string;
    minute: string;
    enabled: boolean;
    onlyWhenOnline: boolean;
}

const EditScheduleModal = ({ schedule, visible, onModalDismissed }: Props) => {
    const { addError, clearFlashes } = useFlash();
    const [showCheatsheet, setShowCheatsheet] = useState(false);

    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const appendSchedule = ServerContext.useStoreActions((actions) => actions.schedules.appendSchedule);

    useEffect(() => {
        return () => {
            clearFlashes('schedule:edit');
        };
    }, []);

    const submit = (values: Values, { setSubmitting }: FormikHelpers<Values>) => {
        clearFlashes('schedule:edit');
        createOrUpdateSchedule(uuid, {
            id: schedule?.id,
            name: values.name,
            cron: {
                minute: values.minute,
                hour: values.hour,
                dayOfMonth: values.dayOfMonth,
                month: values.month,
                dayOfWeek: values.dayOfWeek,
            },
            isActive: values.enabled,
            onlyWhenOnline: values.onlyWhenOnline,
        })
            .then((schedule) => {
                setSubmitting(false);
                appendSchedule(schedule);
                onModalDismissed();
            })
            .catch((error) => {
                console.error(error);
                setSubmitting(false);
                addError({ message: httpErrorToHuman(error), key: 'schedule:edit' });
            });
    };

    return (
        <Dialog
            open={visible}
            onClose={onModalDismissed}
            title={schedule ? 'Edit Schedule' : 'Create New Schedule'}
            description={schedule ? 'Modify your scheduled task settings' : 'Set up automated tasks for your server'}
        >
            <Formik
                onSubmit={submit}
                initialValues={{
                    name: schedule?.name || '',
                    minute: schedule?.cron.minute || '*/5',
                    hour: schedule?.cron.hour || '*',
                    dayOfMonth: schedule?.cron.dayOfMonth || '*',
                    month: schedule?.cron.month || '*',
                    dayOfWeek: schedule?.cron.dayOfWeek || '*',
                    enabled: schedule?.isActive ?? true,
                    onlyWhenOnline: schedule?.onlyWhenOnline ?? true,
                }}
            >
                {({ isSubmitting, submitForm }) => (
                    <Form>
                        <FlashMessageRender byKey={'schedule:edit'} className="mb-6" />
                        
                        {/* Form Fields */}
                        <div className="space-y-6">
                            {/* Schedule Name */}
                            <Field
                                light
                                name={'name'}
                                label={'Schedule Name'}
                                description={'A human readable identifier for this schedule.'}
                            />
                            
                            {/* Cron Expression Grid */}
                            <div>
                                <label className="block text-sm font-medium text-hyper-foreground mb-3">
                                    Schedule Configuration
                                </label>
                                <div className="grid grid-cols-2 sm:grid-cols-5 gap-4">
                                    <Field light name={'minute'} label={'Minute'} />
                                    <Field light name={'hour'} label={'Hour'} />
                                    <Field light name={'dayOfMonth'} label={'Day of Month'} />
                                    <Field light name={'month'} label={'Month'} />
                                    <Field light name={'dayOfWeek'} label={'Day of Week'} />
                                </div>
                                <p className="text-xs text-hyper-muted-foreground mt-2">
                                    The schedule system supports the use of Cronjob syntax when defining when tasks should begin
                                    running. Use the fields above to specify when these tasks should begin running.
                                </p>
                            </div>
                            
                            {/* Cheatsheet Toggle */}
                            <div className="bg-hyper-glass border border-hyper-accent rounded-lg p-4">
                                <Switch
                                    name={'show_cheatsheet'}
                                    description={'Show the cron cheatsheet for some examples.'}
                                    label={'Show Cheatsheet'}
                                    defaultChecked={showCheatsheet}
                                    onChange={() => setShowCheatsheet((s) => !s)}
                                />
                                {showCheatsheet && (
                                    <div className="mt-4">
                                        <ScheduleCheatsheetCards />
                                    </div>
                                )}
                            </div>
                            
                            {/* Online Only Setting */}
                            <div className="bg-hyper-glass border border-hyper-accent rounded-lg p-4">
                                <FormikSwitch
                                    name={'onlyWhenOnline'}
                                    description={'Only execute this schedule when the server is in a running state.'}
                                    label={'Only When Server Is Online'}
                                />
                            </div>
                            
                            {/* Enable/Disable Setting */}
                            <div className="bg-hyper-glass border border-hyper-accent rounded-lg p-4">
                                <FormikSwitch
                                    name={'enabled'}
                                    description={'This schedule will be executed automatically if enabled.'}
                                    label={'Schedule Enabled'}
                                />
                            </div>
                        </div>

                        {/* Footer Actions */}
                        <Dialog.Footer>
                            <Button 
                                type="button" 
                                isSecondary 
                                onClick={onModalDismissed}
                                disabled={isSubmitting}
                                size="small"
                            >
                                Cancel
                            </Button>
                            <Button 
                                onClick={submitForm}
                                disabled={isSubmitting}
                                isLoading={isSubmitting}
                                className="flex items-center gap-2"
                                size="small"
                            >
                                <Save size={16} />
                                {schedule ? 'Save Changes' : 'Create Schedule'}
                            </Button>
                        </Dialog.Footer>
                    </Form>
                )}
            </Formik>
        </Dialog>
    );
};

export default EditScheduleModal;