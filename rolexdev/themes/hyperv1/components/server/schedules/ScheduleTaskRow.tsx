import React, { useState } from 'react';
import { Schedule, Task } from '@/api/server/schedules/getServerSchedules';
import deleteScheduleTask from '@/api/server/schedules/deleteScheduleTask';
import { httpErrorToHuman } from '@/api/http';
import SpinnerOverlay from '@/components/elements/SpinnerOverlay';
import TaskDetailsModal from '@rolexdev/themes/hyperv1/components/server/schedules/TaskDetailsModal';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import useFlash from '@/plugins/useFlash';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import ConfirmationModal from '@rolexdev/themes/hyperv1/components/elements/ConfirmationModal';
import { Command, Power, Archive, Clock, AlertTriangle, Edit3, Trash2, ArrowDown } from 'lucide-react';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';

interface Props {
    schedule: Schedule;
    task: Task;
}

const getActionDetails = (action: string): [string, React.ReactNode] => {
    switch (action) {
        case 'command':
            return ['Send Command', <Command size={16} className="text-hyper-accent" />];
        case 'power':
            return ['Send Power Action', <Power size={16} className="text-hyper-accent" />];
        case 'backup':
            return ['Create Backup', <Archive size={16} className="text-hyper-accent" />];
        default:
            return ['Unknown Action', <Command size={16} className="text-hyper-accent" />];
    }
};

export default ({ schedule, task }: Props) => {
    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const { clearFlashes, addError } = useFlash();
    const [visible, setVisible] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const appendSchedule = ServerContext.useStoreActions((actions) => actions.schedules.appendSchedule);

    const onConfirmDeletion = () => {
        setIsLoading(true);
        clearFlashes('schedules');
        deleteScheduleTask(uuid, schedule.id, task.id)
            .then(() =>
                appendSchedule({
                    ...schedule,
                    tasks: schedule.tasks.filter((t) => t.id !== task.id),
                })
            )
            .catch((error) => {
                console.error(error);
                setIsLoading(false);
                addError({ message: httpErrorToHuman(error), key: 'schedules' });
            });
    };

    const [title, icon] = getActionDetails(task.action);

    return (
        <div className="p-4 hover:bg-hyper-primary-10 transition-colors duration-200">
            <SpinnerOverlay visible={isLoading} fixed size={'large'} />
            <TaskDetailsModal
                schedule={schedule}
                task={task}
                visible={isEditing}
                onModalDismissed={() => setIsEditing(false)}
            />
            <ConfirmationModal
                title={'Confirm task deletion'}
                buttonText={'Delete Task'}
                onConfirmed={onConfirmDeletion}
                visible={visible}
                onModalDismissed={() => setVisible(false)}
            >
                Are you sure you want to delete this task? This action cannot be undone.
            </ConfirmationModal>
            
            <div className="flex items-start gap-4">
                {/* Task Icon */}
                <div className="w-8 h-8 rounded-lg bg-hyper-primary-10 flex items-center justify-center flex-shrink-0 mt-1">
                    {icon}
                </div>
                
                {/* Task Content */}
                <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                        <h4 className="text-sm font-semibold text-hyper-primary uppercase tracking-wide">
                            {title}
                        </h4>
                        
                        {/* Action Buttons */}
                        <div className="flex items-center gap-2">
                            <Can action={'schedule.update'}>
                                <Button
                                    isSecondary
                                    size="xsmall"
                                    onClick={() => setIsEditing(true)}
                                    className="flex items-center gap-1"
                                >
                                    <Edit3 size={12} />
                                    Edit
                                </Button>
                            </Can>
                            <Can action={'schedule.update'}>
                                <Button
                                    color='red'
                                    size="xsmall"
                                    onClick={() => setVisible(true)}
                                    className="flex items-center gap-1"
                                >
                                    <Trash2 size={12} />
                                    Delete
                                </Button>
                            </Can>
                        </div>
                    </div>
                    
                    {/* Task Payload */}
                    {task.payload && (
                        <div className="mb-3">
                            {task.action === 'backup' && (
                                <p className="text-xs text-hyper-muted-foreground mb-2 uppercase tracking-wide">
                                    Ignoring files & folders:
                                </p>
                            )}
                            <div className="bg-hyper-background border border-hyper-primary rounded-lg p-3">
                                <code className="text-sm text-hyper-accent font-mono break-all whitespace-pre-wrap">
                                    {task.payload}
                                </code>
                            </div>
                        </div>
                    )}
                    
                    {/* Task Metadata */}
                    <div className="flex items-center gap-4 text-xs">
                        {task.continueOnFailure && (
                            <div className="flex items-center gap-1 px-2 py-1 bg-yellow-900 text-yellow-300 rounded-full border border-yellow-600">
                                <ArrowDown size={10} />
                                Continues on Failure
                            </div>
                        )}
                        {task.sequenceId > 1 && task.timeOffset > 0 && (
                            <div className="flex items-center gap-1 px-2 py-1 bg-hyper-sidebar text-hyper-accent rounded-full border border-hyper-primary">
                                <Clock size={10} />
                                {task.timeOffset}s delay
                            </div>
                        )}
                        <div className="text-hyper-muted-foreground">
                            Sequence: #{task.sequenceId}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};
