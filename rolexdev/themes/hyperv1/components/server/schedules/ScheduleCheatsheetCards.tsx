import React from 'react';
import { Clock, Hash } from 'lucide-react';

export default () => {
    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Examples Card */}
            <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-6">
                <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                        <Clock size={20} className="text-hyper-accent" />
                    </div>
                    <h2 className="text-xl font-semibold text-hyper-primary">Examples</h2>
                </div>
                
                <div className="space-y-1">
                    <div className="flex items-center py-3 px-4 bg-hyper-glass border border-hyper-accent rounded-lg">
                        <div className="w-1/2 font-mono text-sm text-hyper-primary font-medium">*/5 * * * *</div>
                        <div className="w-1/2 text-sm text-hyper-accent">every 5 minutes</div>
                    </div>
                    <div className="flex items-center py-3 px-4 bg-hyper-background border border-hyper-primary rounded-lg">
                        <div className="w-1/2 font-mono text-sm text-hyper-primary font-medium">0 */1 * * *</div>
                        <div className="w-1/2 text-sm text-hyper-accent">every hour</div>
                    </div>
                    <div className="flex items-center py-3 px-4 bg-hyper-glass border border-hyper-accent rounded-lg">
                        <div className="w-1/2 font-mono text-sm text-hyper-primary font-medium">0 8-12 * * *</div>
                        <div className="w-1/2 text-sm text-hyper-accent">hour range</div>
                    </div>
                    <div className="flex items-center py-3 px-4 bg-hyper-background border border-hyper-primary rounded-lg">
                        <div className="w-1/2 font-mono text-sm text-hyper-primary font-medium">0 0 * * *</div>
                        <div className="w-1/2 text-sm text-hyper-accent">once a day</div>
                    </div>
                    <div className="flex items-center py-3 px-4 bg-hyper-glass border border-hyper-accent rounded-lg">
                        <div className="w-1/2 font-mono text-sm text-hyper-primary font-medium">0 0 * * MON</div>
                        <div className="w-1/2 text-sm text-hyper-accent">every Monday</div>
                    </div>
                </div>
            </div>

            {/* Special Characters Card */}
            <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-6">
                <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                        <Hash size={20} className="text-hyper-accent" />
                    </div>
                    <h2 className="text-xl font-semibold text-hyper-primary">Special Characters</h2>
                </div>
                
                <div className="space-y-1">
                    <div className="flex items-center py-3 px-4 bg-hyper-glass border border-hyper-accent rounded-lg">
                        <div className="w-1/2 font-mono text-lg text-hyper-primary font-bold">*</div>
                        <div className="w-1/2 text-sm text-hyper-accent">any value</div>
                    </div>
                    <div className="flex items-center py-3 px-4 bg-hyper-background border border-hyper-primary rounded-lg">
                        <div className="w-1/2 font-mono text-lg text-hyper-primary font-bold">,</div>
                        <div className="w-1/2 text-sm text-hyper-accent">value list separator</div>
                    </div>
                    <div className="flex items-center py-3 px-4 bg-hyper-glass border border-hyper-accent rounded-lg">
                        <div className="w-1/2 font-mono text-lg text-hyper-primary font-bold">-</div>
                        <div className="w-1/2 text-sm text-hyper-accent">range values</div>
                    </div>
                    <div className="flex items-center py-3 px-4 bg-hyper-background border border-hyper-primary rounded-lg">
                        <div className="w-1/2 font-mono text-lg text-hyper-primary font-bold">/</div>
                        <div className="w-1/2 text-sm text-hyper-accent">step values</div>
                    </div>
                </div>
            </div>
        </div>
    );
};
