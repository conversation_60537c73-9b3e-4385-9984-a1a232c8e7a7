import React, { useState } from 'react';
import { Schedule } from '@/api/server/schedules/getServerSchedules';
import TaskDetailsModal from '@rolexdev/themes/hyperv1/components/server/schedules/TaskDetailsModal';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { Plus } from 'lucide-react';

interface Props {
    schedule: Schedule;
}

export default ({ schedule }: Props) => {
    const [visible, setVisible] = useState(false);

    return (
        <>
            <TaskDetailsModal schedule={schedule} visible={visible} onModalDismissed={() => setVisible(false)} />
            <Button onClick={() => setVisible(true)} className="flex items-center gap-2">
                <Plus size={16} />
                New Task
            </Button>
        </>
    );
};
