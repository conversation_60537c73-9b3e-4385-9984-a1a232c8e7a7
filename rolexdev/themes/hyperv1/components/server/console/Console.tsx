import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ITerminalOptions, Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { SearchAddon } from 'xterm-addon-search';
import { SearchBarAddon } from 'xterm-addon-search-bar';
import { WebLinksAddon } from 'xterm-addon-web-links';
import { ScrollDownHelperAddon } from '@rolexdev/themes/hyperv1/plugins/XtermScrollDownHelperAddon';
import SpinnerOverlay from '@/components/elements/SpinnerOverlay';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { usePermissions } from '@rolexdev/themes/hyperv1/plugins/usePermissions';
import { theme as th } from 'twin.macro';
import useEventListener from '@/plugins/useEventListener';
import { debounce } from 'debounce';
import { usePersistedState } from '@/plugins/usePersistedState';
import { SocketEvent, SocketRequest } from '@/components/server/events';
import classNames from 'classnames';
import { ChevronDoubleRightIcon } from '@heroicons/react/solid';

import 'xterm/css/xterm.css';
import styles from './style.module.css';

const theme = {
    cursor: 'transparent',
    black: th`colors.black`.toString(),
    red: '#E54B4B',
    green: '#9ECE58',
    yellow: '#FAED70',
    blue: '#396FE2',
    magenta: '#BB80B3',
    cyan: '#2DDAFD',
    white: '#d0d0d0',
    brightBlack: 'rgba(255, 255, 255, 0.2)',
    brightRed: '#FF5370',
    brightGreen: '#C3E88D',
    brightYellow: '#FFCB6B',
    brightBlue: '#82AAFF',
    brightMagenta: '#C792EA',
    brightCyan: '#89DDFF',
    brightWhite: '#ffffff',
    selection: '#FAF089',
    background: 'transparent',
};

const terminalProps: ITerminalOptions = {
    fontWeight: 'bold',
    disableStdin: true,
    cursorStyle: 'underline',
    allowTransparency: true,
    fontSize: 12,
    rows: 30,
    theme: theme,
    letterSpacing: -2,
};

// ANSI color codes for different message types
const ANSI_COLORS = {
    reset: '\u001b[0m',
    bright: '\u001b[1m',
    
    // Foreground colors
    red: '\u001b[31m',
    green: '\u001b[32m',
    yellow: '\u001b[33m',
    blue: '\u001b[34m',
    magenta: '\u001b[35m',
    cyan: '\u001b[36m',
    white: '\u001b[37m',
    gray: '\u001b[90m',
    
    // Bright foreground colors
    brightRed: '\u001b[91m',
    brightGreen: '\u001b[92m',
    brightYellow: '\u001b[93m',
    brightBlue: '\u001b[94m',
    brightMagenta: '\u001b[95m',
    brightCyan: '\u001b[96m',
    brightWhite: '\u001b[97m',
    
    // Background colors for emphasis
    bgRed: '\u001b[41m',
    bgYellow: '\u001b[43m',
    bgGreen: '\u001b[42m',
};

// Message type detection patterns
const MESSAGE_PATTERNS = {
    error: /\b(error|failed|failure|exception|fatal|critical|crash|denied|refused|cannot|unable|invalid|missing)\b/i,
    warning: /\b(warn|warning|caution|deprecated|notice|alert)\b/i,
    success: /\b(success|successful|complete|completed|done|finished|ready|started|enabled|loaded|connected|joined)\b/i,
    info: /\b(info|information|status|update|notice|log)\b/i,
    debug: /\b(debug|trace|verbose)\b/i,
    server: /\[Server\]|\[INFO\]|\[WARN\]|\[ERROR\]|\[DEBUG\]/i,
    player: /\b(player|user)\s+(joined|left|connected|disconnected)\b/i,
    command: /\b(command|executed|issued)\b/i,
    permission: /\b(permission|access|denied|granted)\b/i,
};

// Enhanced message processing function
const processConsoleMessage = (message: string): string => {
    if (!message || typeof message !== 'string') {
        return message;
    }

    let processedMessage = message;
    let colorCode = ANSI_COLORS.white; // Default color

    // Check for Minecraft server log levels first
    if (/\[ERROR\]/i.test(message)) {
        colorCode = ANSI_COLORS.brightRed + ANSI_COLORS.bright;
    } else if (/\[WARN\]/i.test(message)) {
        colorCode = ANSI_COLORS.brightYellow;
    } else if (/\[INFO\]/i.test(message)) {
        colorCode = ANSI_COLORS.brightBlue;
    } else if (/\[DEBUG\]/i.test(message)) {
        colorCode = ANSI_COLORS.gray;
    }
    // Check for general message patterns
    else if (MESSAGE_PATTERNS.error.test(message)) {
        colorCode = ANSI_COLORS.brightRed;
    } else if (MESSAGE_PATTERNS.warning.test(message)) {
        colorCode = ANSI_COLORS.brightYellow;
    } else if (MESSAGE_PATTERNS.success.test(message)) {
        colorCode = ANSI_COLORS.brightGreen;
    } else if (MESSAGE_PATTERNS.info.test(message)) {
        colorCode = ANSI_COLORS.brightBlue;
    } else if (MESSAGE_PATTERNS.debug.test(message)) {
        colorCode = ANSI_COLORS.gray;
    } else if (MESSAGE_PATTERNS.player.test(message)) {
        colorCode = ANSI_COLORS.brightCyan;
    } else if (MESSAGE_PATTERNS.command.test(message)) {
        colorCode = ANSI_COLORS.magenta;
    } else if (MESSAGE_PATTERNS.permission.test(message)) {
        colorCode = ANSI_COLORS.brightRed;
    }

    // Apply color and ensure reset at the end
    return colorCode + processedMessage + ANSI_COLORS.reset;
};

// Enhanced error message processing
const processErrorMessage = (message: string): string => {
    if (!message || typeof message !== 'string') {
        return message;
    }
    
    // Use bright red with bold for errors, and red background for critical errors
    const isCritical = /\b(fatal|critical|crash|exception)\b/i.test(message);
    const colorCode = isCritical 
        ? ANSI_COLORS.brightWhite + ANSI_COLORS.bgRed + ANSI_COLORS.bright
        : ANSI_COLORS.brightRed + ANSI_COLORS.bright;
        
    return colorCode + message + ANSI_COLORS.reset;
};

export default () => {
    const server = ServerContext.useStoreState((state) => state.server.data);

    const nodeName = server?.nestName || 'nest';
    const ownerFirstName = server?.id || 'owner';

    const TERMINAL_PRELUDE = `\u001b[1m\u001b[38;2;223;48;80m${nodeName}@${ownerFirstName}~ \u001b[0m`;
    const ref = useRef<HTMLDivElement>(null);
    const terminal = useMemo(() => new Terminal({ ...terminalProps }), []);
    const fitAddon = new FitAddon();
    const searchAddon = new SearchAddon();
    const searchBar = new SearchBarAddon({ searchAddon });
    const webLinksAddon = new WebLinksAddon();
    const scrollDownHelperAddon = new ScrollDownHelperAddon();
    const { connected, instance } = ServerContext.useStoreState((state) => state.socket);
    const [canSendCommands] = usePermissions(['control.console']);
    const serverId = ServerContext.useStoreState((state) => state.server.data!.id);
    const isTransferring = ServerContext.useStoreState((state) => state.server.data!.isTransferring);
    const [history, setHistory] = usePersistedState<string[]>(`${serverId}:command_history`, []);
    const [historyIndex, setHistoryIndex] = useState(-1);
    // SearchBarAddon has hardcoded z-index: 999 :(
    const zIndex = `
    .xterm-search-bar__addon {
        z-index: 10;
    }`;

    const handleConsoleOutput = (line: string, prelude = false) => {
        const cleanLine = line.replace(/(?:\r\n|\r|\n)$/im, '');
        const coloredLine = processConsoleMessage(cleanLine);
        terminal.writeln((prelude ? TERMINAL_PRELUDE : '') + coloredLine);
    };

    const handleTransferStatus = (status: string) => {
        let coloredMessage = '';
        switch (status) {
            // Sent by either the source or target node if a failure occurs.
            case 'failure':
                coloredMessage = processErrorMessage('Transfer has failed.');
                break;
            case 'success':
                coloredMessage = processConsoleMessage('Transfer completed successfully.');
                break;
            case 'starting':
                coloredMessage = processConsoleMessage('Transfer starting...');
                break;
            default:
                coloredMessage = processConsoleMessage(`Transfer status: ${status}`);
        }
        terminal.writeln(TERMINAL_PRELUDE + coloredMessage);
    };

    const handleDaemonErrorOutput = (line: string) => {
        const cleanLine = line.replace(/(?:\r\n|\r|\n)$/im, '');
        const errorMessage = processErrorMessage(cleanLine);
        terminal.writeln(TERMINAL_PRELUDE + errorMessage);
    };

    const handlePowerChangeEvent = (state: string) => {
        let coloredMessage = '';
        switch (state.toLowerCase()) {
            case 'running':
            case 'started':
                coloredMessage = processConsoleMessage(`Server marked as ${state}...`);
                break;
            case 'stopped':
            case 'stopping':
                coloredMessage = ANSI_COLORS.brightYellow + `Server marked as ${state}...` + ANSI_COLORS.reset;
                break;
            case 'offline':
                coloredMessage = ANSI_COLORS.gray + `Server marked as ${state}...` + ANSI_COLORS.reset;
                break;
            default:
                coloredMessage = processConsoleMessage(`Server marked as ${state}...`);
        }
        terminal.writeln(TERMINAL_PRELUDE + coloredMessage);
    };

    const handleCommandKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'ArrowUp') {
            const newIndex = Math.min(historyIndex + 1, history!.length - 1);

            setHistoryIndex(newIndex);
            e.currentTarget.value = history![newIndex] || '';

            // By default up arrow will also bring the cursor to the start of the line,
            // so we'll preventDefault to keep it at the end.
            e.preventDefault();
        }

        if (e.key === 'ArrowDown') {
            const newIndex = Math.max(historyIndex - 1, -1);

            setHistoryIndex(newIndex);
            e.currentTarget.value = history![newIndex] || '';
        }

        const command = e.currentTarget.value;
        if (e.key === 'Enter' && command.length > 0) {
            setHistory((prevHistory) => [command, ...prevHistory!].slice(0, 32));
            setHistoryIndex(-1);

            // Show the command being sent in a distinct color
            const commandOutput = ANSI_COLORS.brightMagenta + '> ' + command + ANSI_COLORS.reset;
            terminal.writeln(TERMINAL_PRELUDE + commandOutput);

            instance && instance.send('send command', command);
            e.currentTarget.value = '';
        }
    };

    useEffect(() => {
        if (connected && ref.current && !terminal.element) {
            terminal.loadAddon(fitAddon);
            terminal.loadAddon(searchAddon);
            terminal.loadAddon(searchBar);
            terminal.loadAddon(webLinksAddon);
            terminal.loadAddon(scrollDownHelperAddon);

            terminal.open(ref.current);
            fitAddon.fit();
            searchBar.addNewStyle(zIndex);

            // Add support for capturing keys
            terminal.attachCustomKeyEventHandler((e: KeyboardEvent) => {
                if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
                    document.execCommand('copy');
                    return false;
                } else if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                    e.preventDefault();
                    searchBar.show();
                    return false;
                } else if (e.key === 'Escape') {
                    searchBar.hidden();
                }
                return true;
            });
        }
    }, [terminal, connected]);

    useEventListener(
        'resize',
        debounce(() => {
            if (terminal.element) {
                fitAddon.fit();
            }
        }, 100)
    );

    useEffect(() => {
        const listeners: Record<string, (s: string) => void> = {
            [SocketEvent.STATUS]: handlePowerChangeEvent,
            [SocketEvent.CONSOLE_OUTPUT]: handleConsoleOutput,
            [SocketEvent.INSTALL_OUTPUT]: handleConsoleOutput,
            [SocketEvent.TRANSFER_LOGS]: handleConsoleOutput,
            [SocketEvent.TRANSFER_STATUS]: handleTransferStatus,
            [SocketEvent.DAEMON_MESSAGE]: (line) => handleConsoleOutput(line, true),
            [SocketEvent.DAEMON_ERROR]: handleDaemonErrorOutput,
        };

        if (connected && instance) {
            // Do not clear the console if the server is being transferred.
            if (!isTransferring) {
                terminal.clear();
            }

            Object.keys(listeners).forEach((key: string) => {
                instance.addListener(key, listeners[key]);
            });
            instance.send(SocketRequest.SEND_LOGS);
        }

        return () => {
            if (instance) {
                Object.keys(listeners).forEach((key: string) => {
                    instance.removeListener(key, listeners[key]);
                });
            }
        };
    }, [connected, instance]);

    return (
        <div className={classNames(styles.terminal, 'relative')}>
            <SpinnerOverlay visible={!connected} size={'large'} />
            <div
                className={classNames(
                    styles.container,
                    styles.overflows_container,
                    { 'rounded-b': !canSendCommands },
                    'bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary'
                )}
            >
                <div className={'h-full'}>
                    <div id={styles.terminal} ref={ref} />
                </div>
            </div>
            {canSendCommands && (
                <div className={classNames('relative', styles.overflows_container,'border border-hyper-primary rounded-b-lg')}>
                    <input
                        className={classNames('peer', styles.command_input, 'bg-hyper-glass text-hyper-primary active:border-hyper-accent focus:border-hyper-accent')}
                        type={'text'}
                        placeholder={'Type a command...'}
                        aria-label={'Console command input.'}
                        disabled={!instance || !connected}
                        onKeyDown={handleCommandKeyDown}
                        autoCorrect={'off'}
                        autoCapitalize={'none'}
                    />
                    <div
                        className={classNames(
                            'text-hyper-accent peer-focus:animate-pulse',
                            styles.command_icon,
                            'hyper-command-icon' // <-- your custom class here
                        )}
                    >
                        <ChevronDoubleRightIcon className={'w-4 h-4'} />
                    </div>
                </div>
            )}
        </div>
    );
};
