import React, { useEffect, useRef, useCallback } from 'react';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { SocketEvent } from '@/components/server/events';
import useWebsocketEvent from '@rolexdev/themes/hyperv1/plugins/useWebsocketEvent';
import { Line } from 'react-chartjs-2';
import { useChart } from '@rolexdev/themes/hyperv1/components/server/console/chart';
import { hexToRgba } from '@/lib/helpers';
import { bytesToString } from '@/lib/formatters';
import { CloudDownloadIcon, CloudUploadIcon } from '@heroicons/react/solid';
import { ShineBorder } from '@rolexdev/themes/hyperv1/components/elements/ShineBorder';
import { Cpu, MemoryStick, Network } from 'lucide-react';

export default () => {
    const status = ServerContext.useStoreState((state) => state.status.value);
    const limits = ServerContext.useStoreState((state) => state.server.data!.limits);
    const previous = useRef<Record<'tx' | 'rx', number>>({ tx: -1, rx: -1 });
    const lastValues = useRef<{ cpu: number; memory: number; networkIn: number; networkOut: number }>({
        cpu: 0,
        memory: 0,
        networkIn: 0,
        networkOut: 0,
    });
    const targetValues = useRef<{ cpu: number; memory: number; networkIn: number; networkOut: number }>({
        cpu: 0,
        memory: 0,
        networkIn: 0,
        networkOut: 0,
    });
    const animationFrameRef = useRef<number>();

    const cpu = useChart('CPU', {
        sets: 1,
        options: {
            maintainAspectRatio: false,
            responsive: true,
            scales: {
                y: {
                    suggestedMax: limits.cpu,
                    ticks: {
                        callback(value) {
                            return `${Number(value).toFixed(2)}%`;
                        },
                    },
                },
            },
        },
    });
    const memory = useChart('Memory', {
        sets: 1,
        options: {
            maintainAspectRatio: false,
            responsive: true,
            scales: {
                y: {
                    suggestedMax: limits.memory,
                    ticks: {
                        callback(value) {
                            return `${value}MiB`;
                        },
                    },
                },
            },
        },
    });
    const network = useChart('Network', {
        sets: 2,
        options: {
            maintainAspectRatio: false,
            responsive: true,
            scales: {
                y: {
                    ticks: {
                        callback(value) {
                            return bytesToString(typeof value === 'string' ? parseInt(value, 10) : value);
                        },
                    },
                },
            },
        },
        callback(opts, index) {
            return {
                ...opts,
                label: !index ? 'Network In' : 'Network Out',
                borderColor: !index ? '#06b6d4' : '#f59e0b', // cyan-500 : amber-500
                backgroundColor: hexToRgba(!index ? '#06b6d4' : '#f59e0b', 0.2),
            };
        },
    });

    // Store chart references
    const chartRefs = useRef({ cpu, memory, network });
    chartRefs.current = { cpu, memory, network };

    // Smooth interpolation function
    const interpolateValues = useCallback(() => {
        const smoothingFactor = 0.15; // Controls how smooth the transition is (lower = smoother)
        
        // Interpolate towards target values
        lastValues.current.cpu += (targetValues.current.cpu - lastValues.current.cpu) * smoothingFactor;
        lastValues.current.memory += (targetValues.current.memory - lastValues.current.memory) * smoothingFactor;
        lastValues.current.networkIn += (targetValues.current.networkIn - lastValues.current.networkIn) * smoothingFactor;
        lastValues.current.networkOut += (targetValues.current.networkOut - lastValues.current.networkOut) * smoothingFactor;

        // Push interpolated values to charts using refs
        chartRefs.current.cpu.push(Number(lastValues.current.cpu.toFixed(2)));
        chartRefs.current.memory.push(Math.floor(lastValues.current.memory));
        chartRefs.current.network.push([lastValues.current.networkIn, lastValues.current.networkOut]);

        // Continue animation
        animationFrameRef.current = requestAnimationFrame(interpolateValues);
    }, []); // Empty dependency array

    // Start/stop continuous animation based on server status
    useEffect(() => {
        if (status === 'offline') {
            if (animationFrameRef.current) {
                cancelAnimationFrame(animationFrameRef.current);
            }
            chartRefs.current.cpu.clear();
            chartRefs.current.memory.clear();
            chartRefs.current.network.clear();
            // Reset values
            lastValues.current = { cpu: 0, memory: 0, networkIn: 0, networkOut: 0 };
            targetValues.current = { cpu: 0, memory: 0, networkIn: 0, networkOut: 0 };
        } else if (!animationFrameRef.current) {
            // Start continuous interpolation only if not already running
            animationFrameRef.current = requestAnimationFrame(interpolateValues);
        }

        return () => {
            if (animationFrameRef.current) {
                cancelAnimationFrame(animationFrameRef.current);
                animationFrameRef.current = undefined;
            }
        };
    }, [status]); // Only depend on status

    useWebsocketEvent(SocketEvent.STATS, (data: string) => {
        let values: any = {};
        try {
            values = JSON.parse(data);
        } catch (e) {
            return;
        }

        // Update target values instead of pushing directly
        targetValues.current.cpu = values.cpu_absolute;
        targetValues.current.memory = Math.floor(values.memory_bytes / 1024 / 1024);
        
        // Fix network variable names - tx is actually outbound, rx is inbound
        const networkIn = previous.current.rx < 0 ? 0 : Math.max(0, values.network.rx_bytes - previous.current.rx);
        const networkOut = previous.current.tx < 0 ? 0 : Math.max(0, values.network.tx_bytes - previous.current.tx);
        
        targetValues.current.networkIn = networkIn;
        targetValues.current.networkOut = networkOut;

        previous.current = { tx: values.network.tx_bytes, rx: values.network.rx_bytes };
    });

    return (
        <div className="flex flex-col lg:flex-row lg:flex-wrap gap-6">
            {/* CPU Load Chart */}
            <div className="relative bg-hyper-card backdrop-blur-sm border border-hyper-accent rounded-2xl p-6 shadow-lg overflow-hidden group hover:shadow-xl transition-all duration-300 h-80 w-full max-w-full lg:flex-1">
                <ShineBorder shineColor={["#df3050"]} borderWidth={2} className="z-[1]" />
                <div className="relative z-[2] h-full flex flex-col">
                    <div className="flex items-center justify-between mb-4 flex-shrink-0">
                        <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center group-hover:bg-hyper-primary-30 transition-colors duration-300">
                                <Cpu size={20} className="text-hyper-accent" />
                            </div>
                            <div>
                                <h3 className="text-lg font-semibold text-hyper-primary">CPU Load</h3>
                                <p className="text-sm text-hyper-muted-foreground">Processor usage in real-time</p>
                            </div>
                        </div>
                    </div>
                    <div className="flex-1 relative min-h-0 w-full overflow-hidden" style={{ height: 'calc(100% - 90px)' }}>
                        <div className="absolute inset-0 w-full h-full">
                            <Line {...cpu.props} />
                        </div>
                    </div>
                </div>
            </div>

            {/* Memory Chart */}
            <div className="relative bg-hyper-card backdrop-blur-sm border border-hyper-accent rounded-2xl p-6 shadow-lg overflow-hidden group hover:shadow-xl transition-all duration-300 h-80 w-full max-w-full lg:flex-1">
                <ShineBorder shineColor={["#df3050"]} borderWidth={2} className="z-[1]" />
                <div className="relative z-[2] h-full flex flex-col">
                    <div className="flex items-center justify-between mb-4 flex-shrink-0">
                        <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center group-hover:bg-hyper-primary-30 transition-colors duration-300">
                                <MemoryStick size={20} className="text-hyper-accent" />
                            </div>
                            <div>
                                <h3 className="text-lg font-semibold text-hyper-primary">Memory</h3>
                                <p className="text-sm text-hyper-muted-foreground">RAM usage in real-time</p>
                            </div>
                        </div>
                    </div>
                    <div className="flex-1 relative min-h-0 w-full overflow-hidden" style={{ height: 'calc(100% - 90px)' }}>
                        <div className="absolute inset-0 w-full h-full">
                            <Line {...memory.props} />
                        </div>
                    </div>
                </div>
            </div>

            {/* Network Chart */}
            <div className="relative bg-hyper-card backdrop-blur-sm border border-hyper-accent rounded-2xl p-6 shadow-lg overflow-hidden group hover:shadow-xl transition-all duration-300 h-80 w-full max-w-full lg:flex-1">
                <ShineBorder shineColor={["#df3050"]} borderWidth={2} className="z-[1]" />
                <div className="relative z-[2] h-full flex flex-col">
                    <div className="flex items-center justify-between mb-4 flex-shrink-0">
                        <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center group-hover:bg-hyper-primary-30 transition-colors duration-300">
                                <Network size={20} className="text-hyper-accent" />
                            </div>
                            <div>
                                <h3 className="text-lg font-semibold text-hyper-primary">Network Activity</h3>
                                <p className="text-sm text-hyper-muted-foreground">Data speed in real-time</p>
                            </div>
                        </div>
                        <div className="flex items-center flex-wrap flex-col gap-2">
                            <div className="flex items-center space-x-2 px-3 py-1 bg-hyper-glass rounded-lg backdrop-blur-sm w-full">
                                <CloudDownloadIcon className="w-4 h-4 text-cyan-400" />
                                <span className="text-xs font-medium text-hyper-secondary">In</span>
                            </div>
                            <div className="flex items-center space-x-2 px-3 py-1 bg-hyper-glass rounded-lg backdrop-blur-sm">
                                <CloudUploadIcon className="w-4 h-4 text-amber-400" />
                                <span className="text-xs font-medium text-hyper-secondary">Out</span>
                            </div>
                        </div>
                    </div>
                    <div className="flex-1 relative min-h-0 w-full overflow-hidden" style={{ height: 'calc(100% - 110px)' }}>
                        <div className="absolute inset-0 w-full h-full">
                            <Line {...network.props} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};
