.stat_block {
    @apply flex items-center rounded shadow-lg relative;
    @apply col-span-3 md:col-span-2 lg:col-span-6;
    @apply px-3 py-2 md:p-3 lg:p-4;

    & > .status_bar {
        @apply w-1 h-full absolute left-0 top-0 rounded-l sm:hidden;
    }

    & > .icon {
        @apply hidden flex-shrink-0 items-center justify-center rounded-lg shadow-md w-12 h-12;
        @apply transition-colors duration-500;
        @apply sm:flex sm:mr-4;

        & > svg {
            @apply w-6 h-6 m-auto;
        }
    }
}

.terminal {
    @apply flex flex-col w-full;
    letter-spacing: normal !important;

    & .overflows_container {
        @apply w-full;
    }

    & > .container {
        @apply rounded-t p-1 sm:p-2 min-h-[16rem] flex-1 font-mono text-sm;

        & #terminal {
            @apply h-full;

            &::-webkit-scrollbar-track {
                @apply w-2;
            }

            &::-webkit-scrollbar-thumb {
                @apply bg-gray-900;
            }
        }
    }

    & .command_icon {
        @apply flex items-center top-0 left-0 absolute z-10 select-none h-full px-3 transition-colors duration-100;
    }

    & .command_input {
        @apply relative px-2 pl-10 pr-4 py-2 w-full font-mono text-sm sm:rounded-b;
        @apply focus:ring-0 outline-none focus-visible:outline-none;
        @apply border-0 border-b-2 border-transparent transition-colors duration-100;
    }
}

.chart_container {
    @apply bg-transparent rounded-2xl shadow-lg relative;
    background: transparent !important;
    border: none !important;
}
