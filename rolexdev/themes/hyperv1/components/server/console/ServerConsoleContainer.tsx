import React, { memo } from 'react';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import ServerContentBlock from '@rolexdev/themes/hyperv1/components/elements/ServerContentBlock';
import isEqual from 'react-fast-compare';
import Spinner from '@/components/elements/Spinner';
import Features from '@rolexdev/themes/hyperv1/components/server/features/Features';
import Console from '@rolexdev/themes/hyperv1/components/server/console/Console';
import StatGraphs from '@rolexdev/themes/hyperv1/components/server/console/StatGraphs';
import { Alert } from '@/components/elements/alert';
import { Monitor } from 'lucide-react';
import { PageTransition, AnimatedCard, AnimatedContainer } from '@rolexdev/themes/hyperv1/components/elements/animations';

export type PowerAction = 'start' | 'stop' | 'restart' | 'kill';

const ServerConsoleContainer = () => {
    const isInstalling = ServerContext.useStoreState((state) => state.server.isInstalling);
    const isTransferring = ServerContext.useStoreState((state) => state.server.data!.isTransferring);
    const eggFeatures = ServerContext.useStoreState((state) => state.server.data!.eggFeatures, isEqual);
    const isNodeUnderMaintenance = ServerContext.useStoreState((state) => state.server.data!.isNodeUnderMaintenance);

    return (
        <ServerContentBlock title={'Console'}>
            <PageTransition>
                {/* Header Section */}
                <AnimatedCard delay={0.1} variant="default" className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                        <Monitor size={20} className="text-hyper-accent" />
                    </div>
                    <div>
                        <h2 className="text-lg font-semibold text-hyper-primary">Server Console</h2>
                        <p className="text-sm text-hyper-muted-foreground">
                            Monitor your server's live output and view performance statistics.
                        </p>
                    </div>
                </AnimatedCard>

                {(isNodeUnderMaintenance || isInstalling || isTransferring) && (
                    <AnimatedCard delay={0.2} variant="default" className={'mb-4'}>
                        <Alert type={'warning'}>
                            {isNodeUnderMaintenance
                                ? 'The node of this server is currently under maintenance and all actions are unavailable.'
                                : isInstalling
                                ? 'This server is currently running its installation process and most actions are unavailable.'
                                : 'This server is currently being transferred to another node and all actions are unavailable.'}
                        </Alert>
                    </AnimatedCard>
                )}
                
                <AnimatedContainer variant="stagger" staggerChildren={0.15}>
                    <AnimatedCard delay={0.3} variant="default" className={'mb-4'}>
                        <div className={'w-full overflow-hidden'}>
                            <Spinner.Suspense>
                                <Console />
                            </Spinner.Suspense>
                        </div>
                    </AnimatedCard>
                    <AnimatedCard delay={0.4} variant="default" className={'w-full overflow-hidden'}>
                        <Spinner.Suspense>
                            <StatGraphs />
                        </Spinner.Suspense>
                    </AnimatedCard>
                    <AnimatedCard delay={0.5} variant="default">
                        <Features enabled={eggFeatures} />
                    </AnimatedCard>
                </AnimatedContainer>
            </PageTransition>
        </ServerContentBlock>
    );
};

export default memo(ServerConsoleContainer, isEqual);
