import React, { useEffect, useState } from 'react';
import { Field as FormikField, Form, Formik, FormikHelpers, useFormikContext } from 'formik';
import { boolean, object, string } from 'yup';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import FormikFieldWrapper from '@/components/elements/FormikFieldWrapper';
import useFlash from '@/plugins/useFlash';
import createServerBackup from '@/api/server/backups/createServerBackup';
import FlashMessageRender from '@/components/FlashMessageRender';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { Textarea } from '@rolexdev/themes/hyperv1/components/elements/Input';
import getServerBackups from '@rolexdev/themes/hyperv1/api/swr/getServerBackups';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import FormikSwitch from '@rolexdev/themes/hyperv1/components/elements/FormikSwitch';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import { Plus, Archive, Lock, Info } from 'lucide-react';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';

interface Values {
    name: string;
    ignored: string;
    isLocked: boolean;
}

export default ({ className }: { className?: string }) => {
    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const { clearFlashes, clearAndAddHttpError } = useFlash();
    const [visible, setVisible] = useState(false);
    const { mutate } = getServerBackups();

    useEffect(() => {
        clearFlashes('backups:create');
    }, [visible]);

    const submit = (values: Values, { setSubmitting }: FormikHelpers<Values>) => {
        clearFlashes('backups:create');
        
        createServerBackup(uuid, values)
            .then((backup) => {
                mutate(
                    (data) => ({ ...data, items: data.items.concat(backup), backupCount: data.backupCount + 1 }),
                    false
                );
                setVisible(false);
            })
            .catch((error) => {
                clearAndAddHttpError({ key: 'backups:create', error });
                setSubmitting(false);
            });
    };

    return (
        <>
            <Dialog
                open={visible}
                onClose={() => setVisible(false)}
                title="Create Server Backup"
                description="Generate a backup of your server files"
            >
                <Formik
                    onSubmit={submit}
                    initialValues={{ name: '', ignored: '', isLocked: false }}
                    validationSchema={object().shape({
                        name: string().max(191),
                        ignored: string(),
                        isLocked: boolean(),
                    })}
                >
                    {({ isSubmitting, submitForm }) => (
                        <Form>
                            <FlashMessageRender byKey={'backups:create'} className="mb-6" />
                            
                            {/* Form Fields */}
                            <div className="space-y-6">
                                <Field
                                    name={'name'}
                                    label={'Backup Name'}
                                    placeholder={'Enter backup name...'}
                                    description={'If provided, the name that should be used to reference this backup.'}
                                    light
                                />

                                <div>
                                    <FormikFieldWrapper
                                        name={'ignored'}
                                        label={'Ignored Files & Directories'}
                                        description={
                                            'Enter the files or folders to ignore while generating this backup. Leave blank to use the contents of the .pteroignore file in the root of the server directory if present. Wildcard matching of files and folders is supported in addition to negating a rule by prefixing the path with an exclamation point.'
                                        }
                                    >
                                        <FormikField 
                                            as={Textarea} 
                                            name={'ignored'} 
                                            rows={6} 
                                            isLight
                                            placeholder="*.log
temp/
cache/
!important.log"
                                            className="font-mono text-sm"
                                        />
                                    </FormikFieldWrapper>
                                </div>

                                <Can action={'backup.delete'}>
                                    <div className="bg-hyper-glass border border-hyper-accent rounded-lg p-4">
                                        <div className="flex items-start gap-3">
                                            <Lock size={16} className="text-hyper-accent flex-shrink-0 mt-0.5" />
                                            <div className="flex-1">
                                                <FormikSwitch
                                                    name={'isLocked'}
                                                    label={'Lock Backup'}
                                                    description={'Prevents this backup from being deleted until explicitly unlocked.'}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </Can>

                                {/* Info Box */}
                                <div className="bg-hyper-sidebar border border-hyper-primary rounded-lg p-4">
                                    <div className="flex items-start gap-3">
                                        <Info size={16} className="text-hyper-accent flex-shrink-0 mt-0.5" />
                                        <div className="text-sm text-hyper-accent">
                                            <p className="font-medium mb-1">Important Information</p>
                                            <ul className="space-y-1 text-xs">
                                                <li>• Backup creation may take some time depending on server size</li>
                                                <li>• The server will continue running during backup creation</li>
                                                <li>• Large files may be excluded to prevent backup failures</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Footer Actions */}
                            <Dialog.Footer>
                                <Button 
                                    type="button" 
                                    isSecondary 
                                    onClick={() => setVisible(false)}
                                    disabled={isSubmitting}
                                    size="small"
                                >
                                    Cancel
                                </Button>
                                <Button 
                                    onClick={submitForm}
                                    disabled={isSubmitting}
                                    isLoading={isSubmitting}
                                    className="flex items-center gap-2"
                                    size="small"
                                >
                                    <Archive size={16} />
                                    Start Backup
                                </Button>
                            </Dialog.Footer>
                        </Form>
                    )}
                </Formik>
            </Dialog>
            
            <Button 
                onClick={() => setVisible(true)}
                className={`flex items-center gap-2 ${className || ''}`}
            >
                <Plus size={16} />
                Create Backup
            </Button>
        </>
    );
};
