import React, { useState } from 'react';
import getBackupDownloadUrl from '@/api/server/backups/getBackupDownloadUrl';
import useFlash from '@/plugins/useFlash';
import SpinnerOverlay from '@/components/elements/SpinnerOverlay';
import deleteBackup from '@/api/server/backups/deleteBackup';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import getServerBackups from '@rolexdev/themes/hyperv1/api/swr/getServerBackups';
import { ServerBackup } from '@/api/server/types';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { restoreServerBackup } from '@/api/server/backups';
import http, { httpErrorToHuman } from '@/api/http';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { StandaloneCheckbox } from '@rolexdev/themes/hyperv1/components/elements/Checkbox';
import { 
    MoreVertical, 
    Download, 
    RotateCcw, 
    Lock, 
    Unlock, 
    Trash2, 
    AlertTriangle 
} from 'lucide-react';

interface Props {
    backup: ServerBackup;
}

export default ({ backup }: Props) => {
    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const setServerFromState = ServerContext.useStoreActions((actions) => actions.server.setServerFromState);
    const [modal, setModal] = useState('');
    const [loading, setLoading] = useState(false);
    const [truncate, setTruncate] = useState(false);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const { clearFlashes, clearAndAddHttpError } = useFlash();
    const { mutate } = getServerBackups();

    const doDownload = () => {
        setLoading(true);
        clearFlashes('backups');
        getBackupDownloadUrl(uuid, backup.uuid)
            .then((url) => {
                // @ts-expect-error this is valid
                window.location = url;
            })
            .catch((error) => {
                console.error(error);
                clearAndAddHttpError({ key: 'backups', error });
            })
            .then(() => setLoading(false));
    };

    const doDeletion = () => {
        setLoading(true);
        clearFlashes('backups');
        deleteBackup(uuid, backup.uuid)
            .then(() =>
                mutate(
                    (data) => ({
                        ...data,
                        items: data.items.filter((b) => b.uuid !== backup.uuid),
                        backupCount: data.backupCount - 1,
                    }),
                    false
                )
            )
            .catch((error) => {
                console.error(error);
                clearAndAddHttpError({ key: 'backups', error });
                setLoading(false);
                setModal('');
            });
    };

    const doRestorationAction = () => {
        setLoading(true);
        clearFlashes('backups');
        restoreServerBackup(uuid, backup.uuid, truncate)
            .then(() =>
                setServerFromState((s) => ({
                    ...s,
                    status: 'restoring_backup',
                }))
            )
            .catch((error) => {
                console.error(error);
                clearAndAddHttpError({ key: 'backups', error });
            })
            .then(() => setLoading(false))
            .then(() => setModal(''));
    };

    const onLockToggle = () => {
        if (backup.isLocked && modal !== 'unlock') {
            return setModal('unlock');
        }

        http.post(`/api/client/servers/${uuid}/backups/${backup.uuid}/lock`)
            .then(() =>
                mutate(
                    (data) => ({
                        ...data,
                        items: data.items.map((b) =>
                            b.uuid !== backup.uuid
                                ? b
                                : {
                                      ...b,
                                      isLocked: !b.isLocked,
                                  }
                        ),
                    }),
                    false
                )
            )
            .catch((error) => alert(httpErrorToHuman(error)))
            .then(() => setModal(''));
    };

    return (
        <>
            <Dialog
                open={modal === 'unlock'}
                onClose={() => setModal('')}
                title={`Unlock "${backup.name}"`}
            >
                <div className="text-hyper-secondary">
                    This backup will no longer be protected from automated or accidental deletions.
                </div>
                
                <Dialog.Footer>
                    <Button 
                        size='small' 
                        isSecondary 
                        onClick={() => setModal('')}
                        disabled={loading}
                    >
                        Cancel
                    </Button>
                    <Button 
                        size='small'
                        onClick={onLockToggle}
                        disabled={loading}
                    >
                        Unlock
                    </Button>
                </Dialog.Footer>
            </Dialog>
            
            <Dialog
                open={modal === 'restore'}
                onClose={() => setModal('')}
                title={`Restore "${backup.name}"`}
            >
                <div className="space-y-4">
                    <div className="bg-hyper-glass border border-hyper-accent rounded-lg p-4">
                        <div className="flex items-start gap-3">
                            <AlertTriangle size={16} className="text-hyper-accent flex-shrink-0 mt-0.5" />
                            <div className="text-sm text-hyper-accent">
                                <p className="font-medium mb-2">Important Information</p>
                                <ul className="space-y-1 text-xs">
                                    <li>• Your server will be stopped during restoration</li>
                                    <li>• You will not be able to control the power state</li>
                                    <li>• File manager access will be disabled</li>
                                    <li>• No additional backups can be created until completed</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div className="bg-hyper-sidebar border border-hyper-primary rounded-lg p-4">
                        <label htmlFor={'restore_truncate'} className="flex items-start gap-3 cursor-pointer">
                            <StandaloneCheckbox
                                id={'restore_truncate'}
                                checked={truncate}
                                onChange={() => setTruncate((s) => !s)}
                                className="mt-0.5"
                            />
                            <div className="text-sm">
                                <span className="font-medium text-hyper-primary block">Delete all files before restoring</span>
                                <span className="text-hyper-accent">This will permanently remove all existing files before restoring the backup.</span>
                            </div>
                        </label>
                    </div>
                </div>
                
                <Dialog.Footer>
                    <Button 
                        size='small' 
                        isSecondary 
                        onClick={() => setModal('')}
                        disabled={loading}
                    >
                        Cancel
                    </Button>
                    <Button 
                        size='small'
                        onClick={() => doRestorationAction()}
                        disabled={loading}
                    >
                        Restore
                    </Button>
                </Dialog.Footer>
            </Dialog>
            
            <Dialog
                open={modal === 'delete'}
                onClose={() => setModal('')}
                title={`Delete "${backup.name}"`}
            >
                <div className="bg-hyper-glass border border-red-400/20 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                        <AlertTriangle size={16} className="text-red-400 flex-shrink-0 mt-0.5" />
                        <div className="text-sm">
                            <p className="text-red-400 font-medium mb-1">Permanent Deletion</p>
                            <p className="text-hyper-accent">This is a permanent operation. The backup cannot be recovered once deleted.</p>
                        </div>
                    </div>
                </div>
                
                <Dialog.Footer>
                    <Button 
                        size='small' 
                        isSecondary 
                        onClick={() => setModal('')}
                        disabled={loading}
                    >
                        Cancel
                    </Button>
                    <Button 
                        size='small'
                        color="red"
                        onClick={doDeletion}
                        disabled={loading}
                    >
                        Delete Backup
                    </Button>
                </Dialog.Footer>
            </Dialog>
            
            <SpinnerOverlay visible={loading} fixed />
            
            {backup.isSuccessful ? (
                <div className="relative" tabIndex={0} onBlur={() => setTimeout(() => setDropdownOpen(false), 100)}>
                    <button
                        onClick={() => setDropdownOpen(v => !v)}
                        className="w-8 h-8 flex items-center justify-center rounded-lg text-hyper-accent hover:bg-hyper-glass transition-colors"
                        aria-haspopup="listbox"
                        aria-expanded={dropdownOpen}
                        type="button"
                    >
                        <MoreVertical size={16} />
                    </button>
                    
                    {dropdownOpen && (
                        <div className="z-[9999] absolute right-0 mt-2 w-[160px] bg-hyper-sidebar backdrop-blur-lg border border-hyper-accent rounded-lg shadow-lg max-h-60 overflow-y-auto">
                            <Can action={'backup.download'}>
                                <div 
                                    className="px-3 py-2 cursor-pointer hover:bg-hyper-primary-10 truncate text-hyper-primary flex items-center transition-colors"
                                    onMouseDown={(e) => {
                                        e.preventDefault();
                                        doDownload();
                                        setDropdownOpen(false);
                                    }}
                                >
                                    <Download size={14} />
                                    <span className="ml-2">Download</span>
                                </div>
                            </Can>
                            <Can action={'backup.restore'}>
                                <div 
                                    className="px-3 py-2 cursor-pointer hover:bg-hyper-primary-10 truncate text-hyper-primary flex items-center transition-colors"
                                    onMouseDown={(e) => {
                                        e.preventDefault();
                                        setModal('restore');
                                        setDropdownOpen(false);
                                    }}
                                >
                                    <RotateCcw size={14} />
                                    <span className="ml-2">Restore</span>
                                </div>
                            </Can>
                            <Can action={'backup.delete'}>
                                <>
                                    <div 
                                        className="px-3 py-2 cursor-pointer hover:bg-hyper-primary-10 truncate text-hyper-primary flex items-center transition-colors"
                                        onMouseDown={(e) => {
                                            e.preventDefault();
                                            onLockToggle();
                                            setDropdownOpen(false);
                                        }}
                                    >
                                        {backup.isLocked ? <Unlock size={14} /> : <Lock size={14} />}
                                        <span className="ml-2">{backup.isLocked ? 'Unlock' : 'Lock'}</span>
                                    </div>
                                    {!backup.isLocked && (
                                        <div 
                                            className="px-3 py-2 cursor-pointer hover:bg-red-400/10 hover:text-red-400 truncate text-hyper-primary flex items-center transition-colors"
                                            onMouseDown={(e) => {
                                                e.preventDefault();
                                                setModal('delete');
                                                setDropdownOpen(false);
                                            }}
                                        >
                                            <Trash2 size={14} />
                                            <span className="ml-2">Delete</span>
                                        </div>
                                    )}
                                </>
                            </Can>
                        </div>
                    )}
                </div>
            ) : (
                <button
                    onClick={() => setModal('delete')}
                    className="w-8 h-8 flex items-center justify-center rounded-lg text-red-400 hover:bg-red-400/10 transition-colors"
                >
                    <Trash2 size={16} />
                </button>
            )}
        </>
    );
};
