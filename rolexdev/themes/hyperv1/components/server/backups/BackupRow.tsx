import React, { useState } from 'react';
import { format, formatDistanceToNow } from 'date-fns';
import Spinner from '@/components/elements/Spinner';
import { bytesToString } from '@/lib/formatters';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import useWebsocketEvent from '@rolexdev/themes/hyperv1/plugins/useWebsocketEvent';
import BackupContextMenu from '@rolexdev/themes/hyperv1/components/server/backups/BackupContextMenu';
import getServerBackups from '@rolexdev/themes/hyperv1/api/swr/getServerBackups';
import { ServerBackup } from '@/api/server/types';
import { SocketEvent } from '@/components/server/events';
import { 
    Archive, 
    Lock, 
    AlertTriangle, 
    CheckCircle, 
    Clock,
    MoreVertical 
} from 'lucide-react';

interface Props {
    backup: ServerBackup;
    className?: string;
}

export default ({ backup, className }: Props) => {
    const { mutate } = getServerBackups();
    const [showFullTime, setShowFullTime] = useState(false);

    useWebsocketEvent(`${SocketEvent.BACKUP_COMPLETED}:${backup.uuid}` as SocketEvent, (data) => {
        try {
            const parsed = JSON.parse(data);

            mutate(
                (data) => ({
                    ...data,
                    items: data.items.map((b) =>
                        b.uuid !== backup.uuid
                            ? b
                            : {
                                  ...b,
                                  isSuccessful: parsed.is_successful || true,
                                  checksum: (parsed.checksum_type || '') + ':' + (parsed.checksum || ''),
                                  bytes: parsed.file_size || 0,
                                  completedAt: new Date(),
                              }
                    ),
                }),
                false
            );
        } catch (e) {
            console.warn(e);
        }
    });

    const getStatusIcon = () => {
        if (backup.completedAt === null) {
            return <Spinner size="small" />;
        }
        
        if (!backup.isSuccessful) {
            return <AlertTriangle size={20} className="text-red-400" />;
        }
        
        if (backup.isLocked) {
            return <Lock size={20} className="text-yellow-400" />;
        }
        
        return <CheckCircle size={20} className="text-green-400" />;
    };

    const getStatusLabel = () => {
        if (backup.completedAt === null) {
            return 'In Progress';
        }
        
        if (!backup.isSuccessful) {
            return 'Failed';
        }
        
        if (backup.isLocked) {
            return 'Locked';
        }
        
        return 'Completed';
    };

    const getStatusColor = () => {
        if (backup.completedAt === null) {
            return 'text-blue-400 bg-blue-400/10 border-blue-400/20';
        }
        
        if (!backup.isSuccessful) {
            return 'text-red-400 bg-red-400/10 border-red-400/20';
        }
        
        if (backup.isLocked) {
            return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
        }
        
        return 'text-green-400 bg-green-400/10 border-green-400/20';
    };

    return (
        <div className={`bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-6 w-[342px] transition-all duration-200 hover:border-hyper-accent hover:scale-[1.02] hover:shadow-lg hover:shadow-hyper-accent/10 ${className || ''}`}>
            {/* Header Section */}
            <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                    <Archive size={20} className="text-hyper-accent" />
                </div>
                <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-semibold text-hyper-primary truncate">{backup.name}</h3>
                    <p className="text-sm text-hyper-accent">Server Backup</p>
                </div>
            </div>

            {/* Status Section */}
            <div className="mb-4">
                <div className="flex items-center gap-3 mb-3">
                    <div className="flex-shrink-0">
                        {getStatusIcon()}
                    </div>
                    <span className={`inline-flex items-center gap-1.5 px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor()}`}>
                        {getStatusLabel()}
                    </span>
                    {backup.completedAt !== null && backup.isSuccessful && (
                        <span className="text-sm text-hyper-accent font-medium">
                            {bytesToString(backup.bytes)}
                        </span>
                    )}
                </div>
                
                {/* Checksum */}
                {backup.checksum && (
                    <div className="mb-3">
                        <label className="block text-sm font-medium text-hyper-foreground mb-2">Checksum</label>
                            <code className="block text-xs text-hyper-accent bg-hyper-background border border-hyper-primary rounded p-2 font-mono break-all">
                                {backup.checksum}
                            </code>
                    </div>
                )}
            </div>
            
            {/* Creation Time */}
            <div className="mb-6">
                <label className="block text-sm font-medium text-hyper-foreground mb-2">Created</label>
                <div className="flex items-center gap-2 text-sm">
                    <Clock size={14} className="text-hyper-accent" />
                        <button
                            className="text-hyper-primary hover:text-hyper-accent transition-colors"
                            onMouseEnter={() => setShowFullTime(true)}
                            onMouseLeave={() => setShowFullTime(false)}
                        >
                            {showFullTime 
                                ? format(backup.createdAt, 'MMM dd, yyyy HH:mm:ss')
                                : formatDistanceToNow(backup.createdAt, { includeSeconds: true, addSuffix: true })
                            }
                        </button>
                </div>
            </div>

            {/* Actions Menu */}
            <div className="flex justify-end pt-4 border-t border-hyper-primary">
                <Can action={['backup.download', 'backup.restore', 'backup.delete']} matchAny>
                    {!backup.completedAt ? (
                        <div className="w-8 h-8 flex items-center justify-center">
                            <MoreVertical size={16} className="text-transparent" />
                        </div>
                    ) : (
                        <BackupContextMenu backup={backup} />
                    )}
                </Can>
            </div>
        </div>
    );
};
