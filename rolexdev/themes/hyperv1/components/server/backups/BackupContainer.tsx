import React, { useContext, useEffect, useState } from 'react';
import Spinner from '@/components/elements/Spinner';
import useFlash from '@/plugins/useFlash';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import CreateBackupButton from '@rolexdev/themes/hyperv1/components/server/backups/CreateBackupButton';
import FlashMessageRender from '@/components/FlashMessageRender';
import BackupRow from '@rolexdev/themes/hyperv1/components/server/backups/BackupRow';
import getServerBackups, { Context as ServerBackupContext } from '@rolexdev/themes/hyperv1/api/swr/getServerBackups';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import ServerContentBlock from '@rolexdev/themes/hyperv1/components/elements/ServerContentBlock';
import PaginationFooter from '@rolexdev/themes/hyperv1/components/elements/table/PaginationFooter';
import { Archive, Plus, AlertCircle } from 'lucide-react';
import { PageTransition, AnimatedCard, AnimatedContainer } from '@rolexdev/themes/hyperv1/components/elements/animations';

const BackupContainer = () => {
    const { page, setPage } = useContext(ServerBackupContext);
    const { clearFlashes, clearAndAddHttpError } = useFlash();
    const { data: backups, error, isValidating } = getServerBackups();

    const backupLimit = ServerContext.useStoreState((state) => state.server.data!.featureLimits.backups);

    useEffect(() => {
        if (!error) {
            clearFlashes('backups');
            return;
        }
        clearAndAddHttpError({ error, key: 'backups' });
    }, [error]);

    if (!backups || (error && isValidating)) {
        return <Spinner size={'large'} centered />;
    }

    const handlePageSelect = (newPage: number, perPage: number) => {
        setPage(newPage);
    };

    return (
        <PageTransition>
            <ServerContentBlock title={'Backups'}>
                <FlashMessageRender byKey={'backups'} className="mb-6" />
                
                <AnimatedContainer staggerChildren={0.1}>
                    {/* Header Section */}
                    <AnimatedCard>
                        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
                            <div className="flex items-center gap-3">
                                <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                                    <Archive size={20} className="text-hyper-accent" />
                                </div>
                                <div>
                                    <h2 className="text-lg font-semibold text-hyper-primary">Server Backups</h2>
                                    <p className="text-sm text-hyper-muted-foreground">
                                        Manage your server backups and create new ones to protect your data.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </AnimatedCard>

                    {/* Backup Limit Warning */}
                    {backupLimit === 0 && (
                        <AnimatedCard>
                            <div className="bg-hyper-glass border border-hyper-accent rounded-lg p-4 mb-6">
                                <div className="flex items-start gap-3">
                                    <AlertCircle size={20} className="text-hyper-accent flex-shrink-0 mt-0.5" />
                                    <div>
                                        <h3 className="text-sm font-semibold text-hyper-primary mb-1">Backups Disabled</h3>
                                        <p className="text-sm text-hyper-accent">
                                            Backups cannot be created for this server because the backup limit is set to 0.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </AnimatedCard>
                    )}

                    {/* Backups List */}
                    <AnimatedCard>
                        <div className="flex flex-wrap gap-6 justify-center lg:justify-start mb-6">
                            {!backups.items.length ? (
                                backupLimit > 0 ? (
                                    <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-8 text-center w-[342px] mx-auto">
                                        <div className="w-16 h-16 rounded-lg bg-hyper-primary-10 flex items-center justify-center mx-auto mb-4">
                                            <Archive size={32} className="text-hyper-accent" />
                                        </div>
                                        <h3 className="text-lg font-semibold text-hyper-primary mb-2">No Backups Yet</h3>
                                        <p className="text-sm text-hyper-accent mb-6">
                                            {page > 1
                                                ? "Looks like we've run out of backups to show you, try going back a page."
                                                : 'It looks like there are no backups currently stored for this server. Create your first backup to get started.'
                                            }
                                        </p>
                                        <Can action={'backup.create'}>
                                            {backupLimit > backups.backupCount && (
                                                <CreateBackupButton />
                                            )}
                                        </Can>
                                    </div>
                                ) : null
                            ) : (
                                <>
                                    {backups.items.map((backup) => (
                                        <BackupRow key={backup.uuid} backup={backup} />
                                    ))}
                                    
                                    {/* Add New Backup Card */}
                                    <Can action={'backup.create'}>
                                        {backupLimit > 0 ? (
                                            backupLimit > backups.backupCount ? (
                                                <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary border-dashed rounded-lg p-6 w-[342px] transition-all duration-200 hover:border-hyper-accent flex flex-col items-center justify-center text-center">
                                                    <div className="w-16 h-16 rounded-lg bg-hyper-primary-10 flex items-center justify-center mx-auto mb-4">
                                                        <Plus size={32} className="text-hyper-accent" />
                                                    </div>
                                                    <h3 className="text-lg font-semibold text-hyper-primary mb-2">Add New Backup</h3>
                                                    <div className="text-xs text-hyper-accent mb-3 bg-hyper-background border border-hyper-primary rounded px-2 py-1">
                                                        {backups.backupCount} of {backupLimit} backups used
                                                    </div>
                                                    <p className="text-sm text-hyper-accent mb-6">
                                                        Create a backup of your server files to protect your data and enable easy restoration.
                                                    </p>
                                                    <CreateBackupButton />
                                                </div>
                                            ) : (
                                                <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary border-dashed rounded-lg p-6 w-[342px] transition-all duration-200 flex flex-col items-center justify-center text-center opacity-60">
                                                    <div className="w-16 h-16 rounded-lg bg-hyper-primary-10 flex items-center justify-center mx-auto mb-4">
                                                        <AlertCircle size={32} className="text-hyper-accent" />
                                                    </div>
                                                    <h3 className="text-lg font-semibold text-hyper-primary mb-2">Backup Limit Reached</h3>
                                                    <div className="text-xs text-hyper-accent mb-3 bg-hyper-background border border-hyper-primary rounded px-2 py-1">
                                                        {backups.backupCount} of {backupLimit} backups used
                                                    </div>
                                                    <p className="text-sm text-hyper-accent">
                                                        You've reached the maximum of {backupLimit} backup{backupLimit !== 1 ? 's' : ''} for this server. Delete an old backup to create a new one.
                                                    </p>
                                                </div>
                                            )
                                        ) : null}
                                    </Can>
                                </>
                            )
                        }
                        </div>
                    </AnimatedCard>

                    {/* Pagination */}
                    {backups.items.length > 0 && backups.pagination && backups.pagination.totalPages > 1 && (
                        <AnimatedCard>
                            <PaginationFooter
                                pagination={backups.pagination}
                                onPageSelect={handlePageSelect}
                            />
                        </AnimatedCard>
                    )}
                </AnimatedContainer>
            </ServerContentBlock>
        </PageTransition>
    );
};

export default () => {
    const [page, setPage] = useState<number>(1);
    return (
        <ServerBackupContext.Provider value={{ page, setPage }}>
            <BackupContainer />
        </ServerBackupContext.Provider>
    );
};
