import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import reinstallServer from '@/api/server/reinstallServer';
import { Actions, useStoreActions } from 'easy-peasy';
import { ApplicationStore } from '@/state';
import { httpErrorToHuman } from '@/api/http';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { AlertTriangle, RotateCcw } from 'lucide-react';

// Custom confirmation dialog that matches the TOTP dialog design
interface ConfirmDialogProps {
    open: boolean;
    onClose: () => void;
    onConfirm: () => void;
    title: string;
    children: React.ReactNode;
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({ open, onClose, onConfirm, title, children }) => {
    if (!open) return null;
    
    const dialogContent = (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
            <div className="fixed inset-0 bg-black bg-opacity-50" onClick={onClose} />
            <div className="relative max-w-md w-fit text-hyper-primary rounded-xl bg-hyper-card backdrop-blur-lg border border-hyper-primary p-6 max-h-fit mx-4">
                <div className="mb-4">
                    <h1 className="text-xl font-bold text-hyper-primary">{title}</h1>
                </div>
                <div className="mb-6">
                    <p className="text-sm text-hyper-secondary leading-relaxed">
                        {children}
                    </p>
                </div>
                <div className="flex justify-end gap-2">
                    <Button
                        size="small"
                        isSecondary
                        onClick={onClose}
                        type="button"
                    >
                        Cancel
                    </Button>
                    <Button
                        size="small"
                        color="red"
                        onClick={onConfirm}
                        className="flex items-center gap-2"
                    >
                        <RotateCcw size={16} />
                        Yes, reinstall server
                    </Button>
                </div>
            </div>
        </div>
    );
    
    // Use portal to render at document body level (like TOTP dialog)
    return createPortal(dialogContent, document.body);
};

export default () => {
    const uuid = ServerContext.useStoreState((state: any) => state.server.data!.uuid);
    const [modalVisible, setModalVisible] = useState(false);
    const { addFlash, clearFlashes } = useStoreActions((actions: Actions<ApplicationStore>) => actions.flashes);

    const reinstall = () => {
        clearFlashes('settings');
        reinstallServer(uuid)
            .then(() => {
                addFlash({
                    key: 'settings',
                    type: 'success',
                    message: 'Your server has begun the reinstallation process.',
                });
            })
            .catch((error: any) => {
                console.error(error);
                addFlash({ key: 'settings', type: 'error', message: httpErrorToHuman(error) });
            })
            .then(() => setModalVisible(false));
    };

    useEffect(() => {
        clearFlashes();
    }, []);

    return (
        <div className="relative">
            <ConfirmDialog
                open={modalVisible}
                title="Confirm server reinstallation"
                onClose={() => setModalVisible(false)}
                onConfirm={reinstall}
            >
                Your server will be stopped and some files may be deleted or modified during this process, are you sure you wish to continue?
            </ConfirmDialog>
            
            <div className="bg-hyper-glass border border-hyper-accent rounded-lg p-4 mb-6">
                <div className="flex items-start gap-3">
                    <div className="w-6 h-6 rounded-full bg-hyper-accent flex items-center justify-center flex-shrink-0 mt-0.5">
                        <AlertTriangle size={14} className="text-hyper-primary" />
                    </div>
                    <div className="flex-1">
                        <p className="text-sm text-hyper-secondary leading-relaxed">
                            Reinstalling your server will stop it, and then re-run the installation script that initially set it up.
                            <span className="block mt-2 font-medium text-hyper-accent">
                                Some files may be deleted or modified during this process, please back up your data before continuing.
                            </span>
                        </p>
                    </div>
                </div>
            </div>
            
            <div className="flex justify-end">
                <Button 
                    color="red"
                    isSecondary
                    size="small"
                    onClick={() => setModalVisible(true)}
                    className="flex items-center gap-2"
                >
                    <RotateCcw size={16} />
                    Reinstall Server
                </Button>
            </div>
        </div>
    );
};
