import React from 'react';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { Field as FormikField, Form, Formik, FormikHelpers, useFormikContext } from 'formik';
import { Actions, useStoreActions } from 'easy-peasy';
import renameServer from '@/api/server/renameServer';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import { object, string } from 'yup';
import SpinnerOverlay from '@/components/elements/SpinnerOverlay';
import { ApplicationStore } from '@/state';
import { httpErrorToHuman } from '@/api/http';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { Edit3 } from 'lucide-react';

interface Values {
    name: string;
    description: string;
}

const RenameServerBox = () => {
    const { isSubmitting } = useFormikContext<Values>();

    return (
        <div className="relative">
            <SpinnerOverlay visible={isSubmitting} />
            <Form className="space-y-6">
                <div>
                    <Field 
                        light
                        id={'name'} 
                        name={'name'} 
                        label={'Server Name'} 
                        type={'text'} 
                    />
                </div>
                
                <div>
                    <Field
                        light
                        multiline
                        name={'description'}
                        label={'Server Description'}
                        placeholder="Enter server description..."
                    />
                </div>
                
                <div className="flex justify-end pt-2">
                    <Button 
                        type={'submit'} 
                        size="small"
                        className="flex items-center gap-2"
                        isLoading={isSubmitting}
                    >
                        <Edit3 size={16} />
                        Save Changes
                    </Button>
                </div>
            </Form>
        </div>
    );
};

export default () => {
    const server = ServerContext.useStoreState((state) => state.server.data!);
    const setServer = ServerContext.useStoreActions((actions) => actions.server.setServer);
    const { addError, clearFlashes } = useStoreActions((actions: Actions<ApplicationStore>) => actions.flashes);

    const submit = ({ name, description }: Values, { setSubmitting }: FormikHelpers<Values>) => {
        clearFlashes('settings');
        renameServer(server.uuid, name, description)
            .then(() => setServer({ ...server, name, description }))
            .catch((error) => {
                console.error(error);
                addError({ key: 'settings', message: httpErrorToHuman(error) });
            })
            .then(() => setSubmitting(false));
    };

    return (
        <Formik
            onSubmit={submit}
            initialValues={{
                name: server.name,
                description: server.description,
            }}
            validationSchema={object().shape({
                name: string().required().min(1),
                description: string().nullable(),
            })}
        >
            <RenameServerBox />
        </Formik>
    );
};
