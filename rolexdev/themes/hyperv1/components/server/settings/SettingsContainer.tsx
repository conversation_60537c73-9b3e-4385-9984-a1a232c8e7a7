import React from 'react';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { useStoreState } from 'easy-peasy';
import RenameServerBox from '@rolexdev/themes/hyperv1/components/server/settings/RenameServerBox';
import FlashMessageRender from '@/components/FlashMessageRender';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import ReinstallServerBox from '@rolexdev/themes/hyperv1/components/server/settings/ReinstallServerBox';
import ServerContentBlock from '@rolexdev/themes/hyperv1/components/elements/ServerContentBlock';
import isEqual from 'react-fast-compare';
import CopyOnClick from '@/components/elements/CopyOnClick';
import { ip } from '@/lib/formatters';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { Copy, Server, Terminal, ExternalLink, Edit3, RotateCcw, Settings } from 'lucide-react';
import Tooltip from '@rolexdev/themes/hyperv1/components/elements/tooltip/Tooltip';
import { PageTransition, AnimatedCard, AnimatedContainer } from '@rolexdev/themes/hyperv1/components/elements/animations';

export default () => {
    const username = useStoreState((state) => state.user.data!.username);
    const id = ServerContext.useStoreState((state) => state.server.data!.id);
    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const node = ServerContext.useStoreState((state) => state.server.data!.node);
    const sftp = ServerContext.useStoreState((state) => state.server.data!.sftpDetails, isEqual);
    const eggName = ServerContext.useStoreState((state) => state.server.data!.eggName);
    const nestName = ServerContext.useStoreState((state) => state.server.data!.nestName);

    return (
        <ServerContentBlock title={'Settings'}>
            <PageTransition>
                {/* Header Section */}
                <AnimatedCard delay={0.1} variant="default" className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                        <Settings size={20} className="text-hyper-accent" />
                    </div>
                    <div>
                        <h2 className="text-lg font-semibold text-hyper-primary">Server Settings</h2>
                        <p className="text-sm text-hyper-muted-foreground">
                            Configure server details, access SFTP, and manage server settings.
                        </p>
                    </div>
                </AnimatedCard>

                <FlashMessageRender byKey={'settings'} className="mb-6" />
                
                <AnimatedContainer variant="stagger" staggerChildren={0.15} className="flex flex-wrap gap-6 justify-center lg:justify-start">
                    {/* SFTP Details Section */}
                    <Can action={'file.sftp'}>
                        <AnimatedCard delay={0.2} variant="hover" className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-6 w-[342px] transition-all duration-200 hover:border-hyper-accent hover:scale-[1.02] hover:shadow-lg hover:shadow-hyper-accent/10">
                            <div className="flex items-center gap-3 mb-6">
                                <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                                    <Terminal size={20} className="text-hyper-accent" />
                                </div>
                                <div className="flex-1 min-w-0">
                                    <h3 className="text-lg font-semibold text-hyper-primary">SFTP Details</h3>
                                    <p className="text-sm text-hyper-accent">Server File Access</p>
                                </div>
                            </div>
                            
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-hyper-foreground mb-2">
                                        Server Address
                                    </label>
                                    <CopyOnClick text={`sftp://${ip(sftp.ip)}:${sftp.port}`}>
                                        <div className="relative group">
                                            <input
                                                type="text"
                                                value={`sftp://${ip(sftp.ip)}:${sftp.port}`}
                                                readOnly
                                                className="appearance-none outline-none w-full min-w-0 p-3 rounded-md text-sm transition-all duration-150 resize-none shadow-none focus:ring-0 h-10 bg-hyper-background text-hyper-foreground border focus:border-2 border-hyper-primary hover:border-hyper-accent focus:outline-none disabled:bg-neutral-100 font-medium hyper-input font-mono cursor-pointer"
                                            />
                                            <Copy size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-hyper-muted-foreground group-hover:text-hyper-accent transition-colors cursor-pointer" />
                                        </div>
                                    </CopyOnClick>
                                </div>
                                
                                <div>
                                    <label className="block text-sm font-medium text-hyper-foreground mb-2">
                                        Username
                                    </label>
                                    <CopyOnClick text={`${username}.${id}`}>
                                        <div className="relative group">
                                            <input
                                                type="text"
                                                value={`${username}.${id}`}
                                                readOnly
                                                className="appearance-none outline-none w-full min-w-0 p-3 rounded-md text-sm transition-all duration-150 resize-none shadow-none focus:ring-0 h-10 bg-hyper-background text-hyper-foreground border focus:border-2 border-hyper-primary hover:border-hyper-accent focus:outline-none disabled:bg-neutral-100 font-medium hyper-input font-mono cursor-pointer"
                                            />
                                            <Copy size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-hyper-muted-foreground group-hover:text-hyper-accent transition-colors cursor-pointer" />
                                        </div>
                                    </CopyOnClick>
                                </div>
                                
                                <div className="bg-hyper-glass border border-hyper-accent rounded-lg p-3">
                                    <div className="flex items-start gap-3">
                                        <div className="w-1 h-6 bg-hyper-accent rounded-full flex-shrink-0 mt-0.5"></div>
                                        <div className="flex-1">
                                            <p className="text-xs text-hyper-foreground mb-2">
                                                Your SFTP password is the same as the password you use to access this panel.
                                            </p>
                                            <Tooltip content="Open SFTP Client" placement="top">
                                                <a href={`sftp://${username}.${id}@${ip(sftp.ip)}:${sftp.port}`}>
                                                    <Button isSecondary size="small" className="flex items-center gap-2">
                                                        <ExternalLink size={14} />
                                                        Launch SFTP
                                                    </Button>
                                                </a>
                                            </Tooltip>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </AnimatedCard>
                    </Can>

                    {/* Debug Information Section */}
                    <AnimatedCard delay={0.3} variant="hover" className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-6 w-[342px] transition-all duration-200 hover:border-hyper-accent hover:scale-[1.02] hover:shadow-lg hover:shadow-hyper-accent/10">
                        <div className="flex items-center gap-3 mb-6">
                            <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                                <Server size={20} className="text-hyper-accent" />
                            </div>
                            <div className="flex-1 min-w-0">
                                <h3 className="text-lg font-semibold text-hyper-primary">Support Information</h3>
                                <p className="text-sm text-hyper-accent">Server Details</p>
                            </div>
                        </div>
                        
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-hyper-foreground">Node</span>
                                <code className="px-2 py-1 bg-hyper-background border border-hyper-primary rounded text-hyper-accent font-mono text-xs">
                                    {node}
                                </code>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-hyper-foreground">Nest</span>
                                <code className="px-2 py-1 bg-hyper-background border border-hyper-primary rounded text-hyper-accent font-mono text-xs">
                                    {nestName}
                                </code>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-hyper-foreground">Egg</span>
                                <code className="px-2 py-1 bg-hyper-background border border-hyper-primary rounded text-hyper-accent font-mono text-xs">
                                    {eggName}
                                </code>
                            </div>
                            
                            <CopyOnClick text={uuid}>
                                <div className="flex items-center justify-between group cursor-pointer hover:bg-hyper-glass transition-colors rounded-lg p-2 -m-2">
                                    <span className="text-sm font-medium text-hyper-foreground">Server ID</span>
                                    <div className="flex items-center gap-2">
                                        <code className="px-2 py-1 bg-hyper-background border border-hyper-primary rounded text-hyper-accent font-mono text-xs truncate max-w-[120px]">
                                            {uuid}
                                        </code>
                                        <Copy size={14} className="text-hyper-muted-foreground group-hover:text-hyper-accent transition-colors" />
                                    </div>
                                </div>
                            </CopyOnClick>
                        </div>
                    </AnimatedCard>

                    {/* Server Management Section */}
                    <Can action={'settings.rename'}>
                        <AnimatedCard delay={0.4} variant="hover" className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-6 w-[342px] transition-all duration-200 hover:border-hyper-accent hover:scale-[1.02] hover:shadow-lg hover:shadow-hyper-accent/10">
                            <div className="flex items-center gap-3 mb-6">
                                <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                                    <Edit3 size={20} className="text-hyper-accent" />
                                </div>
                                <div className="flex-1 min-w-0">
                                    <h3 className="text-lg font-semibold text-hyper-primary">Server Details</h3>
                                    <p className="text-sm text-hyper-accent">Name & Description</p>
                                </div>
                            </div>
                            <RenameServerBox />
                        </AnimatedCard>
                    </Can>
                    
                    <Can action={'settings.reinstall'}>
                        <AnimatedCard delay={0.5} variant="hover" className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-6 w-[342px] transition-all duration-200 hover:border-hyper-accent hover:scale-[1.02] hover:shadow-lg hover:shadow-hyper-accent/10">
                            <div className="flex items-center gap-3 mb-6">
                                <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                                    <RotateCcw size={20} className="text-hyper-accent" />
                                </div>
                                <div className="flex-1 min-w-0">
                                    <h3 className="text-lg font-semibold text-hyper-primary">Reinstall Server</h3>
                                    <p className="text-sm text-hyper-accent">Reset Server Data</p>
                                </div>
                            </div>
                            <ReinstallServerBox />
                        </AnimatedCard>
                    </Can>
                </AnimatedContainer>
            </PageTransition>
        </ServerContentBlock>
    );
};
