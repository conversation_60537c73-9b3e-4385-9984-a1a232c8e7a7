import React, { useState } from 'react';
import EditSubuserModal from '@rolexdev/themes/hyperv1/components/server/users/EditSubuserModal';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { UserPlus } from 'lucide-react';

export default () => {
    const [visible, setVisible] = useState(false);

    return (
        <>
            <EditSubuserModal visible={visible} onModalDismissed={() => setVisible(false)} />
            <Button 
                onClick={() => setVisible(true)}
                className="flex items-center gap-2"
                size="small"
            >
                <UserPlus size={16} />
                Add New User
            </Button>
        </>
    );
};
