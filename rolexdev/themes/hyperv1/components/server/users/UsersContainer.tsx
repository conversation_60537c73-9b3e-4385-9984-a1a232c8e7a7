import React, { useEffect, useState } from 'react';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { Actions, useStoreActions, useStoreState } from 'easy-peasy';
import { ApplicationStore } from '@/state';
import Spinner from '@/components/elements/Spinner';
import AddSubuserButton from '@rolexdev/themes/hyperv1/components/server/users/AddSubuserButton';
import UserRow from '@rolexdev/themes/hyperv1/components/server/users/UserRow';
import FlashMessageRender from '@/components/FlashMessageRender';
import getServerSubusers from '@/api/server/users/getServerSubusers';
import { httpErrorToHuman } from '@/api/http';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import ServerContentBlock from '@rolexdev/themes/hyperv1/components/elements/ServerContentBlock';
import { Users, UserPlus, Shield } from 'lucide-react';
import { PageTransition, AnimatedCard, AnimatedContainer } from '@rolexdev/themes/hyperv1/components/elements/animations';

export default () => {
    const [loading, setLoading] = useState(true);

    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const subusers = ServerContext.useStoreState((state) => state.subusers.data);
    const setSubusers = ServerContext.useStoreActions((actions) => actions.subusers.setSubusers);

    const permissions = useStoreState((state: ApplicationStore) => state.permissions.data);
    const getPermissions = useStoreActions((actions: Actions<ApplicationStore>) => actions.permissions.getPermissions);
    const { addError, clearFlashes } = useStoreActions((actions: Actions<ApplicationStore>) => actions.flashes);

    useEffect(() => {
        clearFlashes('users');
        getServerSubusers(uuid)
            .then((subusers) => {
                setSubusers(subusers);
                setLoading(false);
            })
            .catch((error) => {
                console.error(error);
                addError({ key: 'users', message: httpErrorToHuman(error) });
            });
    }, []);

    useEffect(() => {
        getPermissions().catch((error) => {
            addError({ key: 'users', message: httpErrorToHuman(error) });
            console.error(error);
        });
    }, []);

    if (!subusers.length && (loading || !Object.keys(permissions).length)) {
        return (
            <PageTransition>
                <ServerContentBlock title={'Users'}>
                    <AnimatedCard>
                        <Spinner size={'large'} centered />
                    </AnimatedCard>
                </ServerContentBlock>
            </PageTransition>
        );
    }

    return (
        <PageTransition>
            <ServerContentBlock title={'Users'}>
                <AnimatedContainer staggerChildren={0.1}>
                    <AnimatedCard>
                        <div className="mb-6">
                            <div className="flex items-center gap-3 mb-4">
                                <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                                    <Users size={20} className="text-hyper-accent" />
                                </div>
                                <div>
                                    <h2 className="text-lg font-bold text-hyper-primary">Server Users</h2>
                                    <p className="text-sm text-hyper-muted-foreground">
                                        Manage user access and permissions for this server
                                    </p>
                                </div>
                            </div>
                            
                            <FlashMessageRender byKey={'users'} className="mb-4" />
                        </div>
                    </AnimatedCard>

                    <AnimatedCard>
                        <div className="flex flex-wrap gap-6 justify-center lg:justify-start">
                            {!subusers.length ? (
                                <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-8 text-center w-[342px] mx-auto">
                                    <div className="w-16 h-16 rounded-lg bg-hyper-primary-10 flex items-center justify-center mx-auto mb-4">
                                        <Shield size={32} className="text-hyper-accent" />
                                    </div>
                                    <h3 className="text-lg font-semibold text-hyper-primary mb-2">No Users Found</h3>
                                    <p className="text-sm text-hyper-accent mb-6">
                                        You haven't added any subusers to this server yet. 
                                        Add users to grant them specific permissions and access to your server.
                                    </p>
                                    <Can action={'user.create'}>
                                        <AddSubuserButton />
                                    </Can>
                                </div>
                            ) : (
                                <>
                                    {subusers.map((subuser) => (
                                        <UserRow key={subuser.uuid} subuser={subuser} />
                                    ))}
                                    
                                    {/* Add User Card */}
                                    <Can action={'user.create'}>
                                        <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary border-dashed rounded-lg p-6 w-[342px] transition-all duration-200 hover:border-hyper-accent flex flex-col items-center justify-center text-center">
                                            <div className="w-16 h-16 rounded-lg bg-hyper-primary-10 flex items-center justify-center mx-auto mb-4">
                                                <UserPlus size={32} className="text-hyper-accent" />
                                            </div>
                                            <h3 className="text-lg font-semibold text-hyper-primary mb-2">Add New User</h3>
                                            <p className="text-sm text-hyper-accent mb-6">
                                                Grant access to this server by adding a new subuser with specific permissions.
                                            </p>
                                            <AddSubuserButton />
                                        </div>
                                    </Can>
                                </>
                            )}
                        </div>
                    </AnimatedCard>
                </AnimatedContainer>
            </ServerContentBlock>
        </PageTransition>
    );
};
