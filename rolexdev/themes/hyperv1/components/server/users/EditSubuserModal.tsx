import React, { useEffect, useRef } from 'react';
import { Subuser } from '@/state/server/subusers';
import { Form, Formik } from 'formik';
import { array, object, string } from 'yup';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import { Actions, useStoreActions, useStoreState } from 'easy-peasy';
import { ApplicationStore } from '@/state';
import createOrUpdateSubuser from '@/api/server/users/createOrUpdateSubuser';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import FlashMessageRender from '@/components/FlashMessageRender';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import { usePermissions } from '@rolexdev/themes/hyperv1/plugins/usePermissions';
import { useDeepCompareMemo } from '@/plugins/useDeepCompareMemo';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import PermissionTitleBox from '@rolexdev/themes/hyperv1/components/server/users/PermissionTitleBox';
import PermissionRow from '@rolexdev/themes/hyperv1/components/server/users/PermissionRow';
import { UserPlus, Settings, AlertCircle, Mail } from 'lucide-react';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';

type Props = {
    subuser?: Subuser;
    visible: boolean;
    onModalDismissed: () => void;
};

interface Values {
    email: string;
    permissions: string[];
}

const EditSubuserModal = ({ subuser, visible, onModalDismissed }: Props) => {
    const ref = useRef<HTMLHeadingElement>(null);
    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const appendSubuser = ServerContext.useStoreActions((actions) => actions.subusers.appendSubuser);
    const { clearFlashes, clearAndAddHttpError } = useStoreActions(
        (actions: Actions<ApplicationStore>) => actions.flashes
    );

    const isRootAdmin = useStoreState((state) => state.user.data!.rootAdmin);
    const permissions = useStoreState((state) => state.permissions.data);
    // The currently logged in user's permissions. We're going to filter out any permissions
    // that they should not need.
    const loggedInPermissions = ServerContext.useStoreState((state) => state.server.permissions);
    const [canEditUser] = usePermissions(subuser ? ['user.update'] : ['user.create']);

    // The permissions that can be modified by this user.
    const editablePermissions = useDeepCompareMemo(() => {
        const cleaned = Object.keys(permissions).map((key) =>
            Object.keys(permissions[key].keys).map((pkey) => `${key}.${pkey}`)
        );

        const list: string[] = ([] as string[]).concat.apply([], Object.values(cleaned));

        if (isRootAdmin || (loggedInPermissions.length === 1 && loggedInPermissions[0] === '*')) {
            return list;
        }

        return list.filter((key) => loggedInPermissions.indexOf(key) >= 0);
    }, [isRootAdmin, permissions, loggedInPermissions]);

    const submit = (values: Values) => {
        clearFlashes('user:edit');

        createOrUpdateSubuser(uuid, values, subuser)
            .then((subuser) => {
                appendSubuser(subuser);
                onModalDismissed();
            })
            .catch((error) => {
                console.error(error);
                clearAndAddHttpError({ key: 'user:edit', error });

                if (ref.current) {
                    ref.current.scrollIntoView();
                }
            });
    };

    useEffect(
        () => () => {
            clearFlashes('user:edit');
        },
        []
    );

    return (
        <Dialog
            open={visible}
            onClose={onModalDismissed}
            title={subuser ? `${canEditUser ? 'Modify' : 'View'} User Permissions` : 'Add New User'}
        >
            <Formik
                onSubmit={submit}
                initialValues={
                    {
                        email: subuser?.email || '',
                        permissions: subuser?.permissions || [],
                    } as Values
                }
                validationSchema={object().shape({
                    email: string()
                        .max(191, 'Email addresses must not exceed 191 characters.')
                        .email('A valid email address must be provided.')
                        .required('A valid email address must be provided.'),
                    permissions: array().of(string()),
                })}
            >
                <Form id="edit-subuser-form" className="space-y-6 py-6">
                    <FlashMessageRender byKey={'user:edit'} className="mt-4" />
                    
                    {/* Permission Notice */}
                    {!isRootAdmin && loggedInPermissions[0] !== '*' && (
                        <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
                            <div className="flex items-start gap-3">
                                <AlertCircle size={20} className="text-blue-400 flex-shrink-0 mt-0.5" />
                                <div>
                                    <h4 className="text-sm font-semibold text-blue-400 mb-1">Permission Notice</h4>
                                    <p className="text-xs text-blue-300">
                                        Only permissions which your account is currently assigned may be selected when creating or
                                        modifying other users.
                                    </p>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Email Field for New Users */}
                    {!subuser && (
                        <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-6">
                            <div className="flex items-center gap-3 mb-4">
                                <div className="w-8 h-8 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                                    <Mail size={16} className="text-hyper-accent" />
                                </div>
                                <h3 className="text-lg font-semibold text-hyper-primary">User Information</h3>
                            </div>
                            <Field
                                light
                                name={'email'}
                                label={'User Email Address'}
                                placeholder="<EMAIL>"
                                description={'Enter the email address of the user you wish to invite as a subuser for this server.'}
                            />
                        </div>
                    )}

                    {/* Permissions Section */}
                    <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-6">
                        <div className="flex items-center gap-3 mb-6">
                            <div className="w-8 h-8 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                                <Settings size={16} className="text-hyper-accent" />
                            </div>
                            <div>
                                <h3 className="text-lg font-semibold text-hyper-primary">User Permissions</h3>
                                <p className="text-sm text-hyper-secondary">
                                    Configure what actions this user can perform on the server
                                </p>
                            </div>
                        </div>
                        
                        <div className="space-y-4">
                            {Object.keys(permissions)
                                .filter((key) => key !== 'websocket')
                                .map((key, index) => (
                                    <PermissionTitleBox
                                        key={`permission_${key}`}
                                        title={key}
                                        isEditable={canEditUser}
                                        permissions={Object.keys(permissions[key].keys).map((pkey) => `${key}.${pkey}`)}
                                    >
                                        <p className="text-sm text-hyper-secondary mb-4">{permissions[key].description}</p>
                                        <div className="space-y-2">
                                            {Object.keys(permissions[key].keys).map((pkey) => (
                                                <PermissionRow
                                                    key={`permission_${key}.${pkey}`}
                                                    permission={`${key}.${pkey}`}
                                                    disabled={!canEditUser || editablePermissions.indexOf(`${key}.${pkey}`) < 0}
                                                />
                                            ))}
                                        </div>
                                    </PermissionTitleBox>
                                ))}
                        </div>
                    </div>
                </Form>
            </Formik>

            {/* Footer Actions */}
            <Dialog.Footer>
                <Button 
                    isSecondary
                    onClick={onModalDismissed}
                    size="small"
                >
                    Cancel
                </Button>
                <Can action={subuser ? 'user.update' : 'user.create'}>
                    <Button 
                        type={'submit'} 
                        form="edit-subuser-form"
                        className="flex items-center gap-2"
                        size="small"
                    >
                        {subuser ? (
                            <>
                                <Settings size={16} />
                                Save Changes
                            </>
                        ) : (
                            <>
                                <UserPlus size={16} />
                                Invite User
                            </>
                        )}
                    </Button>
                </Can>
            </Dialog.Footer>
        </Dialog>
    );
};

export default EditSubuserModal;
