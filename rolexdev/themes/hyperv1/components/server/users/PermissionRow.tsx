import React from 'react';
import { useStoreState } from 'easy-peasy';
import Checkbox from '@rolexdev/themes/hyperv1/components/elements/Checkbox';
import { Key, Lock } from 'lucide-react';

interface Props {
    permission: string;
    disabled: boolean;
}

const PermissionRow = ({ permission, disabled }: Props) => {
    const [key, pkey] = permission.split('.', 2);
    const permissions = useStoreState((state: any) => state.permissions.data);

    const handleRowClick = () => {
        if (!disabled) {
            // Trigger click on the checkbox
            const checkbox = document.getElementById(`permission_${permission}`) as HTMLInputElement;
            if (checkbox) {
                checkbox.click();
            }
        }
    };

    return (
        <div 
            onClick={handleRowClick}
            className={`group flex items-center gap-3 p-3 rounded-lg border border-transparent transition-all duration-200 ${
                disabled 
                    ? 'opacity-50 cursor-not-allowed' 
                    : 'cursor-pointer hover:border-hyper-accent hover:bg-hyper-primary-10'
            }`}
        >
            {/* Checkbox */}
            <div className="flex-shrink-0" onClick={(e) => e.stopPropagation()}>
                <Checkbox
                    id={`permission_${permission}`}
                    name={'permissions'}
                    value={permission}
                    disabled={disabled}
                />
            </div>

            {/* Icon */}
            <div className="flex-shrink-0">
                <div className="w-8 h-8 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                    {disabled ? (
                        <Lock size={14} className="text-hyper-muted-foreground" />
                    ) : (
                        <Key size={14} className="text-hyper-accent" />
                    )}
                </div>
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                    <h5 className="text-sm font-medium text-hyper-primary">
                        {pkey}
                    </h5>
                    {disabled && (
                        <span className="inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium bg-hyper-muted/20 text-hyper-muted-foreground border border-hyper-muted/30">
                            <Lock size={10} />
                            Restricted
                        </span>
                    )}
                </div>
                {permissions[key].keys[pkey].length > 0 && (
                    <p className="text-xs text-hyper-secondary line-clamp-2">
                        {permissions[key].keys[pkey]}
                    </p>
                )}
            </div>
        </div>
    );
};

export default PermissionRow;
