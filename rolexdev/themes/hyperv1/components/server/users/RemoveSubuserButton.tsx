import React, { useState } from 'react';
import ConfirmationModal from '@rolexdev/themes/hyperv1/components/elements/ConfirmationModal';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { Subuser } from '@/state/server/subusers';
import deleteSubuser from '@/api/server/users/deleteSubuser';
import { Actions, useStoreActions } from 'easy-peasy';
import { ApplicationStore } from '@/state';
import { httpErrorToHuman } from '@/api/http';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { Trash2 } from 'lucide-react';

export default ({ subuser }: { subuser: Subuser }) => {
    const [loading, setLoading] = useState(false);
    const [showConfirmation, setShowConfirmation] = useState(false);

    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const removeSubuser = ServerContext.useStoreActions((actions) => actions.subusers.removeSubuser);
    const { addError, clearFlashes } = useStoreActions((actions: Actions<ApplicationStore>) => actions.flashes);

    const doDeletion = () => {
        setLoading(true);
        clearFlashes('users');
        deleteSubuser(uuid, subuser.uuid)
            .then(() => {
                setLoading(false);
                removeSubuser(subuser.uuid);
            })
            .catch((error) => {
                console.error(error);
                addError({ key: 'users', message: httpErrorToHuman(error) });
                setShowConfirmation(false);
            });
    };

    return (
        <>
            <ConfirmationModal
                title={'Remove User Access'}
                buttonText={'Yes, remove user'}
                visible={showConfirmation}
                showSpinnerOverlay={loading}
                onConfirmed={() => doDeletion()}
                onModalDismissed={() => setShowConfirmation(false)}
            >
                <div className="space-y-4">
                    <p className="text-hyper-secondary">
                        Are you sure you want to remove <strong className="text-hyper-primary">{subuser.email}</strong> from this server?
                    </p>
                    <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
                        <div className="flex items-start gap-3">
                            <div className="w-1 h-6 bg-red-500 rounded-full flex-shrink-0 mt-0.5"></div>
                            <div>
                                <h4 className="text-sm font-semibold text-red-400 mb-1">Warning</h4>
                                <p className="text-xs text-red-300">
                                    This user will immediately lose all access to this server and cannot perform any actions. 
                                    This action cannot be undone.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </ConfirmationModal>
            
            <Button
                color="red"
                size="small"
                onClick={() => setShowConfirmation(true)}
                className="flex items-center gap-1"
            >
                <Trash2 size={14} />
                <span className="">Remove</span>
            </Button>
        </>
    );
};
