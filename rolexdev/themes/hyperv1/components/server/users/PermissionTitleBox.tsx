import React, { memo, useCallback } from 'react';
import { useField } from 'formik';
import { StandaloneCheckbox } from '@rolexdev/themes/hyperv1/components/elements/Checkbox';
import isEqual from 'react-fast-compare';
import { Minus } from 'lucide-react';

interface Props {
    isEditable: boolean;
    title: string;
    permissions: string[];
    className?: string;
    children?: React.ReactNode;
}

const PermissionTitleBox: React.FC<Props> = memo(({ isEditable, title, permissions, className, children }) => {
    const [{ value }, , { setValue }] = useField<string[]>('permissions');

    const onCheckboxClicked = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            if (e.currentTarget.checked) {
                setValue([...value, ...permissions.filter((p) => !value.includes(p))]);
            } else {
                setValue(value.filter((p) => !permissions.includes(p)));
            }
        },
        [permissions, value]
    );

    const allSelected = permissions.every((p) => value.includes(p));
    const someSelected = permissions.some((p) => value.includes(p));

    return (
        <div className={`bg-hyper-glass backdrop-blur-lg border border-hyper-primary rounded-lg ${className || ''}`}>
            {/* Header */}
            <div className="p-4 border-b border-hyper-primary/50">
                <div className="flex items-center justify-between">
                    <div>
                        <h4 className="text-sm font-semibold text-hyper-primary uppercase tracking-wide">
                            {title}
                        </h4>
                    </div>
                    {isEditable && (
                        <div className="flex items-center gap-2">
                            <span className="text-xs text-hyper-secondary">
                                {allSelected ? 'All selected' : someSelected ? 'Some selected' : 'None selected'}
                            </span>
                            <div className="relative">
                                <StandaloneCheckbox
                                    checked={allSelected}
                                    onChange={onCheckboxClicked}
                                />
                                {someSelected && !allSelected && (
                                    <Minus size={12} className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-hyper-accent pointer-events-none" />
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>
            
            {/* Content */}
            <div className="p-4">
                {children}
            </div>
        </div>
    );
}, isEqual);

export default PermissionTitleBox;
