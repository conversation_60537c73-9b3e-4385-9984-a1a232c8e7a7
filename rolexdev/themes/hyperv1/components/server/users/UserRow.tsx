import React, { useState } from 'react';
import { Subuser } from '@/state/server/subusers';
import { Shield, ShieldX, Edit3, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Mail, Key } from 'lucide-react';
import RemoveSubuserButton from '@rolexdev/themes/hyperv1/components/server/users/RemoveSubuserButton';
import EditSubuserModal from '@rolexdev/themes/hyperv1/components/server/users/EditSubuserModal';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import { useStoreState } from 'easy-peasy';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import Tooltip from '@rolexdev/themes/hyperv1/components/elements/tooltip/Tooltip';
import { createAvatar } from '@dicebear/core';
import { identicon } from '@dicebear/collection';

interface Props {
    subuser: Subuser;
}

export default ({ subuser }: Props) => {
    const uuid = useStoreState((state) => state.user!.data!.uuid);
    const [visible, setVisible] = useState(false);

    // Generate avatar using DiceBear (same pattern as sidebar)
    const avatarSvg = createAvatar(identicon, { 
        seed: subuser.email, 
        size: 48 
    }).toString();

    const permissionCount = subuser.permissions.filter((permission) => permission !== 'websocket.connect').length;

    return (
        <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-6 w-[342px] transition-all duration-200 hover:border-hyper-accent hover:scale-[1.02] hover:shadow-lg hover:shadow-hyper-accent/10">
            <EditSubuserModal subuser={subuser} visible={visible} onModalDismissed={() => setVisible(false)} />
            
            {/* Header Section */}
            <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                    <Shield size={20} className="text-hyper-accent" />
                </div>
                <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-semibold text-hyper-primary">User Account</h3>
                    <p className="text-sm text-hyper-accent">
                        {subuser.uuid === uuid ? 'Server Owner' : 'Subuser'}
                    </p>
                </div>
            </div>

            {/* User Avatar and Info */}
            <div className="mb-6">
                <div className="flex items-center gap-4 mb-4">
                    <div
                        className="w-16 h-16 rounded-lg bg-hyper-accent flex items-center justify-center text-hyper-primary font-semibold border border-hyper-primary overflow-hidden flex-shrink-0"
                        dangerouslySetInnerHTML={{ __html: avatarSvg }}
                    />
                    <div className="flex-1 min-w-0">
                        <h4 className="text-lg font-semibold text-hyper-primary truncate mb-1">
                            {subuser.email}
                        </h4>
                        <div className="flex items-center gap-2">
                            <Tooltip placement="top" content={subuser.twoFactorEnabled ? 'Two-Factor Authentication Enabled' : 'Two-Factor Authentication Disabled'}>
                                <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                                    subuser.twoFactorEnabled 
                                        ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                                        : 'bg-red-500/20 text-red-400 border border-red-500/30'
                                }`}>
                                    {subuser.twoFactorEnabled ? (
                                        <Shield size={12} />
                                    ) : (
                                        <ShieldX size={12} />
                                    )}
                                    2FA
                                </div>
                            </Tooltip>
                        </div>
                    </div>
                </div>
            </div>

            {/* User Details */}
            <div className="space-y-4 mb-6">
                <div className="grid grid-cols-1 gap-4">
                    <div className="bg-hyper-background border border-hyper-primary rounded-lg p-3">
                        <div className="flex items-center gap-2 mb-2">
                            <Mail size={16} className="text-hyper-accent" />
                            <span className="text-sm font-medium text-hyper-foreground">Email Address</span>
                        </div>
                        <p className="text-sm text-hyper-primary font-mono truncate">{subuser.email}</p>
                    </div>
                    
                    <div className="bg-hyper-background border border-hyper-primary rounded-lg p-3">
                        <div className="flex items-center gap-2 mb-2">
                            <Key size={16} className="text-hyper-accent" />
                            <span className="text-sm font-medium text-hyper-foreground">Permissions</span>
                        </div>
                        <p className="text-sm text-hyper-primary">
                            {permissionCount === 0 ? 'No permissions assigned' : `${permissionCount} permissions assigned`}
                        </p>
                    </div>
                    
                    <div className="bg-hyper-background border border-hyper-primary rounded-lg p-3">
                        <div className="flex items-center gap-2 mb-2">
                            <UserCheck size={16} className="text-hyper-accent" />
                            <span className="text-sm font-medium text-hyper-foreground">Security Level</span>
                        </div>
                        <div className="flex items-center gap-2">
                            {subuser.twoFactorEnabled ? (
                                <UserCheck size={14} className="text-green-400" />
                            ) : (
                                <ShieldX size={14} className="text-red-400" />
                            )}
                            <span className="text-sm text-hyper-primary">
                                {subuser.twoFactorEnabled ? 'Secured with 2FA' : 'Basic authentication'}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Actions */}
            {subuser.uuid !== uuid && (
                <div className="flex items-center justify-between pt-4 border-t border-hyper-primary">
                    <Can action={'user.update'}>
                        <Tooltip placement="top" content="Edit User">
                            <Button
                                isSecondary
                                size="small"
                                onClick={() => setVisible(true)}
                                className="flex items-center gap-2"
                            >
                                <Edit3 size={16} />
                                Edit User
                            </Button>
                        </Tooltip>
                    </Can>
                    <Can action={'user.delete'}>
                        <RemoveSubuserButton subuser={subuser} />
                    </Can>
                </div>
            )}
        </div>
    );
};
