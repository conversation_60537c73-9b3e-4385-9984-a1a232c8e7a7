import React, { useState } from 'react';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import deleteServerAllocation from '@/api/server/network/deleteServerAllocation';
import getServerAllocations from '@rolexdev/themes/hyperv1/api/swr/getServerAllocations';
import { useFlashKey } from '@/plugins/useFlash';
import ConfirmationModal from '@rolexdev/themes/hyperv1/components/elements/ConfirmationModal';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { Trash2 } from 'lucide-react';
import Tooltip from '@rolexdev/themes/hyperv1/components/elements/tooltip/Tooltip';

interface Props {
    allocation: number;
}

const DeleteAllocationButton = ({ allocation }: Props) => {
    const [showConfirmation, setShowConfirmation] = useState(false);
    const [loading, setLoading] = useState(false);

    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const setServerFromState = ServerContext.useStoreActions((actions) => actions.server.setServerFromState);

    const { mutate } = getServerAllocations();
    const { clearFlashes, clearAndAddHttpError } = useFlashKey('server:network');

    const deleteAllocation = () => {
        setLoading(true);
        clearFlashes();

        mutate((data) => data?.filter((a) => a.id !== allocation), false);
        setServerFromState((s) => ({ ...s, allocations: s.allocations.filter((a) => a.id !== allocation) }));

        deleteServerAllocation(uuid, allocation)
            .catch((error) => {
                clearAndAddHttpError(error);
                mutate();
            })
            .finally(() => {
                setLoading(false);
                setShowConfirmation(false);
            });
    };

    return (
        <>
            <ConfirmationModal
                visible={showConfirmation}
                title="Delete Allocation"
                buttonText="Delete Allocation"
                onConfirmed={deleteAllocation}
                onModalDismissed={() => setShowConfirmation(false)}
                showSpinnerOverlay={loading}
            >
                <div className="space-y-4">
                    <p className="text-hyper-secondary">
                        Are you sure you want to delete this network allocation?
                    </p>
                    <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
                        <div className="flex items-start gap-3">
                            <div className="w-1 h-6 bg-red-500 rounded-full flex-shrink-0 mt-0.5"></div>
                            <div>
                                <h4 className="text-sm font-semibold text-red-400 mb-1">Warning</h4>
                                <p className="text-xs text-red-300">
                                    This allocation will be immediately removed from your server and cannot be recovered.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </ConfirmationModal>

            <Tooltip placement="top" content="Delete Allocation">
                <Button
                    color="red"
                    size="small"
                    onClick={() => setShowConfirmation(true)}
                    className="flex items-center gap-2"
                    disabled={loading}
                >
                    <Trash2 size={16} />
                    Delete
                </Button>
            </Tooltip>
        </>
    );
};

export default DeleteAllocationButton;
