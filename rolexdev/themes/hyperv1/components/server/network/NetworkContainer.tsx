import React, { useEffect, useState } from 'react';
import Spinner from '@/components/elements/Spinner';
import { useFlashKey } from '@/plugins/useFlash';
import ServerContentBlock from '@rolexdev/themes/hyperv1/components/elements/ServerContentBlock';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import AllocationRow from '@rolexdev/themes/hyperv1/components/server/network/AllocationRow';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import createServerAllocation from '@/api/server/network/createServerAllocation';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import SpinnerOverlay from '@/components/elements/SpinnerOverlay';
import getServerAllocations from '@rolexdev/themes/hyperv1/api/swr/getServerAllocations';
import isEqual from 'react-fast-compare';
import { useDeepCompareEffect } from '@/plugins/useDeepCompareEffect';
import FlashMessageRender from '@/components/FlashMessageRender';
import { Network, Plus, AlertCircle } from 'lucide-react';
import PaginationFooter from '@rolexdev/themes/hyperv1/components/elements/table/PaginationFooter';
import { PageTransition, AnimatedCard, AnimatedContainer } from '@rolexdev/themes/hyperv1/components/elements/animations';

const NetworkContainer = () => {
    const [loading, setLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [perPage, setPerPage] = useState(10);
    
    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const allocationLimit = ServerContext.useStoreState((state) => state.server.data!.featureLimits.allocations);
    const allocations = ServerContext.useStoreState((state) => state.server.data!.allocations, isEqual);
    const setServerFromState = ServerContext.useStoreActions((actions) => actions.server.setServerFromState);

    const { clearFlashes, clearAndAddHttpError } = useFlashKey('server:network');
    const { data, error, mutate } = getServerAllocations();

    // Calculate pagination
    const totalAllocations = data?.length || 0;
    const totalPages = Math.ceil(totalAllocations / perPage);
    const startIndex = (currentPage - 1) * perPage;
    const endIndex = startIndex + perPage;
    const paginatedAllocations = data?.slice(startIndex, endIndex) || [];

    const pagination = {
        currentPage,
        perPage,
        total: totalAllocations,
        count: paginatedAllocations.length,
        totalPages,
    };

    const handlePageSelect = (page: number, newPerPage: number) => {
        setCurrentPage(page);
        if (newPerPage !== perPage) {
            setPerPage(newPerPage);
            // Adjust current page if needed when changing perPage
            const newTotalPages = Math.ceil(totalAllocations / newPerPage);
            if (page > newTotalPages) {
                setCurrentPage(newTotalPages || 1);
            }
        }
    };

    useEffect(() => {
        mutate(allocations);
    }, []);

    useEffect(() => {
        clearAndAddHttpError(error);
    }, [error]);

    useDeepCompareEffect(() => {
        if (!data) return;

        setServerFromState((state) => ({ ...state, allocations: data }));
    }, [data]);

    const onCreateAllocation = () => {
        clearFlashes();

        setLoading(true);
        createServerAllocation(uuid)
            .then((allocation) => {
                setServerFromState((s) => ({ ...s, allocations: s.allocations.concat(allocation) }));
                return mutate(data?.concat(allocation), false);
            })
            .catch((error) => clearAndAddHttpError(error))
            .then(() => setLoading(false));
    };

    return (
        <PageTransition>
            <ServerContentBlock title={'Network'}>
                <FlashMessageRender byKey={'server:network'} className="mb-6" />
                
                <AnimatedContainer staggerChildren={0.1}>
                    {/* Header Section */}
                    <AnimatedCard>
                        <div className="flex items-center gap-3 mb-6">
                            <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                                <Network size={20} className="text-hyper-accent" />
                            </div>
                            <div className="flex-1">
                                <h2 className="text-xl font-semibold text-hyper-primary">Network Allocations</h2>
                                <p className="text-sm text-hyper-muted-foreground">
                                    Manage server IP addresses and port allocations
                                </p>
                            </div>
                        </div>
                    </AnimatedCard>

                    {!data ? (
                        <AnimatedCard>
                            <Spinner size={'large'} centered />
                        </AnimatedCard>
                    ) : (
                        <>
                            {data.length === 0 ? (
                                <AnimatedCard>
                                    <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-8 text-center w-[342px] mx-auto">
                                        <div className="w-16 h-16 rounded-lg bg-hyper-primary-10 flex items-center justify-center mx-auto mb-4">
                                            <Network size={32} className="text-hyper-accent" />
                                        </div>
                                        <h3 className="text-lg font-semibold text-hyper-primary mb-2">No Network Allocations</h3>
                                        <p className="text-sm text-hyper-accent mb-6">
                                            This server currently has no network allocations configured. Create your first allocation to get started.
                                        </p>
                                        {allocationLimit > 0 && (
                                            <Can action={'allocation.create'}>
                                                <Button 
                                                    onClick={onCreateAllocation}
                                                    className="flex items-center gap-2"
                                                    disabled={loading}
                                                >
                                                    <Plus size={16} />
                                                    Create First Allocation
                                                </Button>
                                            </Can>
                                        )}
                                    </div>
                                </AnimatedCard>
                            ) : (
                                <>
                                    <AnimatedCard>
                                        <div className="flex flex-wrap gap-6 justify-center lg:justify-start">
                                            {paginatedAllocations.map((allocation) => (
                                                <AllocationRow key={`${allocation.ip}:${allocation.port}`} allocation={allocation} />
                                            ))}
                                            
                                            {/* Add New Allocation Card */}
                                            <Can action={'allocation.create'}>
                                                {allocationLimit > 0 ? (
                                                    allocationLimit > data.length ? (
                                                        <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary border-dashed rounded-lg p-6 w-[342px] transition-all duration-200 hover:border-hyper-accent flex flex-col items-center justify-center text-center">
                                                            <div className="w-16 h-16 rounded-lg bg-hyper-primary-10 flex items-center justify-center mx-auto mb-4">
                                                                <Plus size={32} className="text-hyper-accent" />
                                                            </div>
                                                            <h3 className="text-lg font-semibold text-hyper-primary mb-2">Add New Allocation</h3>
                                                            <div className="text-xs text-hyper-accent mb-3 bg-hyper-background border border-hyper-primary rounded px-2 py-1">
                                                                {data.length} of {allocationLimit} allocations used
                                                            </div>
                                                            <p className="text-sm text-hyper-accent mb-6">
                                                                Create additional network endpoints for your server to handle more connections.
                                                            </p>
                                                            <Button 
                                                                onClick={onCreateAllocation}
                                                                className="flex items-center gap-2"
                                                                size="small"
                                                                disabled={loading}
                                                            >
                                                                <Plus size={16} />
                                                                Create Allocation
                                                            </Button>
                                                        </div>
                                                    ) : (
                                                        <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary border-dashed rounded-lg p-6 w-[342px] transition-all duration-200 flex flex-col items-center justify-center text-center opacity-60">
                                                            <div className="w-16 h-16 rounded-lg bg-hyper-primary-10 flex items-center justify-center mx-auto mb-4">
                                                                <AlertCircle size={32} className="text-hyper-accent" />
                                                            </div>
                                                            <h3 className="text-lg font-semibold text-hyper-primary mb-2">Allocation Limit Reached</h3>
                                                            <div className="text-xs text-hyper-accent mb-3 bg-hyper-background border border-hyper-primary rounded px-2 py-1">
                                                                {data.length} of {allocationLimit} allocations used
                                                            </div>
                                                            <p className="text-sm text-hyper-accent">
                                                                You've reached the maximum of {allocationLimit} allocation{allocationLimit !== 1 ? 's' : ''} for this server. Delete an existing allocation to create a new one.
                                                            </p>
                                                        </div>
                                                    )
                                                ) : null}
                                            </Can>
                                        </div>
                                    </AnimatedCard>
                                    
                                    {totalAllocations > perPage && (
                                        <AnimatedCard>
                                            <PaginationFooter
                                                pagination={pagination}
                                                onPageSelect={handlePageSelect}
                                                className="mt-6"
                                            />
                                        </AnimatedCard>
                                    )}
                                </>
                            )}
                            
                            <SpinnerOverlay visible={loading} />
                        </>
                    )}
                </AnimatedContainer>
            </ServerContentBlock>
        </PageTransition>
    );
};

export default NetworkContainer;
