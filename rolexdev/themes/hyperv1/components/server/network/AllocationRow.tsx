import React, { memo, useCallback, useState } from 'react';
import isEqual from 'react-fast-compare';
import InputSpinner from '@/components/elements/InputSpinner';
import { Textarea } from '@rolexdev/themes/hyperv1/components/elements/Input';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { Allocation } from '@/api/server/getServer';
import { debounce } from 'debounce';
import setServerAllocationNotes from '@/api/server/network/setServerAllocationNotes';
import { useFlashKey } from '@/plugins/useFlash';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import CopyOnClick from '@/components/elements/CopyOnClick';
import DeleteAllocationButton from '@rolexdev/themes/hyperv1/components/server/network/DeleteAllocationButton';
import setPrimaryServerAllocation from '@/api/server/network/setPrimaryServerAllocation';
import getServerAllocations from '@rolexdev/themes/hyperv1/api/swr/getServerAllocations';
import { ip } from '@/lib/formatters';
import { Network, Star, Copy } from 'lucide-react';
import Tooltip from '@rolexdev/themes/hyperv1/components/elements/tooltip/Tooltip';

interface Props {
    allocation: Allocation;
}

const AllocationRow = ({ allocation }: Props) => {
    const [loading, setLoading] = useState(false);
    const { clearFlashes, clearAndAddHttpError } = useFlashKey('server:network');
    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const { mutate } = getServerAllocations();

    const onNotesChanged = useCallback((id: number, notes: string) => {
        mutate((data) => data?.map((a) => (a.id === id ? { ...a, notes } : a)), false);
    }, []);

    const setAllocationNotes = debounce((notes: string) => {
        setLoading(true);
        clearFlashes();

        setServerAllocationNotes(uuid, allocation.id, notes)
            .then(() => onNotesChanged(allocation.id, notes))
            .catch((error) => clearAndAddHttpError(error))
            .then(() => setLoading(false));
    }, 750);

    const setPrimaryAllocation = () => {
        clearFlashes();
        mutate((data) => data?.map((a) => ({ ...a, isDefault: a.id === allocation.id })), false);

        setPrimaryServerAllocation(uuid, allocation.id).catch((error) => {
            clearAndAddHttpError(error);
            mutate();
        });
    };

    return (
        <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-6 w-[342px] transition-all duration-200 hover:border-hyper-accent hover:scale-[1.02] hover:shadow-lg hover:shadow-hyper-accent/10">
            {/* Header Section */}
            <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                    <Network size={20} className="text-hyper-accent" />
                </div>
                <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-semibold text-hyper-primary">Network Allocation</h3>
                    <p className="text-sm text-hyper-accent">Server network endpoint</p>
                </div>
            </div>

            {/* Connection Details */}
            <div className="space-y-4 mb-6">
                {/* IP Address/Hostname */}
                <div>
                    <label className="block text-sm font-medium text-hyper-foreground mb-2">
                        {allocation.alias ? 'Hostname' : 'IP Address'}
                    </label>
                    <CopyOnClick text={allocation.alias || ip(allocation.ip)}>
                        <div className="relative group">
                            <input
                                type="text"
                                value={allocation.alias || ip(allocation.ip)}
                                readOnly
                                className="appearance-none outline-none w-full min-w-0 p-3 rounded-md text-sm transition-all duration-150 resize-none shadow-none focus:ring-0 h-10 bg-hyper-background text-hyper-foreground border focus:border-2 border-hyper-primary hover:border-hyper-accent focus:outline-none disabled:bg-neutral-100 font-medium hyper-input font-mono cursor-pointer"
                            />
                            <Copy size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-hyper-muted-foreground group-hover:text-hyper-accent transition-colors cursor-pointer" />
                        </div>
                    </CopyOnClick>
                </div>

                {/* Port */}
                <div>
                    <label className="block text-sm font-medium text-hyper-foreground mb-2">
                        Port
                    </label>
                    <CopyOnClick text={allocation.port.toString()}>
                        <div className="relative group">
                            <input
                                type="text"
                                value={allocation.port}
                                readOnly
                                className="appearance-none outline-none w-full min-w-0 p-3 rounded-md text-sm transition-all duration-150 resize-none shadow-none focus:ring-0 h-10 bg-hyper-background text-hyper-foreground border focus:border-2 border-hyper-primary hover:border-hyper-accent focus:outline-none disabled:bg-neutral-100 font-medium hyper-input font-mono cursor-pointer"
                            />
                            <Copy size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-hyper-muted-foreground group-hover:text-hyper-accent transition-colors cursor-pointer" />
                        </div>
                    </CopyOnClick>
                </div>

                {/* Notes */}
                <div>
                    <label className="block text-sm font-medium text-hyper-foreground mb-2">
                        Notes
                    </label>
                    <InputSpinner visible={loading}>
                        <Textarea
                            placeholder="Add notes for this allocation..."
                            defaultValue={allocation.notes || ''}
                            onChange={(e) => setAllocationNotes(e.currentTarget.value)}
                            className="min-h-[80px] resize-y"
                            isLight
                        />
                    </InputSpinner>
                </div>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between pt-4 border-t border-hyper-primary">
                <div className="flex items-center gap-2">
                    {allocation.isDefault ? (
                        <div className="flex items-center gap-2 px-3 py-2 bg-green-500/10 border border-green-500/30 rounded-lg">
                            <span className="text-sm font-medium text-green-500">Primary</span>
                        </div>
                    ) : (
                        <Can action={'allocation.update'}>
                            <Tooltip placement="top" content="Set as primary allocation">
                                <Button
                                    size="small"
                                    onClick={setPrimaryAllocation}
                                    className="flex items-center gap-2"
                                >
                                    Make Primary
                                </Button>
                            </Tooltip>
                        </Can>
                    )}
                </div>

                {!allocation.isDefault && (
                    <Can action={'allocation.delete'}>
                        <DeleteAllocationButton allocation={allocation.id} />
                    </Can>
                )}
            </div>
        </div>
    );
};

export default memo(AllocationRow, isEqual);
