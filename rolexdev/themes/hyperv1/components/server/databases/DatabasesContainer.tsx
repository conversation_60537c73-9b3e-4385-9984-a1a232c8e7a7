import React, { useEffect, useState } from 'react';
import { ServerDatabase } from '@/api/server/databases/getServerDatabases';
import getServerDatabases from '@/api/server/databases/getServerDatabases';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { httpErrorToHuman } from '@/api/http';
import FlashMessageRender from '@/components/FlashMessageRender';
import DatabaseRow from '@rolexdev/themes/hyperv1/components/server/databases/DatabaseRow';
import Spinner from '@/components/elements/Spinner';
import CreateDatabaseButton from '@rolexdev/themes/hyperv1/components/server/databases/CreateDatabaseButton';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import useFlash from '@/plugins/useFlash';
import Fade from '@/components/elements/Fade';
import ServerContentBlock from '@rolexdev/themes/hyperv1/components/elements/ServerContentBlock';
import { useDeepMemoize } from '@/plugins/useDeepMemoize';
import { Database, AlertCircle, Plus } from 'lucide-react';
import { PageTransition, AnimatedCard, AnimatedContainer, AnimatedList } from '@rolexdev/themes/hyperv1/components/elements/animations';

export default () => {
    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const databaseLimit = ServerContext.useStoreState((state) => state.server.data!.featureLimits.databases);

    const { addError, clearFlashes } = useFlash();
    const [loading, setLoading] = useState(true);

    const databases = useDeepMemoize(ServerContext.useStoreState((state) => state.databases.data));
    const setDatabases = ServerContext.useStoreActions((state) => state.databases.setDatabases);

    useEffect(() => {
        setLoading(!databases.length);
        clearFlashes('databases');

        getServerDatabases(uuid)
            .then((databases) => setDatabases(databases))
            .catch((error) => {
                console.error(error);
                addError({ key: 'databases', message: httpErrorToHuman(error) });
            })
            .then(() => setLoading(false));
    }, []);

    return (
        <ServerContentBlock title={'Databases'}>
            <PageTransition>
                <FlashMessageRender byKey={'databases'} className="mb-6" />
                
                {/* Header Section */}
                <AnimatedCard delay={0.1} variant="default" className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                            <Database size={20} className="text-hyper-accent" />
                        </div>
                        <div>
                            <h2 className="text-lg font-semibold text-hyper-primary">Server Databases</h2>
                            <p className="text-sm text-hyper-muted-foreground">
                                Manage your MySQL databases and create new ones to store application data for your server.
                            </p>
                        </div>
                    </div>
                </AnimatedCard>

                {/* Database Limit Warning */}
                {databaseLimit === 0 && (
                    <AnimatedCard delay={0.2} variant="default" className="bg-hyper-glass border border-hyper-accent rounded-lg p-4 mb-6">
                        <div className="flex items-start gap-3">
                            <AlertCircle size={20} className="text-hyper-accent flex-shrink-0 mt-0.5" />
                            <div>
                                <h3 className="text-sm font-semibold text-hyper-primary mb-1">Databases Disabled</h3>
                                <p className="text-sm text-hyper-accent">
                                    Databases cannot be created for this server because the database limit is set to 0.
                                </p>
                            </div>
                        </div>
                    </AnimatedCard>
                )}

                {!databases.length && loading ? (
                    <Spinner size={'large'} centered />
                ) : (
                    <AnimatedCard delay={0.3} variant="default">
                        {/* Databases List */}
                        <AnimatedContainer variant="stagger" staggerChildren={0.1} className="flex flex-wrap gap-6 justify-center lg:justify-start mb-6">
                            {!databases.length ? (
                                databaseLimit > 0 ? (
                                    <div className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-8 text-center w-[342px] mx-auto">
                                        <div className="w-16 h-16 rounded-lg bg-hyper-primary-10 flex items-center justify-center mx-auto mb-4">
                                            <Database size={32} className="text-hyper-accent" />
                                        </div>
                                        <h3 className="text-lg font-semibold text-hyper-primary mb-2">No Databases Yet</h3>
                                        <p className="text-sm text-hyper-accent mb-6">
                                            It looks like there are no databases currently created for this server. Create your first database to get started.
                                        </p>
                                        <Can action={'database.create'}>
                                            {databaseLimit > databases.length && (
                                                <CreateDatabaseButton />
                                            )}
                                        </Can>
                                    </div>
                                ) : null
                            ) : (
                                <>
                                    <AnimatedList variant="scale" staggerDelay={0.05}>
                                        {databases.map((database) => (
                                            <DatabaseRow
                                                key={database.id}
                                                database={database}
                                            />
                                        ))}
                                    </AnimatedList>
                                    
                                    {/* Add New Database Card */}
                                    <Can action={'database.create'}>
                                        {databaseLimit > 0 ? (
                                            databaseLimit > databases.length ? (
                                                <AnimatedCard delay={0.5} variant="hover" className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary border-dashed rounded-lg p-6 w-[342px] transition-all duration-200 hover:border-hyper-accent flex flex-col items-center justify-center text-center">
                                                    <div className="w-16 h-16 rounded-lg bg-hyper-primary-10 flex items-center justify-center mx-auto mb-4">
                                                        <Plus size={32} className="text-hyper-accent" />
                                                    </div>
                                                    <h3 className="text-lg font-semibold text-hyper-primary mb-2">Add New Database</h3>
                                                    <div className="text-xs text-hyper-accent mb-3 bg-hyper-background border border-hyper-primary rounded px-2 py-1">
                                                        {databases.length} of {databaseLimit} databases used
                                                    </div>
                                                    <p className="text-sm text-hyper-accent mb-6">
                                                        Create a MySQL database to store application data and manage content for your server.
                                                    </p>
                                                    <CreateDatabaseButton />
                                                </AnimatedCard>
                                            ) : (
                                                <AnimatedCard delay={0.5} variant="default" className="bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary border-dashed rounded-lg p-6 w-[342px] transition-all duration-200 flex flex-col items-center justify-center text-center opacity-60">
                                                    <div className="w-16 h-16 rounded-lg bg-hyper-primary-10 flex items-center justify-center mx-auto mb-4">
                                                        <AlertCircle size={32} className="text-hyper-accent" />
                                                    </div>
                                                    <h3 className="text-lg font-semibold text-hyper-primary mb-2">Database Limit Reached</h3>
                                                    <div className="text-xs text-hyper-accent mb-3 bg-hyper-background border border-hyper-primary rounded px-2 py-1">
                                                        {databases.length} of {databaseLimit} databases used
                                                    </div>
                                                    <p className="text-sm text-hyper-accent">
                                                        You've reached the maximum of {databaseLimit} database{databaseLimit !== 1 ? 's' : ''} for this server. Delete an old database to create a new one.
                                                    </p>
                                                </AnimatedCard>
                                            )
                                        ) : null}
                                    </Can>
                                </>
                            )}
                        </AnimatedContainer>
                    </AnimatedCard>
                )}
            </PageTransition>
        </ServerContentBlock>
    );
};
