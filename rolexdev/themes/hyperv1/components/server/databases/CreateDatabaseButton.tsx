import React, { useState } from 'react';
import { Form, Formik, FormikHelpers } from 'formik';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import { object, string } from 'yup';
import createServerDatabase from '@/api/server/databases/createServerDatabase';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import { httpErrorToHuman } from '@/api/http';
import FlashMessageRender from '@/components/FlashMessageRender';
import useFlash from '@/plugins/useFlash';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import { Database, Plus } from 'lucide-react';

interface Values {
    databaseName: string;
    connectionsFrom: string;
}

const schema = object().shape({
    databaseName: string()
        .required('A database name must be provided.')
        .min(3, 'Database name must be at least 3 characters.')
        .max(48, 'Database name must not exceed 48 characters.')
        .matches(
            /^[\w\-.]{3,48}$/,
            'Database name should only contain alphanumeric characters, underscores, dashes, and/or periods.'
        ),
    connectionsFrom: string().matches(/^[\w\-/.%:]+$/, 'A valid host address must be provided.'),
});

export default () => {
    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const { addError, clearFlashes } = useFlash();
    const [visible, setVisible] = useState(false);

    const appendDatabase = ServerContext.useStoreActions((actions) => actions.databases.appendDatabase);

    const submit = (values: Values, { setSubmitting }: FormikHelpers<Values>) => {
        clearFlashes('database:create');
        createServerDatabase(uuid, {
            databaseName: values.databaseName,
            connectionsFrom: values.connectionsFrom || '%',
        })
            .then((database) => {
                appendDatabase(database);
                setVisible(false);
            })
            .catch((error) => {
                addError({ key: 'database:create', message: httpErrorToHuman(error) });
                setSubmitting(false);
            });
    };

    return (
        <>
            <Formik
                onSubmit={submit}
                initialValues={{ databaseName: '', connectionsFrom: '' }}
                validationSchema={schema}
            >
                {({ isSubmitting, resetForm }) => (
                    <Dialog
                        open={visible}
                        onClose={() => {
                            resetForm();
                            setVisible(false);
                        }}
                        title="Create new database"
                        preventExternalClose={isSubmitting}
                    >
                        <div className="p-6">
                            <FlashMessageRender byKey={'database:create'} className="mb-6" />
                            <Form className="space-y-4">
                                <Field
                                    type={'string'}
                                    id={'database_name'}
                                    name={'databaseName'}
                                    label={'Database Name'}
                                    description={'A descriptive name for your database instance.'}
                                    light
                                />
                                <Field
                                    type={'string'}
                                    id={'connections_from'}
                                    name={'connectionsFrom'}
                                    label={'Connections From'}
                                    description={'Where connections should be allowed from. Leave blank to allow connections from anywhere.'}
                                    light
                                />
                            </Form>
                        </div>
                        <Dialog.Footer>
                            <Button
                                type={'button'}
                                isSecondary
                                onClick={() => setVisible(false)}
                                disabled={isSubmitting}
                            >
                                Cancel
                            </Button>
                            <Button 
                                type={'submit'} 
                                disabled={isSubmitting}
                                isLoading={isSubmitting}
                                onClick={() => {
                                    const form = document.querySelector('form');
                                    if (form) {
                                        form.requestSubmit();
                                    }
                                }}
                            >
                                Create Database
                            </Button>
                        </Dialog.Footer>
                    </Dialog>
                )}
            </Formik>
            <Button 
                onClick={() => setVisible(true)}
                className="flex items-center gap-2"
            >
                <Plus size={16} />
                New Database
            </Button>
        </>
    );
};
