import React, { useState } from 'react';
import { Form, Formik, FormikHelpers } from 'formik';
import Field from '@rolexdev/themes/hyperv1/components/elements/Field';
import { object, string } from 'yup';
import FlashMessageRender from '@/components/FlashMessageRender';
import { ServerContext } from '@rolexdev/themes/hyperv1/state/server';
import deleteServerDatabase from '@/api/server/databases/deleteServerDatabase';
import { httpErrorToHuman } from '@/api/http';
import RotatePasswordButton from '@rolexdev/themes/hyperv1/components/server/databases/RotatePasswordButton';
import Can from '@rolexdev/themes/hyperv1/components/elements/Can';
import { ServerDatabase } from '@/api/server/databases/getServerDatabases';
import useFlash from '@/plugins/useFlash';
import Button from '@rolexdev/themes/hyperv1/components/elements/Button';
import Label from '@rolexdev/themes/hyperv1/components/elements/Label';
import Input from '@rolexdev/themes/hyperv1/components/elements/Input';
import CopyOnClick from '@/components/elements/CopyOnClick';
import { Dialog } from '@rolexdev/themes/hyperv1/components/elements/dialog';
import { Database, Eye, Trash2, Server, Key, Lock } from 'lucide-react';

interface Props {
    database: ServerDatabase;
    className?: string;
}

export default ({ database, className }: Props) => {
    const uuid = ServerContext.useStoreState((state) => state.server.data!.uuid);
    const { addError, clearFlashes } = useFlash();
    const [visible, setVisible] = useState(false);
    const [connectionVisible, setConnectionVisible] = useState(false);

    const appendDatabase = ServerContext.useStoreActions((actions) => actions.databases.appendDatabase);
    const removeDatabase = ServerContext.useStoreActions((actions) => actions.databases.removeDatabase);

    const jdbcConnectionString = `jdbc:mysql://${database.username}${
        database.password ? `:${encodeURIComponent(database.password)}` : ''
    }@${database.connectionString}/${database.name}`;

    const schema = object().shape({
        confirm: string()
            .required('The database name must be provided.')
            .oneOf([database.name.split('_', 2)[1], database.name], 'The database name must be provided.'),
    });

    const submit = (values: { confirm: string }, { setSubmitting }: FormikHelpers<{ confirm: string }>) => {
        clearFlashes();
        deleteServerDatabase(uuid, database.id)
            .then(() => {
                setVisible(false);
                setTimeout(() => removeDatabase(database.id), 150);
            })
            .catch((error) => {
                console.error(error);
                setSubmitting(false);
                addError({ key: 'database:delete', message: httpErrorToHuman(error) });
            });
    };

    return (
        <>
            <Formik onSubmit={submit} initialValues={{ confirm: '' }} validationSchema={schema} isInitialValid={false}>
                {({ isSubmitting, isValid, resetForm }) => (
                    <Dialog
                        open={visible}
                        onClose={() => {
                            setVisible(false);
                            resetForm();
                        }}
                        title="Confirm database deletion"
                        preventExternalClose={isSubmitting}
                    >
                        <div className="p-6">
                            <FlashMessageRender byKey={'database:delete'} className="mb-6" />
                            <div className="flex items-center gap-3 mb-6">
                                <div className="w-12 h-12 bg-red-500/20 border border-red-500/30 rounded-lg flex items-center justify-center">
                                    <Trash2 size={20} className="text-red-400" />
                                </div>
                                <div>
                                    <p className="text-hyper-primary font-medium mb-1">Delete Database</p>
                                    <p className="text-hyper-accent text-sm">This action cannot be undone</p>
                                </div>
                            </div>
                            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6">
                                <p className="text-red-300 text-sm leading-relaxed">
                                    Deleting a database is a permanent action, it cannot be undone. This will permanently delete
                                    the <strong className="text-red-200">{database.name}</strong> database and remove all associated data.
                                </p>
                            </div>
                            <Form className="space-y-4">
                                <Field
                                    type={'text'}
                                    id={'confirm_name'}
                                    name={'confirm'}
                                    label={'Confirm Database Name'}
                                    description={'Enter the database name to confirm deletion.'}
                                    light
                                />
                            </Form>
                        </div>
                        <Dialog.Footer>
                            <Button 
                                type={'button'} 
                                isSecondary 
                                onClick={() => setVisible(false)}
                                disabled={isSubmitting}
                            >
                                Cancel
                            </Button>
                            <Button 
                                type={'submit'} 
                                color={'red'} 
                                disabled={!isValid || isSubmitting}
                                isLoading={isSubmitting}
                                onClick={() => {
                                    const form = document.querySelector('form');
                                    if (form) {
                                        form.requestSubmit();
                                    }
                                }}
                            >
                                Delete Database
                            </Button>
                        </Dialog.Footer>
                    </Dialog>
                )}
            </Formik>

            <Dialog 
                open={connectionVisible} 
                onClose={() => setConnectionVisible(false)}
                title="Database Connection Details"
            >
                <div className="p-6">
                    <FlashMessageRender byKey={'database-connection-modal'} className="mb-6" />
                    
                    <div className="space-y-4">
                        <div>
                            <Label className="flex items-center gap-2 mb-2">
                                <Server size={16} className="text-hyper-accent" />
                                Endpoint
                            </Label>
                            <CopyOnClick text={database.connectionString}>
                                <Input type={'text'} readOnly value={database.connectionString} isLight />
                            </CopyOnClick>
                        </div>

                        <div>
                            <Label className="flex items-center gap-2 mb-2">
                                <Database size={16} className="text-hyper-accent" />
                                Connections from
                            </Label>
                            <Input type={'text'} readOnly value={database.allowConnectionsFrom} isLight />
                        </div>

                        <div>
                            <Label className="flex items-center gap-2 mb-2">
                                <Key size={16} className="text-hyper-accent" />
                                Username
                            </Label>
                            <CopyOnClick text={database.username}>
                                <Input type={'text'} readOnly value={database.username} isLight />
                            </CopyOnClick>
                        </div>

                        <Can action={'database.view_password'}>
                            <div>
                                <Label className="flex items-center gap-2 mb-2">
                                    <Lock size={16} className="text-hyper-accent" />
                                    Password
                                </Label>
                                <CopyOnClick text={database.password} showInNotification={false}>
                                    <Input type={'text'} readOnly value={database.password} isLight />
                                </CopyOnClick>
                            </div>
                        </Can>

                        <div>
                            <Label className="flex items-center gap-2 mb-2">
                                <Database size={16} className="text-hyper-accent" />
                                JDBC Connection String
                            </Label>
                            <CopyOnClick text={jdbcConnectionString} showInNotification={false}>
                                <Input type={'text'} readOnly value={jdbcConnectionString} isLight />
                            </CopyOnClick>
                        </div>
                    </div>
                </div>
                
                <Dialog.Footer>
                    <Can action={'database.update'}>
                        <RotatePasswordButton databaseId={database.id} onUpdate={appendDatabase} />
                    </Can>
                    <Button isSecondary onClick={() => setConnectionVisible(false)}>
                        Close
                    </Button>
                </Dialog.Footer>
            </Dialog>
            <div className={`bg-hyper-sidebar backdrop-blur-lg border border-hyper-primary rounded-lg p-6 w-[342px] transition-all duration-200 hover:border-hyper-accent hover:scale-[1.02] hover:shadow-lg hover:shadow-hyper-accent/10 ${className || ''}`}>
                {/* Header Section */}
                <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 rounded-lg bg-hyper-primary-10 flex items-center justify-center">
                        <Database size={20} className="text-hyper-accent" />
                    </div>
                    <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold text-hyper-primary">Database</h3>
                        <p className="text-sm text-hyper-accent">MySQL Database</p>
                    </div>
                </div>

                {/* Database Name */}
                <div className="mb-4">
                    <label className="block text-sm font-medium text-hyper-foreground mb-2">Database Name</label>
                    <CopyOnClick text={database.name}>
                        <div className="bg-hyper-background border border-hyper-primary rounded-lg p-3 cursor-pointer hover:border-hyper-accent transition-colors">
                            <p className="text-sm text-hyper-foreground font-mono truncate">{database.name}</p>
                        </div>
                    </CopyOnClick>
                </div>

                {/* Connection Details */}
                <div className="space-y-3 mb-6">
                    <div>
                        <label className="block text-sm font-medium text-hyper-foreground mb-2">Endpoint</label>
                        <CopyOnClick text={database.connectionString}>
                            <div className="bg-hyper-background border border-hyper-primary rounded-lg p-3 cursor-pointer hover:border-hyper-accent transition-colors">
                                <p className="text-sm text-hyper-foreground font-mono truncate">{database.connectionString}</p>
                            </div>
                        </CopyOnClick>
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-hyper-foreground mb-2">Username</label>
                        <CopyOnClick text={database.username}>
                            <div className="bg-hyper-background border border-hyper-primary rounded-lg p-3 cursor-pointer hover:border-hyper-accent transition-colors">
                                <p className="text-sm text-hyper-foreground font-mono truncate">{database.username}</p>
                            </div>
                        </CopyOnClick>
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-hyper-foreground mb-2">Connections From</label>
                        <div className="bg-hyper-background border border-hyper-primary rounded-lg p-3">
                            <p className="text-sm text-hyper-foreground">{database.allowConnectionsFrom}</p>
                        </div>
                    </div>
                </div>
                
                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t border-hyper-primary">
                    <Button 
                        isSecondary 
                        size="small"
                        onClick={() => setConnectionVisible(true)}
                        className="flex items-center gap-2"
                    >
                        <Eye size={16} />
                        Details
                    </Button>
                    
                    <Can action={'database.delete'}>
                            <Button 
                                color={'red'} 
                                size="small"
                                onClick={() => setVisible(true)}
                                className="flex items-center gap-2"
                            >
                                <Trash2 size={16} />
                                Delete
                            </Button>
                    </Can>
                </div>
            </div>
        </>
    );
};
