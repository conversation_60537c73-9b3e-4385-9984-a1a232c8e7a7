import * as React from 'react';
import { useState } from 'react';
import { useStoreState } from 'easy-peasy';
import { ApplicationStore } from '@/state';
import SearchContainer from '@rolexdev/themes/hyperv1/components/dashboard/search/SearchContainer';
import {SidebarTooltip} from '@rolexdev/themes/hyperv1/components/elements/SidebarTooltip';
import tw, { theme } from 'twin.macro';
import styled from 'styled-components/macro';
import http from '@/api/http';
import SpinnerOverlay from '@/components/elements/SpinnerOverlay';
import { useSidebarCollapsed } from '@rolexdev/themes/hyperv1/components/sidebarToggle';
import { PanelLeft } from '@rolexdev/themes/hyperv1/components/icons/panel-left';
import { PanelRight } from '@rolexdev/themes/hyperv1/components/icons/panel-right';
import { Settings } from '@rolexdev/themes/hyperv1/components/icons/settings';
import { AnimateIcon } from './icons-move/icon';
import useSWR from 'swr';
import getServers from '@rolexdev/themes/hyperv1/api/getServers';
import Spinner from '@/components/elements/Spinner';
import { Server } from '@/api/server/getServer';
import { PaginatedResult } from '@/api/http';
import { ChevronDown } from 'lucide-react';
import { useLocation, useHistory } from 'react-router-dom';

interface NavigationBarProps {
    isMobile?: boolean;
    sidebarCollapsed?: boolean;
    onSidebarToggle?: () => void;
    route?: 'dashboard' | 'server';
}

const RightNavigation = styled.div`
    & > a,
    & > button,
    & > .navigation-link {
        ${tw`flex items-center h-full no-underline px-6 cursor-pointer transition-all duration-150`};
    }
`;

const ServerSelector: React.FC<{
    currentServerUuid?: string;
    onNavigate: (server: Server) => void;
}> = ({ currentServerUuid, onNavigate }) => {
    const { data, error } = useSWR<PaginatedResult<Server>>(
        ['/api/client/servers', 1, 50],
        () => getServers({ page: 1, perPage: 50 })
    );
    const [open, setOpen] = React.useState(false);

    const currentServer = data?.items.find(s => s.uuid === currentServerUuid);

    if (!data && !error) return <div className="ml-4"><Spinner size="small" /></div>;
    if (error || !data?.items?.length) return null;

    return (
        <div className="relative ml-4" tabIndex={0} onBlur={() => setTimeout(() => setOpen(false), 100)}>
            <button
                className="flex items-center justify-between px-3 py-1 rounded-lg bg-hyper-card border border-hyper-accent text-hyper-primary w-[200px] hover:bg-hyper-primary-10 transition"
                onClick={() => setOpen(v => !v)}
                aria-haspopup="listbox"
                aria-expanded={open}
                type="button"
            >
                <span className="truncate mr-2">
                    {currentServer ? currentServer.name : 'Select Server'}
                </span>
                <ChevronDown size={18} className="text-hyper-accent" />
            </button>
            {open && (
                <div className="z-[9999] absolute left-0 mt-2 w-[200px] bg-hyper-sidebar backdrop-blur-lg border border-hyper-accent rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {data.items.map(server => (
                        <div
                            key={server.uuid}
                            className={`px-3 py-2 cursor-pointer bg-hyper-sidebar backdrop-blur-lg hover:bg-hyper-primary-10 truncate text-hyper-primary ${
                                currentServerUuid === server.uuid ? 'bg-hyper-primary-30 font-bold' : ''
                            }`}
                            onMouseDown={e => {
                                e.preventDefault();
                                onNavigate(server);
                                setOpen(false);
                            }}
                        >
                            {server.name}
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

const NavigationBar: React.FC<NavigationBarProps> = ({
    isMobile = false,
    sidebarCollapsed,
    onSidebarToggle,
    route = 'dashboard',
}) => {
    const rootAdmin = useStoreState((state: ApplicationStore) => state.user.data!.rootAdmin);
    const [isLoggingOut, setIsLoggingOut] = useState(false);
    const [tooltip, setTooltip] = useState<{ text: string; rect: DOMRect } | null>(null);
    const [isCollapsed, setIsCollapsed] = useSidebarCollapsed();

    const [selectedServer, setSelectedServer] = React.useState<Server | undefined>(undefined);
    const location = useLocation();
    const history = useHistory();
    const serverUuid = React.useMemo(() => {
        const match = location.pathname.match(/\/server\/([a-zA-Z0-9-]+)/);
        return match ? match[1] : undefined;
    }, [location.pathname]);

    const handleToggleSidebar = () => {
        setIsCollapsed(!isCollapsed);
    };

    const isTouchDevice = () => 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    // Tooltip handlers
    const handleTooltipShow = (e: React.MouseEvent, text: string) => {
        if (isTouchDevice()) return; // Prevent tooltip on touch devices
        const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
        setTooltip({ text, rect });
    };
    const handleTooltipHide = () => setTooltip(null);
    

    return (
        <>
            {tooltip && <SidebarTooltip text={tooltip.text} rect={tooltip.rect} placement='bottom'/>}
            <div className={'sticky top-0 z-[210] w-full bg-hyper-sidebar backdrop-blur-lg border-b border-hyper-primary overflow-visible'}>
                <SpinnerOverlay visible={isLoggingOut} />
                <div className={'w-full flex items-center h-14 justify-between px-3'}>
                    {/* Mobile: sidebar button + logo */}
                    {isMobile ? (
                        <div className="flex items-center">
                            <AnimateIcon animateOnHover>
                                <button
                                    onClick={onSidebarToggle}
                                    className="p-2 rounded-md hover:bg-hyper-primary-10 transition-colors text-hyper-secondary"
                                    aria-label="Toggle Sidebar"
                                    onMouseEnter={e => handleTooltipShow(e, 'Toggle Sidebar')}
                                    onMouseLeave={handleTooltipHide}
                                >
                                    {sidebarCollapsed ? <PanelRight size={20} /> : <PanelLeft size={20} />}
                                </button>
                            </AnimateIcon>
                            <a href="/" aria-label="Go to Home">
                                <img
                                    src="/logo/large.png"
                                    alt="Logo"
                                    className="h-6 ml-3"
                                />
                            </a>
                        </div>
                    ) : (
                        <div id={'sidebarbutton'} className={'flex items-center'}>
                            <AnimateIcon animateOnHover>
                                <button
                                    onClick={handleToggleSidebar}
                                    className="p-2 rounded-md hover:bg-hyper-primary-10 transition-colors text-hyper-secondary"
                                    aria-label="Toggle Sidebar"
                                    onMouseEnter={e => handleTooltipShow(e, 'Toggle Sidebar')}
                                    onMouseLeave={handleTooltipHide}
                                >
                                    {isCollapsed ? <PanelRight size={20} /> : <PanelLeft size={20} />}
                                </button>
                            </AnimateIcon>
                            {route === 'server' && (
                                <ServerSelector
                                    currentServerUuid={serverUuid}
                                    onNavigate={server => history.push(`/server/${server.uuid}`)}
                                />
                            )}
                        </div>
                    )}
                    <RightNavigation className={'flex h-full items-center justify-center text-hyper-forground'}>
                        <SearchContainer 
                            onSearchIconMouseEnter={e => handleTooltipShow(e, 'Search')}
                            onSearchIconMouseLeave={handleTooltipHide}
                        />
                        {rootAdmin && (
                            <a
                                href={'/admin'}
                                rel={'noreferrer'}
                                onMouseEnter={e => handleTooltipShow(e, 'Admin Panel')}
                                onMouseLeave={handleTooltipHide}
                            >
                                <Settings size={20} animateOnHover/>
                            </a>
                        )}
                    </RightNavigation>
                </div>
            </div>
        </>
    );
};
export default NavigationBar;