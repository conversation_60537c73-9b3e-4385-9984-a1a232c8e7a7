// Utility functions for color conversion and CSS variable extraction

/**
 * Gets the value of a CSS custom property (CSS variable)
 */
export const getCSSVariableValue = (variableName: string): string => {
  const value = getComputedStyle(document.documentElement)
    .getPropertyValue(variableName)
    .trim();
  
  return value;
};

/**
 * Converts a hex color to RGB array normalized to 0-1 range
 */
export const hexToRgbArray = (hex: string): [number, number, number] => {
  try {
    // Remove # if present and clean the string
    const cleanHex = hex.replace('#', '').trim();
    
    // Validate hex format
    if (!/^[0-9A-F]{6}$/i.test(cleanHex)) {
      console.warn(`Invalid hex color format: ${hex}, using fallback`);
      return hexToRgbArray('#ff0000');
    }
    
    // Parse RGB values
    const r = parseInt(cleanHex.substr(0, 2), 16) / 255;
    const g = parseInt(cleanHex.substr(2, 2), 16) / 255;
    const b = parseInt(cleanHex.substr(4, 2), 16) / 255;
    
    return [r, g, b];
  } catch (error) {
    console.warn(`Error parsing hex color ${hex}:`, error);
    return [0.87, 0.19, 0.31]; // Default primary color RGB values
  }
};

/**
 * Converts an RGBA string to RGB array normalized to 0-1 range
 */
export const rgbaToRgbArray = (rgba: string): [number, number, number] => {
  const match = rgba.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
  if (match) {
    const r = parseInt(match[1], 10) / 255;
    const g = parseInt(match[2], 10) / 255;
    const b = parseInt(match[3], 10) / 255;
    return [r, g, b];
  }
  
  // Fallback to default primary color if parsing fails
  return hexToRgbArray('#ff0000');
};

/**
 * Gets CSS variable value and converts it to RGB array for use in components
 */
export const getCSSVariableAsRgbArray = (variableName: string, fallbackHex: string = '#ff0000'): [number, number, number] => {
  try {
    const value = getCSSVariableValue(variableName);
    
    if (!value || value === '') {
      return hexToRgbArray(fallbackHex);
    }
    
    if (value.startsWith('#')) {
      return hexToRgbArray(value);
    }
    
    if (value.startsWith('rgb')) {
      return rgbaToRgbArray(value);
    }
    
    // If it's neither hex nor rgb, try to parse as hex anyway (might be missing #)
    const hexValue = value.startsWith('#') ? value : `#${value}`;
    if (/^#[0-9A-F]{6}$/i.test(hexValue)) {
      return hexToRgbArray(hexValue);
    }
    
    console.warn(`Unrecognized color format for ${variableName}: ${value}, using fallback`);
    return hexToRgbArray(fallbackHex);
  } catch (error) {
    console.warn(`Failed to parse CSS variable ${variableName}, using fallback:`, error);
    return hexToRgbArray(fallbackHex);
  }
};

/**
 * Converts RGB to YIQ color space (same as the shader)
 */
const rgbToYiq = (r: number, g: number, b: number): [number, number, number] => {
  // Same matrix as in the shader
  const y = 0.299 * r + 0.587 * g + 0.114 * b;
  const i = 0.596 * r - 0.274 * g - 0.322 * b;
  const q = 0.211 * r - 0.523 * g + 0.312 * b;
  return [y, i, q];
};

/**
 * Converts YIQ back to RGB (same as the shader)
 */
const yiqToRgb = (y: number, i: number, q: number): [number, number, number] => {
  // Same matrix as in the shader
  const r = y + 0.956 * i + 0.621 * q;
  const g = y - 0.272 * i - 0.647 * q;
  const b = y - 1.106 * i + 1.703 * q;
  return [Math.max(0, Math.min(1, r)), Math.max(0, Math.min(1, g)), Math.max(0, Math.min(1, b))];
};

/**
 * Applies hue shift in YIQ space (same as the shader)
 */
const applyHueShiftYiq = (rgb: [number, number, number], degreesShift: number): [number, number, number] => {
  const [r, g, b] = rgb;
  const [y, i, q] = rgbToYiq(r, g, b);
  
  const rad = (degreesShift * Math.PI) / 180;
  const cosH = Math.cos(rad);
  const sinH = Math.sin(rad);
  
  const iShifted = i * cosH - q * sinH;
  const qShifted = i * sinH + q * cosH;
  
  return yiqToRgb(y, iShifted, qShifted);
};

/**
 * Converts RGB array to hue shift value for shader effects
 * Uses a direct hue mapping approach for better accuracy
 */
export const rgbToHueShift = (targetRgb: [number, number, number]): number => {
  try {
    // For red #FF0000 [1, 0, 0], we know empirically that we need around 120 degrees
    // For the default color #df3050, we use 0 degrees (no shift)
    
    const targetHex = `#${Math.round(targetRgb[0] * 255).toString(16).padStart(2, '0')}${Math.round(targetRgb[1] * 255).toString(16).padStart(2, '0')}${Math.round(targetRgb[2] * 255).toString(16).padStart(2, '0')}`;
    
    // Direct color mappings based on visual testing
    // The shader's base color at 0 degrees is violet/purple
    const colorMappings: Record<string, number> = {
      '#ff0000': 90,   // Red (shift from violet base)
      '#00ff00': 210,  // Green  
      '#0000ff': 0,    // Blue/Violet (natural shader color)
      '#ffff00': 150,  // Yellow
      '#ff00ff': 300,  // Magenta
      '#00ffff': 270,  // Cyan
    };
    
    // Check for exact matches first
    const lowerTargetHex = targetHex.toLowerCase();
    if (colorMappings[lowerTargetHex] !== undefined) {
      console.log(`Direct color mapping for ${targetHex}: ${colorMappings[lowerTargetHex]} degrees`);
      return colorMappings[lowerTargetHex];
    }
    
    // Calculate hue-based shift for other colors
    const targetHue = rgbToHue(targetRgb);
    
    // The shader's natural base appears to be violet/blue (around 270 degrees)
    const baseHue = 270;
    
    // Calculate the shift needed
    let hueShift = targetHue - baseHue;
    
    // Normalize to 0-360 range
    while (hueShift < 0) hueShift += 360;
    while (hueShift >= 360) hueShift -= 360;
    
    console.log('Hue calculation result:', {
      targetRgb,
      targetHex,
      targetHue,
      baseHue,
      calculatedShift: hueShift
    });
    
    return hueShift;
  } catch (error) {
    console.warn('Error in hue calculation:', error);
    return 260; // Default to roughly no shift
  }
};

/**
 * Convert RGB to HSV hue (0-360 degrees)
 */
const rgbToHue = (rgb: [number, number, number]): number => {
  const [r, g, b] = rgb;
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const delta = max - min;
  
  if (delta === 0) return 0;
  
  let hue = 0;
  if (max === r) {
    hue = ((g - b) / delta) % 6;
  } else if (max === g) {
    hue = (b - r) / delta + 2;
  } else {
    hue = (r - g) / delta + 4;
  }
  
  hue = Math.round(hue * 60);
  if (hue < 0) hue += 360;
  
  return hue;
};