'use client';

import React, { useRef, useEffect, useCallback } from 'react';

type Star = {
  x: number;
  y: number;
  z: number;
  prevX: number;
  prevY: number;
};

type StarsCanvasProps = {
  count?: number;
  speed?: number;
  starColor?: string;
  className?: string;
  mouseEffect?: boolean;
  stop?: boolean;
};

export default function StarsCanvas({
  count = 800,
  speed = 0.3,
  starColor = '#ffffff',
  className = '',
  mouseEffect = true,
  stop = false,
}: StarsCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const starsRef = useRef<Star[]>([]);
  const mouseRef = useRef({ x: 0, y: 0 });

  const generateStars = useCallback((width: number, height: number): Star[] => {
    const stars: Star[] = [];
    for (let i = 0; i < count; i++) {
      const star: Star = {
        x: Math.random() * width - width / 2,
        y: Math.random() * height - height / 2,
        z: Math.random() * 1000 + 1,
        prevX: 0,
        prevY: 0,
      };
      star.prevX = star.x;
      star.prevY = star.y;
      stars.push(star);
    }
    return stars;
  }, [count]);

  const animate = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const { width, height } = canvas;
    const centerX = width / 2;
    const centerY = height / 2;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    ctx.fillStyle = starColor;

    // Mouse effect offset
    const mouseOffsetX = mouseEffect ? (mouseRef.current.x - centerX) * 0.0002 : 0;
    const mouseOffsetY = mouseEffect ? (mouseRef.current.y - centerY) * 0.0002 : 0;

    starsRef.current.forEach((star) => {
      star.prevX = star.x;
      star.prevY = star.y;

      // Move star towards camera
      star.z -= speed;

      // Reset star when it goes behind camera
      if (star.z <= 0) {
        star.x = Math.random() * width - width / 2;
        star.y = Math.random() * height - height / 2;
        star.z = 1000;
        star.prevX = star.x;
        star.prevY = star.y;
      }

      // Calculate projected position
      const x = (star.x / star.z) * 1000 + centerX + mouseOffsetX * star.z;
      const y = (star.y / star.z) * 1000 + centerY + mouseOffsetY * star.z;
      const prevX = (star.prevX / (star.z + speed)) * 1000 + centerX + mouseOffsetX * (star.z + speed);
      const prevY = (star.prevY / (star.z + speed)) * 1000 + centerY + mouseOffsetY * (star.z + speed);

      // Only draw stars within canvas bounds
      if (x >= 0 && x <= width && y >= 0 && y <= height) {
        const size = Math.max(0, (1 - star.z / 1000) * 3);
        const opacity = Math.max(0, 1 - star.z / 1000);

        // Draw star trail for motion effect
        if (speed > 0.1) {
          ctx.beginPath();
          ctx.strokeStyle = `${starColor}${Math.floor(opacity * 50).toString(16).padStart(2, '0')}`;
          ctx.lineWidth = Math.max(0.3, size * 0.3);
          ctx.moveTo(prevX, prevY);
          ctx.lineTo(x, y);
          ctx.stroke();
        }

        // Draw star
        ctx.globalAlpha = opacity;
        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fill();
        ctx.globalAlpha = 1;
      }
    });

    animationRef.current = requestAnimationFrame(animate);
  }, [speed, starColor, mouseEffect]);

  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!mouseEffect) return;
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;
    
    mouseRef.current = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
  }, [mouseEffect]);

  const drawOnce = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    const { width, height } = canvas;
    const centerX = width / 2;
    const centerY = height / 2;
    ctx.clearRect(0, 0, width, height);
    ctx.fillStyle = starColor;
    const mouseOffsetX = mouseEffect ? (mouseRef.current.x - centerX) * 0.0002 : 0;
    const mouseOffsetY = mouseEffect ? (mouseRef.current.y - centerY) * 0.0002 : 0;
    starsRef.current.forEach((star) => {
      // Calculate projected position
      const x = (star.x / star.z) * 1000 + centerX + mouseOffsetX * star.z;
      const y = (star.y / star.z) * 1000 + centerY + mouseOffsetY * star.z;
      if (x >= 0 && x <= width && y >= 0 && y <= height) {
        const size = Math.max(0, (1 - star.z / 1000) * 3);
        const opacity = Math.max(0, 1 - star.z / 1000);
        ctx.globalAlpha = opacity;
        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fill();
        ctx.globalAlpha = 1;
      }
    });
  }, [starColor, mouseEffect]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const resizeCanvas = () => {
      const parent = canvas.parentElement;
      if (!parent) return;

      // Get the full viewport dimensions or parent dimensions
      const rect = parent.getBoundingClientRect();
      const { clientWidth, clientHeight } = parent;
      
      // Use the larger of client dimensions or viewport to ensure full coverage
      const width = Math.max(clientWidth, window.innerWidth);
      const height = Math.max(clientHeight, window.innerHeight);
      
      canvas.width = width;
      canvas.height = height;
      canvas.style.width = `${width}px`;
      canvas.style.height = `${height}px`;

      // Regenerate stars for new dimensions
      starsRef.current = generateStars(width, height);
      if (stop) drawOnce();
    };

    // Use ResizeObserver for better resize detection
    let resizeObserver: ResizeObserver | null = null;
    if (typeof ResizeObserver !== 'undefined') {
      resizeObserver = new ResizeObserver(() => {
        requestAnimationFrame(resizeCanvas);
      });
      resizeObserver.observe(canvas.parentElement!);
    }

    window.addEventListener('resize', resizeCanvas);
    resizeCanvas();

    // Start animation
    if (!stop) {
      animationRef.current = requestAnimationFrame(animate);
    } else {
      drawOnce();
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      window.removeEventListener('resize', resizeCanvas);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [generateStars, animate, stop, drawOnce]);

  return (
    <canvas
      ref={canvasRef}
      className={`block ${className}`}
      onMouseMove={handleMouseMove}
      style={{ 
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%', 
        height: '100%',
        display: 'block',
        margin: 0,
        padding: 0,
      }}
    />
  );
}
