'use client';

import React from 'react';
import StarsCanvas from './StarsCanvas';

type StarsBackgroundCanvasProps = {
  children?: React.ReactNode;
  count?: number;
  speed?: number;
  starColor?: string;
  mouseEffect?: boolean;
  className?: string;
};

export default function StarsBackgroundCanvas({
  children,
  count = 800,
  speed = 0.3,
  starColor = '#ffffff',
  mouseEffect = true,
  className = '',
}: StarsBackgroundCanvasProps) {
  return (
    <div 
      className={`relative w-full h-full overflow-hidden ${className}`}
      style={{ minHeight: '100vh' }}
    >
      <StarsCanvas
        count={count}
        speed={speed}
        starColor={starColor}
        mouseEffect={mouseEffect}
      />
      {children && (
        <div className="relative z-10 w-full h-full">
          {children}
        </div>
      )}
    </div>
  );
}
