{"name": "pterodactyl-panel", "engines": {"node": ">=14"}, "dependencies": {"@dicebear/collection": "^9.2.3", "@dicebear/core": "^9.2.3", "@floating-ui/react-dom-interactions": "^0.6.6", "@fortawesome/fontawesome-svg-core": "^1.2.32", "@fortawesome/free-solid-svg-icons": "^5.15.1", "@fortawesome/react-fontawesome": "^0.1.11", "@headlessui/react": "^1.6.4", "@heroicons/react": "^1.0.6", "@hot-loader/react-dom": "^16.14.0", "@preact/signals-react": "^1.2.1", "@tailwindcss/forms": "^0.5.2", "@tailwindcss/line-clamp": "^0.4.0", "axios": "^0.27.2", "boring-avatars": "^1.7.0", "chart.js": "^3.8.0", "classnames": "^2.3.1", "clsx": "^2.1.1", "cobe": "^0.6.4", "codemirror": "^5.57.0", "copy-to-clipboard": "^3.3.1", "date-fns": "^2.28.0", "debounce": "^1.2.0", "deepmerge-ts": "^4.2.1", "easy-peasy": "^4.0.1", "events": "^3.0.0", "formik": "^2.2.6", "framer-motion": "6.3.10", "i18next": "^21.8.9", "i18next-http-backend": "^1.4.1", "i18next-multiload-backend-adapter": "^1.0.0", "lucide-react": "^0.525.0", "ogl": "^1.0.11", "qrcode.react": "^1.0.1", "react": "^16.14.0", "react-chartjs-2": "^4.2.0", "react-dom": "npm:@hot-loader/react-dom", "react-fast-compare": "^3.2.0", "react-hot-loader": "^4.12.21", "react-i18next": "^11.2.1", "react-router-dom": "^5.1.2", "react-transition-group": "^4.4.1", "reaptcha": "^1.7.2", "sockette": "^2.0.6", "styled-components": "^5.2.1", "styled-components-breakpoint": "^3.0.0-preview.20", "swr": "^0.2.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.0.24", "use-fit-text": "^2.4.0", "uuid": "^8.3.2", "xterm": "^4.19.0", "xterm-addon-fit": "^0.5.0", "xterm-addon-search": "^0.9.0", "xterm-addon-search-bar": "^0.2.0", "xterm-addon-web-links": "^0.6.0", "yup": "^0.29.1"}, "devDependencies": {"@babel/core": "^7.12.1", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-proposal-optional-chaining": "^7.12.1", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.18.2", "@babel/plugin-transform-react-jsx": "^7.12.1", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/preset-env": "^7.12.1", "@babel/preset-react": "^7.12.1", "@babel/preset-typescript": "^7.12.1", "@babel/runtime": "^7.12.1", "@testing-library/dom": "^8.14.0", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "12.1.5", "@testing-library/user-event": "^14.2.1", "@types/codemirror": "^0.0.98", "@types/debounce": "^1.2.0", "@types/events": "^3.0.0", "@types/jest": "^28.1.3", "@types/node": "^14.11.10", "@types/qrcode.react": "^1.0.1", "@types/react": "^16.14.0", "@types/react-copy-to-clipboard": "^4.3.0", "@types/react-dom": "^16.9.16", "@types/react-redux": "^7.1.1", "@types/react-router": "^5.1.3", "@types/react-router-dom": "^5.1.3", "@types/react-transition-group": "^4.4.0", "@types/styled-components": "^5.1.7", "@types/uuid": "^3.4.5", "@types/webpack-env": "^1.15.2", "@types/yup": "^0.29.3", "@typescript-eslint/eslint-plugin": "^5.29.0", "@typescript-eslint/parser": "^5.29.0", "autoprefixer": "^10.4.7", "babel-jest": "^28.1.1", "babel-loader": "^8.2.5", "babel-plugin-styled-components": "^2.0.7", "cross-env": "^7.0.2", "css-loader": "^5.2.7", "eslint": "^8.18.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jest-dom": "^4.0.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "fork-ts-checker-webpack-plugin": "^6.2.10", "identity-obj-proxy": "^3.0.0", "jest": "^28.1.1", "postcss": "^8.4.14", "postcss-import": "^14.1.0", "postcss-loader": "^4.0.0", "postcss-nesting": "^10.1.8", "postcss-preset-env": "^7.7.1", "prettier": "^2.7.1", "redux-devtools-extension": "^2.13.8", "source-map-loader": "^1.1.3", "style-loader": "^2.0.0", "svg-url-loader": "^7.1.1", "terser-webpack-plugin": "^4.2.3", "ts-essentials": "^9.1.2", "ts-jest": "^28.0.5", "twin.macro": "^2.8.2", "typescript": "^4.7.3", "webpack": "^4.43.0", "webpack-assets-manifest": "^3.1.1", "webpack-bundle-analyzer": "^3.8.0", "webpack-cli": "^3.3.12", "webpack-dev-server": "^3.11.0", "yarn-deduplicate": "^1.1.1"}, "scripts": {"clean": "cd public/assets && find . \\( -name \"*.js\" -o -name \"*.map\" \\) -type f -delete", "test": "jest", "lint": "eslint ./resources/scripts/**/*.{ts,tsx} --ext .ts,.tsx", "watch": "cross-env NODE_ENV=development NODE_OPTIONS=--openssl-legacy-provider ./node_modules/.bin/webpack --watch --progress", "build": "cross-env NODE_ENV=development NODE_OPTIONS=--openssl-legacy-provider ./node_modules/.bin/webpack --progress", "build:production": "yarn run clean && cross-env NODE_ENV=production NODE_OPTIONS=--openssl-legacy-provider ./node_modules/.bin/webpack --mode production", "serve": "yarn run clean && cross-env WEBPACK_PUBLIC_PATH=/webpack@hmr/ NODE_ENV=development webpack-dev-server --host 0.0.0.0 --port 8080 --public https://pterodactyl.test --hot"}, "browserslist": ["> 0.5%", "last 2 versions", "firefox esr", "not dead"], "babelMacros": {"twin": {"preset": "styled-components"}, "styledComponents": {"pure": true, "displayName": true, "fileName": true}}}