<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('hyperv1_theme_settings', function (Blueprint $table) {
            // Add color fields for all CSS variables (nullable with defaults handled in model)
            $table->string('primary_hover_color', 25)->nullable()->after('primary_color');
            $table->string('secondary_color', 25)->nullable()->after('primary_hover_color');
            $table->string('accent_color', 25)->nullable()->after('secondary_color');
            $table->string('card_color', 25)->nullable()->after('background_color');
            $table->string('muted_color', 25)->nullable()->after('card_color');
            $table->string('destructive_color', 25)->nullable()->after('muted_color');
            $table->string('sidebar_color', 25)->nullable()->after('destructive_color');
            $table->string('glass_color', 25)->nullable()->after('sidebar_color');
            $table->string('tooltip_color', 25)->nullable()->after('glass_color');
            $table->string('text_primary_color', 25)->nullable()->after('tooltip_color');
            $table->string('text_secondary_color', 25)->nullable()->after('text_primary_color');
            $table->string('text_foreground_color', 25)->nullable()->after('text_secondary_color');
            $table->string('text_muted_color', 25)->nullable()->after('text_foreground_color');
            $table->string('text_destructive_color', 25)->nullable()->after('text_muted_color');
            $table->string('destructive_text_color', 25)->nullable()->after('text_destructive_color');
        });
    }
/**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('hyperv1_theme_settings', function (Blueprint $table) {
            $table->dropColumn([
                'primary_hover_color',
                'secondary_color',
                'accent_color',
                'card_color',
                'muted_color',
                'destructive_color',
                'sidebar_color',
                'glass_color',
                'tooltip_color',
                'text_primary_color',
                'text_secondary_color',
                'text_foreground_color',
                'text_muted_color',
                'text_destructive_color',
                'destructive_text_color',
            ]);
        });
    }
};
