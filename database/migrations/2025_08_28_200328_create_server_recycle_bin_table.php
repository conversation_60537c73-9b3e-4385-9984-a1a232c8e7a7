<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('server_recycle_bin', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('server_id');
            $table->string('name', 255); // Original file name
            $table->text('original_path'); // Original full path where file was deleted from
            $table->text('original_directory'); // Original directory for restoration
            $table->boolean('is_file')->default(true); // true = file, false = directory
            $table->bigInteger('size')->nullable(); // File size in bytes (for files only)
            $table->timestamp('modified_at')->nullable(); // Original modification date
            $table->timestamp('deleted_at'); // When moved to recycle bin
            $table->timestamp('expires_at'); // When it will be permanently deleted
            $table->string('mimetype', 255)->nullable(); // MIME type
            $table->string('extension', 50)->nullable(); // File extension
            $table->text('recycle_bin_path'); // Current location in recycle bin storage
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('server_id')->references('id')->on('servers')->onDelete('cascade');
            
            // Indexes for performance
            $table->index(['server_id', 'deleted_at']);
            $table->index(['server_id', 'expires_at']);
            $table->index('expires_at'); // For cleanup command
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('server_recycle_bin');
    }
};
