<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hyperv1_theme_settings', function (Blueprint $table) {
            $table->id();
            
            // Logo Settings
            $table->string('large_logo_url')->nullable();
            $table->integer('large_logo_width')->default(200);
            $table->integer('large_logo_height')->default(60);
            $table->boolean('large_logo_enabled')->default(true);
            
            $table->string('small_logo_url')->nullable();
            $table->integer('small_logo_width')->default(40);
            $table->integer('small_logo_height')->default(40);
            $table->boolean('small_logo_enabled')->default(true);
            
            // External Links
            $table->string('discord_url')->nullable();
            $table->string('knowledge_base_url')->nullable();
            $table->string('support_url')->nullable();
            $table->string('billing_url')->nullable();
            
            // Theme Color Settings
            $table->string('primary_color')->default('#df3050');
            $table->string('background_color')->default('#0C0A09');
            $table->boolean('dark_mode')->default(true);
            $table->boolean('sidebar_collapsed')->default(false);
            $table->boolean('animations')->default(true);
            $table->text('custom_css')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hyperv1_theme_settings');
    }
};
