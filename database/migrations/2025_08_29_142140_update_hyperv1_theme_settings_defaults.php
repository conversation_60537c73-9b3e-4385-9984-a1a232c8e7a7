<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing records to use new default logo URLs and dimensions
        DB::table('hyperv1_theme_settings')->update([
            'large_logo_url' => '/logo/large.png',
            'large_logo_width' => 120,
            'large_logo_height' => 24,
            'small_logo_url' => '/logo/small.png',
            'small_logo_width' => 24,
            'small_logo_height' => 24,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert to previous default values
        DB::table('hyperv1_theme_settings')->update([
            'large_logo_url' => null,
            'large_logo_width' => 200,
            'large_logo_height' => 60,
            'small_logo_url' => null,
            'small_logo_width' => 40,
            'small_logo_height' => 40,
        ]);
    }
};
